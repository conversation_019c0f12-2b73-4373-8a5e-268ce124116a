# RICI Pager Alert Group Audit Implementation

This document describes the implementation of audit functionality for the `updateAlertGroup` method in the RICI Pager system.

## Overview

The implementation adds comprehensive audit logging for operations that update the association between persons and alert groups in the RICI pager system. When a person is assigned to or removed from an alert group, this action is now tracked in the audit system.

## Backend Implementation

### 1. Controller Changes
- **File**: `cgdis-portal-webapp/src/main/java/lu/fujitsu/ts/cgdis/portal/webapp/controllers/rici/RiciPagerController.java`
- **Change**: Added `@AuditLog` annotation to the `updateAlertGroup` method
- **Audit Type**: `RICI_PAGER_ALERT_GROUP`
- **Action Type**: `UPDATE`

### 2. Domain Model
- **File**: `cgdis-portal-core/src/main/java/lu/fujitsu/ts/cgdis/portal/core/domain/audit/AuditRiciPagerAlertGroup.java`
- **Purpose**: Domain model for audit records
- **Fields**: Person info, alert group info, RIC schema/range info, pager info, action details

### 3. Database Layer
- **SQL Script**: `database-model/src/scripts/versions/rici/24-rici-pager-alert-group-audit.sql`
- **DBO**: `cgdis-portal-db-connector/src/main/java/lu/fujitsu/ts/cgdis/portal/connector/portaldb/dbos/audit/AuditRiciPagerAlertGroupDbo.java`
- **Repository**: `cgdis-portal-db-connector/src/main/java/lu/fujitsu/ts/cgdis/portal/connector/portaldb/repositories/audit/IAuditRiciPagerAlertGroupRepository.java`
- **Service**: `cgdis-portal-db-connector/src/main/java/lu/fujitsu/ts/cgdis/portal/connector/portaldb/services/audit/rici/AuditRiciPagerAlertGroupRepositoryService.java`

### 4. Audit Aspect
- **File**: `cgdis-portal-webapp/src/main/java/lu/fujitsu/ts/cgdis/portal/webapp/aop/audit/aspect/rici/RiciPagerAlertGroupUpdateAuditAspect.java`
- **Purpose**: Handles the audit logic using AOP
- **Functionality**: Captures person, alert group, and pager information before and after the operation

### 5. REST Endpoint
- **File**: `cgdis-portal-webapp/src/main/java/lu/fujitsu/ts/cgdis/portal/webapp/controllers/audit/AuditController.java`
- **Endpoint**: `/audits/rici/pager-alert-groups`
- **Purpose**: Provides REST API for querying audit records

### 6. Enum Updates
- **File**: `cgdis-portal-core/src/main/java/lu/fujitsu/ts/cgdis/portal/core/domain/audit/AuditType.java`
- **Addition**: `RICI_PAGER_ALERT_GROUP` enum value

## Frontend Implementation

### 1. Model
- **File**: `cgdis-portal-webapp/src/main/webapp/app/model/audit/audit-rici-pager-alert-group.model.ts`
- **Purpose**: TypeScript model for audit records

### 2. Service
- **File**: `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/pager-alert-group/audit-management-list-rici-pager-alert-group.service.ts`
- **Purpose**: Data service for audit operations

### 3. List Component
- **Files**: 
  - `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/pager-alert-group/audit-management-list-rici-pager-alert-group.component.ts`
  - `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/pager-alert-group/audit-management-list-rici-pager-alert-group.component.html`
- **Purpose**: Displays audit records in a datatable with filtering capabilities

### 4. Detail Component
- **Files**:
  - `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/detail/pager-alert-group/audit-management-rici-pager-alert-group-detail.component.ts`
  - `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/detail/pager-alert-group/audit-management-rici-pager-alert-group-detail.component.html`
- **Purpose**: Shows detailed view of individual audit records

### 5. Main Audit Component Updates
- **Files**:
  - `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/audit-rici.component.ts`
  - `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/audit-rici.component.html`
- **Changes**: Added new panel for pager alert group audits

### 6. Routing Updates
- **File**: `cgdis-portal-webapp/src/main/webapp/app/audit-management/rici/audit-rici.routing.ts`
- **Addition**: Route for `pager-alert-group` audit type

### 7. Enum Updates
- **File**: `cgdis-portal-webapp/src/main/webapp/app/model/audit/audit.enum.ts`
- **Addition**: `RICI_PAGER_ALERT_GROUP` enum value

### 8. Service Mapping
- **File**: `cgdis-portal-webapp/src/main/webapp/app/audit-management/audit-management.service.ts`
- **Addition**: Mapping for the new audit type with UPDATE action

## Access URL

The new audit panel can be accessed at:
```
http://localhost:9000/#/audit-management/rici
```

The panel will appear as "RICI Pager Alert Group Audit" in the accordion-style interface.

## Features

### Audit Information Captured
- **Person Information**: Name, CGDIS registration number, TECID
- **Alert Group Information**: Name, description, TECID
- **RIC Schema Information**: Alias, TECID
- **RIC Range Information**: Name, TECID
- **Pager Information**: Pager ID, serial number, TECID
- **Action Information**: Whether assigned (true) or removed (false)
- **Standard Audit Fields**: Action type, datetime, performing user

### Frontend Features
- **Filterable Datatable**: Filter by person name, alert group, schema, range, pager
- **Detailed View**: Modal popup with complete audit information
- **Action Indicator**: Visual badge showing assigned/removed status
- **Responsive Design**: Works on desktop and mobile devices

## Database Schema

The audit table `AUDIT_RICI_PAGER_ALERT_GROUP` stores:
- Foreign key to main AUDIT table
- Person details (TECID, name, registration number)
- Alert group details (TECID, name, description)
- RIC schema details (TECID, alias)
- RIC range details (TECID, name)
- Pager details (TECID, pager ID, serial number)
- Action value (boolean: assigned/removed)

## Security

The audit functionality respects the existing security constraints:
- Uses `ROLE_PERMISSION_RICI_PAGER_VIEW` for reading audit data
- Uses `ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR` for audit access
- Follows the same security patterns as other RICI audit types

## Testing

To test the implementation:
1. Navigate to the RICI pager assignment page
2. Assign or remove a person from an alert group
3. Check the audit management page at `/audit-management/rici`
4. Verify the new audit record appears in the "RICI Pager Alert Group Audit" panel
5. Click the "View" button to see detailed audit information

## Notes

- Translation keys for the UI labels need to be added to the translation files (fr.json, etc.)
- The implementation follows the existing patterns used by other RICI audit types
- All changes are backward compatible and don't affect existing functionality
