{"rules": {"class-name": true, "comment-format": [true, "check-space"], "curly": true, "eofline": true, "forin": true, "indent": [true, "spaces"], "label-position": true, "max-line-length": [true, {"limit": 140, "ignore-pattern": "^import |^export {(.*?)}"}], "member-access": false, "member-ordering": [true, "static-before-instance", "variables-before-functions"], "no-arg": true, "no-bitwise": true, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-construct": true, "no-debugger": true, "no-duplicate-variable": true, "no-empty": false, "no-eval": true, "no-inferrable-types": true, "no-shadowed-variable": true, "no-string-literal": false, "no-switch-case-fall-through": true, "no-trailing-whitespace": true, "no-unused-expression": false, "no-unused-variable": true, "no-use-before-declare": true, "no-var-keyword": true, "object-literal-sort-keys": false, "one-line": [true, "check-open-brace", "check-catch", "check-else", "check-whitespace"], "quotemark": [true, "single"], "radix": true, "semicolon": [true, "always"], "triple-equals": [true, "allow-null-check", "allow-undefined-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "variable-name": false, "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}