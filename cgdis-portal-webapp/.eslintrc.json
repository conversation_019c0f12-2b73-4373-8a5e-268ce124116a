{
  "root": true,
  "ignorePatterns": [
    "projects/**/*"
  ],
  "overrides": [
    {
      "files": [
        "*.ts"
      ],
      "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:@angular-eslint/recommended",
        "plugin:@angular-eslint/template/process-inline-templates",
        "plugin:prettier/recommended"
      ],
      "plugins": [
        "prettier"
      ],
      "rules": {
        "@angular-eslint/directive-selector": [
          "error",
          {
            "type": "attribute",
            "prefix": "cgdis-portal",
            "style": "camelCase"
          }
        ],
        "@angular-eslint/component-selector": [
          "error",
          {
            "type": "element",
            "prefix": "cgdis-portal",
            "style": "kebab-case"
          }
        ],
        "eqeqeq": [
          "off",
          "always",
          {
            "null": "ignore"
          }
        ],
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-unused-vars": [
          "error",
          {
            "args": "after-used",
            "argsIgnorePattern": "^_",
            "caughtErrors": "all",
            "caughtErrorsIgnorePattern": "^_",
            "destructuredArrayIgnorePattern": "^_",
            //            "varsIgnorePattern": "^_",
            "ignoreRestSiblings": true
          }
        ],
        "prettier/prettier": [
          "error",
          {
            "endOfLine": "auto",
            "plugins": [
              "prettier-plugin-css-order"
            ]
          }
        ],
        "@angular-eslint/no-empty-lifecycle-method": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "prefer-const": "off",
        "no-prototype-builtins": "off",
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-this-alias": "off",
        "@angular-eslint/no-output-on-prefix": "off",
        "no-empty": "off",
        "@typescript-eslint/ban-ts-comment": "off",
        "@angular-eslint/no-input-rename": "off",
        "@typescript-eslint/ban-types": "off",
        "no-extra-semi": "off",
        "no-useless-escape": "off",
        "@angular-eslint/no-host-metadata-property": "off",
        "@angular-eslint/directive-class-suffix": "off",
        "@angular-eslint/no-output-rename": "off",
        "no-var": "off",
        "@angular-eslint/no-output-native": "off",
        "no-control-regex": "off",
        "@angular-eslint/contextual-lifecycle": "off"
      }
    },
    {
      "files": [
        "*.html"
      ],
      "extends": [
        "plugin:@angular-eslint/template/recommended",
        "plugin:@angular-eslint/template/accessibility"
      ],
      "rules": {
        // Disable this rule for HTML due to error in parsing library
        "@angular-eslint/template/eqeqeq": "off",
        "@angular-eslint/template/label-has-associated-control": "off",
        "@angular-eslint/template/click-events-have-key-events": "off",
        "@angular-eslint/template/elements-content": "off",
        "@angular-eslint/template/interactive-supports-focus": "off",
        "@angular-eslint/template/alt-text": "off",
        "@angular-eslint/template/role-has-required-aria": "off",
        "@angular-eslint/template/valid-aria": "off"
      }
    }
  ]
}
