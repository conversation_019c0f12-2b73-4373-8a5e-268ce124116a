# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================
spring:
  profiles:
        active: dev
        include: swagger
  devtools:
      restart:
          enabled: true
          additional-paths: "../cgdis-portal-business,../cgdis-portal-core,../cgdis-portal-db-connector"
      livereload:
          enabled: false
  thymeleaf:
    cache: false
  messages:
    cache-duration: 30s
  datasource:
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 40
      minimum-idle: 20
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    url: ****************************************************************************
    username: cgdis_portal_user
    password: SRnP5t2C7fvkgtSB

server:
  port: 8080

logging:
  #  file: C:/tmp/cgdis-portal.log
  level:
    root: info
    lu: debug
    lu.fujitsu.ts.cgdis.portal.connector.portaldb: INFO
    lu.fujitsu.ts.cgdis.portal.connector.portaldb.config.RepositoryServiceAspect: info
    lu.fujitsu.ts.cgdis.portal.business.config.BusinessServiceAspect: info
#    org.springframework.security: TRACE
    org.springframework.security.web.context.SecurityContextPersistenceFilter: INFO # Don't display all user's roles and permissions
    org.springframework.security.web.access.intercept.FilterSecurityInterceptor: INFO # Don't display all user's roles and permissions
    org.springframework.security.web.authentication.AnonymousAuthenticationFilter: INFO # Don't display all user's roles and permissions
    org.springframework.security.web.context.HttpSessionSecurityContextRepository: INFO  # Don't display all user's roles and permissions
    org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter: INFO   # Don't display all user's roles and permissions
    org.springframework.security.web.util.matcher.AntPathRequestMatcher: INFO
    org.springframework.security.web.FilterChainProxy: INFO
    org.springframework.security.web.util.matcher.OrRequestMatcher: INFO
    org.springframework.boot.actuate.endpoint.web.reactive.ControllerEndpointHandlerMapping: INFO
    lu.fujitsu.ts.cgdis.portal.webapp.config.WebConfig: INFO
    org.springframework.security.ldap: TRACE
  #    com.zaxxer: debug
#    org.hibernate.type: trace
  pattern:
    console: "%clr(%20X{req.method:-} %X{req.requestURI}){green} - %clr(%20d{-yyyy-MM-dd HH:mm:ss.SSS}){faint} - %clr(%-6X{UserId}) - %X{SessionId} -  %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %n %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
#    org.hibernate.type: trace
#    org.hibernate.SQL: error
#    jdbc.sqlonly: error
cgdis-portal:
  connector:
    db:
      show-sql: false
      format-sql: false
    ad:
      domain: DOM.AD.FUJITSU
#      url: ldaps://drupal.fujitsubas.lu:636/
      url: ldap://localhost:1389/
#      url: ldap://localhost:1389/
      mapping:
        ou: PortailCGDIS
        authorities:
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: ChefdeCentre
            authority: ROLE_CENTER_CHIEF
          - groupSuffix: ChefdeZone
            authority: ROLE_ZONE_CHIEF
          - groupSuffix: Volunteer
            authority: ROLE_MEMBER
          - groupSuffix: Volunteer
            authority: ROLE_MEMBER
          - groupSuffix: Volontaire
            authority: ROLE_MEMBER
          - groupSuffix: Perma_Gest
            authority: ROLE_PERMANENCE_MANAGEMENT
          - groupSuffix: Perma_Gest_Light
            authority: ROLE_PERMANENCE_MANAGEMENT_LIGHT
          - groupSuffix: Perma_Gest_Global
            authority: ROLE_PERMANENCE_MANAGEMENT_GLOBAL
#          - groupSuffix: PermaSAP_Gest
#            authority: ROLE_MEMBER
#          - groupSuffix: PermalNCSA_GEST
#            authority: ROLE_MEMBER
          - groupSuffix: Personnes_Audit
            authority: ROLE_PERSON_AUDIT
          - groupSuffix: Personnes_Audit_Global
            authority: ROLE_PERSON_AUDIT_GLOBAL
          - groupSuffix: Personnes_Gest
            authority: ROLE_PERSON_MANAGEMENT
          - groupSuffix: Personnes_Gest_Global
            authority: ROLE_PERSON_MANAGEMENT_GLOBAL
          - groupSuffix: Vehicules_Audit
            authority: ROLE_VEHICLE_AUDIT
          - groupSuffix: Vehicules_Gest
            authority: ROLE_VEHICLE_MANAGEMENT
          - groupSuffix: Admin
            authority: ROLE_ADMIN
          - groupSuffix: AdminOperational
            authority: ROLE_ADMIN_OPERATIONAL
          - groupSuffix: Audit
            authority: ROLE_AUDIT
          - groupSuffix: DATAELS
            authority: ROLE_DCO_DATA_ELS
          - groupSuffix: DPVol
            authority: ROLE_DCO_VOLUNTEER
          - groupSuffix: Helpdesk
            authority: ROLE_HELPDESK
          - groupSuffix: Export_Gest
            authority: ROLE_MANAGEMENT_EXPORT
          - groupSuffix: DMLPagerGest
            authority: ROLE_PAGER_MANAGEMENT
          - groupSuffix: DCOCGOCoord
            authority: ROLE_DCOCGO_COORDINATOR
          - groupSuffix: DCOCGOAssistant
            authority: ROLE_DCOCGO_ASSISTANT
          - groupSuffix: _DCODCOAdministratif
            authority: ROLE_DCODCO_ADMINISTRATIF
          - groupSuffix: _INFSSecretariat
            authority: ROLE_INFS_SECRETARIAT
          - groupSuffix: _MoniteurJeunesP
            authority: ROLE_MONITEUR_JEUNES
          - groupSuffix: _CCIE
            authority: ROLE_CHEF_COMPAGNIE
          - groupSuffix: _directeur
            authority: ROLE_DIRECTOR
          - groupSuffix: _DMSCoordSAMU
            authority: ROLE_DMS_COORDINATOR_SAMU
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: _DAFCompta
            authority: ROLE_DAF_COMPTABILITE
          - groupSuffix: _FO_Gest
            authority: ROLE_FO_GEST
          - groupSuffix: _FO_Gest_Light
            authority: ROLE_FO_GEST_LIGHT
          - groupSuffix: DMSOSCSU
            authority: ROLE_DMS_OFFICIER_SANTE_CSU
          - groupSuffix: DCOCSUCds
            authority: ROLE_DCOCSU_CHEFDESALLE
          - groupSuffix: DCOCSUCadre
            authority: ROLE_DCOCSU_CADRE
          - groupSuffix: DCOCSUReferent
            authority: ROLE_DCOCSU_REFERENT
          - groupSuffix: DCOCSURegulateur
            authority: ROLE_DCOCSU_REGULATEUR
          - groupSuffix: DCOCSUDispatcher
            authority: ROLE_DCOCSU_DISPATCHER
          - groupSuffix: DCOCSUSuppIT
            authority: ROLE_DCOCSU_SUPPORT_IT
          - groupSuffix: INFS_Gestionnaire
            authority: ROLE_INFS_STAGE_GEST
          - groupSuffix: _INFSFormationSpecialisee
            authority: ROLE_INFS_FORM_SPEC
      technical-user: admin
#      technical-user: "cn=jcv,dc=dom,dc=ad,DC=FUJITSU"
      technical-password: "Welcome1*"
      use-space-in-entity-name: true
      user-dn-pattern: "cn=%s,ou=users,dc=dom,dc=ad,DC=FUJITSU"
  business:
    visible-vehicle-entity:
      - "POOL LF"
      - "POOL AMB"
      - "CGDIS POOL"
    notifier:
      update-person-informations:
        request-url: http://localhost:6001/person_information_push_request
      update-service-plan:
        request-url: http://localhost:6001/service_plan_update_request/{servicePlanId}
      update-vehicle-state:
        request-url: http://localhost:6001/vehicle_update_state_push_request
      function-operational-url: http://localhost:6001/function_operational_push_request
      refresh-entities-url: http://localhost:6001/entities_push_request
      refresh-vehicles-url: http://localhost:6001/vehicle_push_request
      generic-notifier-url: http://localhost:6001/generic_notifier
      pager-program-request-url: http://localhost:6001/pager_program_request
      resend-error-message-url : http://localhost:6001/resend_error_message/{tecid}
      resend-error-message-limit-days : 7
      scheduler-properties: http://localhost:6001/scheduler_properties/all
      base-url: http://localhost:6001
      service-plan-send-filling-url: http://localhost:6001/service_plan_send_filling
      prestations-allowances-properties:
        by-date-url: http://localhost:6001/prestations/allowances/all
        by-entity-url: http://localhost:6001/prestations/allowances/entities/{entityId}
        by-person-url: http://localhost:6001/prestations/allowances/persons/{personId}
  export:
    service-plan-properties:
      prestations:
        template-url: "http://localhost:8080/templates/service-plan/%s/prestations/export?from=%s&to=%s"
        logout-url: "http://localhost:8080/logout"
        password: "Welcome1*"
        script: "C:\\JCV\\pupeteer\\test.js"
        temp-folder: "C:\\JCV\\pupeteer\\temp"
        username: "admin"
        commands:
          - "node"
    jasper-templates-path: classpath:\jasper
    default-file-creation-timeout: 60000
  core:
    log-password:
      - "admin"
    environment: DEV
    export:
      period-max-days: 14
      period-default-days: 6
  webapp:
    login-url: "https://cgdis.portal.fujitsubas.lu/login"
    mobile-staging-api-url: ""
management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
