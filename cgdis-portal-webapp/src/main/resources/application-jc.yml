# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================
cgdis-portal:
  connector:
    db:
      show-sql: false
      format-sql: false
  webapp:
    portal-url: http://cgdis.portal:9100
spring:
  devtools:
    restart:
      enabled: false
  boot:
    admin:
      client:
        instance:
          prefer-ip: false
#          metadata:
#            "user.name": FujitsuProcess
#            "user.password": "2WL7d63AgRRzECbsruR"
          service-base-url: http://localhost:8080
          management-base-url: http://localhost:8080
        url: http://localhost:2222
        password: "Welcome1*"
        username: "Fujitsu"
        enabled: true
        period: 10000

  datasource:
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 40
      minimum-idle: 20
#    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: org.mariadb.jdbc.Driver
    url: **********************************************************************
#    url: jdbc:p6spy:mariadb://************:2206/cgdis_portal_db?maxPoolSize=40&pool=true
    username: cgdis_portal_user
    password: SRnP5t2C7fvkgtSB
  session:
    store-type: redis
    redis:
      flush-mode: on_save
      namespace: spring:session
  data:
    redis:
      host: localhost
      port: 16379
server:
  address: 0.0.0.0
