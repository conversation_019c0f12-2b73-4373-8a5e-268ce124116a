# ===================================================================
# Spring Boot configuration for the "production" profile.
#
# This configuration overrides the application.yml file.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================
cgdis-portal:
  business:
    notifier:
      update-person-informations:
        request-url: http://10.216.18.17:8080/bugfix/person_information_push_request
      update-service-plan:
        request-url: http://10.216.18.17:8080/bugfix/service_plan_update_request/{servicePlanId}
      update-vehicle-state:
        request-url: http://10.216.18.17:8080/bugfix/vehicle_update_state_push_request/{tecid}
      function-operational-url: http://10.216.18.17:8080/bugfix/function_operational_push_request
      refresh-entities-url: http://10.216.18.17:8080/bugfix/entities_push_request
      refresh-vehicles-url: http://10.216.18.17:8080/bugfix/vehicle_push_request
      generic-notifier-url: http://10.216.18.17:8080/bugfix/generic_notifier
      resend-error-message-url : http://10.216.18.17:8080/bugfix/resend_error_message/{tecid}
      resend-error-message-limit-days : 7
      scheduler-properties: http://10.216.18.17:8080/bugfix/scheduler_properties/all
      base-url: http://10.216.18.17:8080/bugfix
      service-plan-send-filling-url: http://10.216.18.17:8080/bugfix/service_plan_send_filling
