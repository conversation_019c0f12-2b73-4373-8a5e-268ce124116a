<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<!--  <title>Spring Boot Thymeleaf Hello World Example</title>-->

  <link rel="stylesheet" type="text/css" href="/css/main.css">


  <style>

    .planner__people{
      flex-basis: 10%;
    }

    .planner__people-list.-placeholder {

      font-family: "Roboto", Georgia, "Times New Roman", Times, serif;

    }
    .timeSlotColumn {
      max-width: 10rem;
      min-width: 10rem;
      width: 10rem;

    }

    .people{
      text-align: left;
      align-items: start;
    }

    .planner-timeslots {
      padding: 1.5rem;
    }

    .planner__people .planner__people-list {

    }

    .planner__people-list2 > .planner__people {
      min-width: 100%;
    }


    .planner__people-list2 .planner__people-list {
      width: 100%
    }

    .planner__header-jobs {
      padding-right: 0;
    }

    .all_planner__people-list .planner__people:not(.timeSlotColumn) {
      border-bottom: 1px solid #e9eef5;
    }

    .all_planner__people-list {
      margin: 0;
      padding: 0;
      list-style-type: none;
    }

    .timeSlotColumn.schedule {
      color: white;
    }


    .planner__people svg.icon-time {
      display: block !important;
      margin: auto .8rem !important;
      width: 1.2rem !important;
      height: 1.2rem !important;
    }

    .tab-header {
      font-size: 3rem;
      padding: 5rem 1.5rem;
    }

    .tab-header .servicePlanName::after {
      content: '-';
      margin-left: 1rem;
    }

    .tab-header .scheduleDate::before {
      content: ' ';
      margin-left: 1rem;

    }

    .oneSchedule {
      margin-bottom: 10rem;

    }


    header.header{
      background-color: white;
      position: relative;
      font-size:4rem;
      height: inherit;
      justify-content: center;
      margin-bottom:4rem;
      padding:2rem;
      flex-wrap: wrap;
    }


  </style>

</head>

<body>

<svg xmlns="http://www.w3.org/2000/svg" display="none">
  <symbol id="icon-time" viewBox="0 0 512 512">
    <path
      d="m256 4c-139 0-252 113-252 252 0 139 113 252 252 252 139 0 252-113 252-252 0-139-113-252-252-252z m18 468l0-44-35 0 0 44c-106-9-190-93-199-198l44 0 0-35-44 0c9-106 93-190 198-199l0 44 35 0 0-44c106 9 190 93 199 198l-44 0 0 35 44 0c-9 106-93 190-198 199z m-18-219l-70-124-30 17 78 143 131-1 0-36z"/>
  </symbol>

  <symbol id="icon-logo" viewBox="200 2 200 129">
    <g
      transform="matrix(1.3333333,0,0,-1.3333333,0,132.28)"
      id="g10"><g
      transform="scale(0.1)"
      id="g12"><path
      id="path14"
      style="fill:#2d3c7d;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="M 3968.5,559.363 V 992.129 L 0,992.129 V 559.363 L 65.1953,494.168 0,428.984 V 0 h 3968.5 v 428.984 l -65.18,65.184 65.18,65.195" /><path
      id="path16"
      style="fill:#ed1c24;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="M 3878.31,901.934 V 796.063 h 90.19 V 992.129 H 0 V 796.063 H 90.1953 V 901.934 H 3878.31" /><path
      id="path18"
      style="fill:#00adef;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="M 90.1953,90.1953 V 196.063 H 0 V 0 h 3968.5 v 196.063 h -90.19 V 90.1953 H 90.1953" /><path
      id="path20"
      style="fill:none;stroke:#ffffff;stroke-width:12.75339985;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1"
      d="M 828.25,737.293 V 250.156" /><path
      id="path22"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 331.102,605.223 c 0.371,-0.352 1.093,-0.633 1.613,-0.633 h 116.883 116.894 c 0.52,0 1.238,0.281 1.602,0.633 l 52.441,50.293 c 0.879,0.839 0.879,2.226 0,3.078 l -51.965,49.832 c -0.886,0.851 -2.332,0.851 -3.211,0 l -46.355,-44.453 c -0.879,-0.848 -2.324,-0.848 -3.203,0 l -29.336,28.125 c -0.852,0.82 -1.328,1.925 -1.328,3.078 v 7.82 c 0,1.067 0.41,2.09 1.14,2.891 l 22.442,24.25 c 0.918,0.996 0.754,2.429 -0.364,3.211 l -57.417,39.816 c -0.735,0.508 -1.934,0.508 -2.668,0 l -57.422,-39.816 c -1.114,-0.782 -1.282,-2.215 -0.371,-3.211 l 22.441,-24.25 c 0.742,-0.801 1.145,-1.824 1.145,-2.891 v -7.82 c 0,-1.153 -0.469,-2.258 -1.333,-3.078 l -29.324,-28.125 c -0.886,-0.848 -2.324,-0.848 -3.215,0 l -46.343,44.453 c -0.891,0.851 -2.328,0.851 -3.215,0 l -51.961,-49.832 c -0.879,-0.852 -0.879,-2.239 0,-3.078 l 52.43,-50.293" /><path
      id="path24"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 430.645,346.211 -64.028,48.074 c -0.515,0.391 -0.828,0.977 -0.879,1.602 l -4.707,53.82 c -0.054,0.801 -0.73,1.68 -1.511,1.961 l -69.305,25.273 c -1.738,0.625 -3.176,-0.312 -3.188,-2.097 l -0.386,-61.192 c -0.012,-1.027 0.847,-2.031 1.914,-2.226 l 47.398,-12.871 c 1.301,-0.235 1.445,-0.977 0.324,-1.653 l -59.863,-26.511 c -1.133,-0.676 -1.453,-2.09 -0.715,-3.157 l 44.731,-64.636 c 0.574,-0.84 1.843,-1.223 2.82,-0.852 l 106.785,40.617 c 2.063,0.782 2.336,2.508 0.61,3.848" /><path
      id="path26"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 678.238,555.75 -35.437,41.027 c -0.653,0.996 -2.063,1.356 -3.133,0.789 l -55.727,-29.363 c -0.691,-0.371 -1.472,-0.558 -2.261,-0.558 H 449.598 317.52 c -0.793,0 -1.567,0.187 -2.258,0.558 l -55.723,29.363 c -1.086,0.567 -2.488,0.207 -3.137,-0.789 L 220.965,555.75 c -0.402,-0.605 -0.43,-1.621 -0.07,-2.254 l 36.996,-41.25 c 0.894,-1.582 2.625,-1.758 3.855,-0.391 l 22.356,16.454 c 0.64,0.722 1.707,0.976 2.644,0.625 L 387.41,490.789 c 0.852,-0.312 1.426,-1.074 1.485,-1.941 l 4.101,-60.352 c 0.078,-1.164 1.121,-2.109 2.332,-2.109 l 54.27,-0.215 54.277,0.215 c 1.199,0 2.258,0.945 2.336,2.109 l 4.102,60.352 c 0.054,0.867 0.632,1.629 1.484,1.941 l 100.664,38.145 c 0.926,0.351 1.988,0.097 2.644,-0.625 l 22.356,-16.454 c 1.219,-1.367 2.957,-1.191 3.855,0.391 l 36.993,41.25 c 0.363,0.633 0.324,1.649 -0.071,2.254" /><path
      id="path28"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 523.633,284.637 -73.164,27.656 c -0.481,0.176 -1.25,0.176 -1.731,0 l -73.164,-27.656 c -0.82,-0.313 -1.496,-1.258 -1.496,-2.098 v -29.523 c 0,-0.508 0.317,-1.231 0.695,-1.59 l 17.665,-16.934 c 0.375,-0.371 1.125,-0.664 1.664,-0.664 h 55.496 55.507 c 0.54,0 1.282,0.293 1.661,0.664 l 17.668,16.934 c 0.379,0.359 0.679,1.082 0.679,1.59 v 29.523 c 0,0.84 -0.664,1.785 -1.48,2.098" /><path
      id="path30"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 468.563,346.211 64.023,48.074 c 0.519,0.391 0.832,0.977 0.867,1.602 l 4.719,53.82 c 0.051,0.801 0.73,1.68 1.516,1.961 l 69.292,25.273 c 1.75,0.625 3.176,-0.312 3.188,-2.097 l 0.398,-61.192 c 0.008,-1.027 -0.859,-2.031 -1.914,-2.226 L 563.25,398.555 c -1.301,-0.235 -1.445,-0.977 -0.32,-1.653 l 59.863,-26.511 c 1.121,-0.676 1.441,-2.09 0.711,-3.157 l -44.727,-64.636 c -0.578,-0.84 -1.855,-1.223 -2.824,-0.852 l -106.785,40.617 c -2.063,0.782 -2.332,2.508 -0.605,3.848" /><path
      id="path32"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1089.73,250.156 h -85.35 v 27.735 h 24.66 v 121.386 h -24.66 v 27.735 h 85.35 V 399.277 H 1065.2 V 277.891 h 24.53 v -27.735" /><path
      id="path34"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1225.58,250.156 -64.4,126.375 V 250.156 h -35.26 v 176.856 h 46.76 l 59.93,-119.231 v 119.231 h 35.4 V 250.156 h -42.43" /><path
      id="path36"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1455.64,279.422 c -7.58,-11.336 -16.88,-19.656 -27.92,-24.981 -11.03,-5.32 -24.6,-7.988 -40.69,-7.988 -54.78,0 -82.17,30.703 -82.17,92.129 0,31.438 6.94,54.648 20.83,69.641 13.89,14.988 33.91,22.488 60.06,22.488 17.29,0 31.39,-2.918 42.29,-8.75 10.9,-5.84 19.21,-13.484 24.92,-22.938 l -27.98,-21.718 c -4.09,6.133 -7.84,10.554 -11.25,13.281 -3.41,2.723 -7.29,4.773 -11.62,6.133 -4.35,1.367 -9.54,2.051 -15.6,2.051 -14.48,0 -25.51,-4.629 -33.09,-13.868 -7.58,-9.25 -11.37,-24.687 -11.37,-46.32 0,-20.195 3.55,-35.273 10.66,-45.234 7.12,-9.969 18.47,-14.95 34.06,-14.95 10.47,0 18.64,2 24.47,6.004 5.84,4.004 11.14,9.328 15.91,15.977 l 28.49,-20.957" /><path
      id="path38"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1486.39,250.156 v 176.856 h 114.88 v -31.828 h -78.84 v -37.696 h 66.44 v -31.941 h -66.44 v -42.422 h 78.84 v -32.969 h -114.88" /><path
      id="path40"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1738.8,250.156 -64.41,126.375 V 250.156 h -35.26 v 176.856 h 46.77 l 59.92,-119.231 v 119.231 h 35.4 V 250.156 h -42.42" /><path
      id="path42"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1926.14,338.836 c 0,15.422 -1.51,26.855 -4.53,34.309 -3.03,7.449 -8.16,12.8 -15.4,16.035 -7.24,3.242 -17.98,4.863 -32.2,4.863 h -13.8 V 283.125 h 13.8 c 14.22,0 24.93,1.641 32.14,4.922 7.2,3.281 12.32,8.75 15.39,16.414 3.07,7.668 4.6,19.121 4.6,34.375 z m 37.19,-0.254 c 0,-21.297 -2.94,-38.418 -8.82,-51.367 -5.88,-12.949 -14.74,-22.363 -26.58,-28.242 -11.84,-5.879 -28.49,-8.817 -49.96,-8.817 h -53.92 v 176.856 h 53.41 c 20.87,0 37.46,-3.028 49.77,-9.082 12.31,-6.047 21.4,-15.481 27.28,-28.301 5.88,-12.824 8.82,-29.836 8.82,-51.047" /><path
      id="path44"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2073.33,250.156 h -85.35 v 27.735 h 24.65 v 121.386 h -24.65 v 27.735 h 85.35 V 399.277 H 2048.8 V 277.891 h 24.53 v -27.735" /><path
      id="path46"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2109.51,250.156 v 176.856 h 114.87 v -31.828 h -78.84 v -37.696 h 66.45 v -31.941 h -66.45 v -42.422 h 78.84 v -32.969 h -114.87" /><path
      id="path48"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2413.36,394.297 c 0,3.484 -1.46,6.387 -4.35,8.691 -2.9,2.293 -6.69,3.446 -11.38,3.446 -5.95,0 -10.45,-1.454 -13.47,-4.344 -3.03,-2.902 -4.54,-6.856 -4.54,-11.887 0,-3.496 1,-6.941 3,-10.351 2.01,-3.407 5,-7.364 9.01,-11.883 5.54,3.66 9.95,6.961 13.23,9.902 3.27,2.938 5.51,5.535 6.71,7.801 1.19,2.258 1.79,5.129 1.79,8.625 z m -30.41,-65.938 c -8.01,-5.031 -13.6,-9.902 -16.75,-14.629 -3.15,-4.726 -4.73,-10.117 -4.73,-16.164 0,-7.664 2.85,-13.652 8.57,-17.957 5.7,-4.296 13.49,-6.457 23.39,-6.457 10.98,0 20.6,3.243 28.87,9.719 z m 66.82,-78.203 -9.2,10.996 c -6.13,-4.179 -12.86,-7.675 -20.19,-10.48 -7.33,-2.813 -16.49,-4.219 -27.47,-4.219 -20.19,0 -36.14,4.641 -47.85,13.926 -11.72,9.289 -17.57,21.769 -17.57,37.441 0,11.164 2.44,20.403 7.34,27.727 4.9,7.324 14.76,15.594 29.58,24.793 -5.03,6.133 -9.32,12.433 -12.9,18.906 -3.58,6.477 -5.37,12.988 -5.37,19.551 0,13.633 4.51,24.023 13.55,31.183 9.03,7.157 21.93,10.731 38.72,10.731 14.05,0 25.59,-3.152 34.63,-9.453 9.02,-6.309 13.54,-15.203 13.54,-26.707 0,-6.133 -1.33,-11.926 -3.96,-17.383 -2.64,-5.449 -6.26,-10.559 -10.86,-15.332 -4.6,-4.766 -11.64,-10.266 -21.09,-16.484 l 33.61,-39.485 c 5.44,7.414 9.79,17.207 13.03,29.395 l 29.01,-6.649 c -4.35,-17.968 -11.84,-33.136 -22.49,-45.488 l 27.73,-32.969 h -41.79" /><path
      id="path50"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2721.68,301.395 c 0,-17.715 -5.81,-31.309 -17.44,-40.762 -11.63,-9.453 -28.77,-14.18 -51.44,-14.18 -15.67,0 -29.28,2.598 -40.82,7.793 -11.54,5.195 -20.94,13.586 -28.17,25.176 l 27.72,21.211 c 4.09,-5.801 7.97,-10.137 11.63,-13.035 3.66,-2.903 8.01,-5.157 13.03,-6.77 5.03,-1.621 10.82,-2.43 17.38,-2.43 9.8,0 17.44,1.68 22.94,5.047 5.49,3.36 8.24,8.496 8.24,15.403 0,3.914 -0.96,7.195 -2.88,9.832 -1.91,2.636 -4.79,4.922 -8.62,6.836 -3.84,1.914 -10.48,4.113 -19.93,6.582 -9.2,2.472 -17.87,4.961 -26.01,7.472 -8.13,2.52 -15.16,5.77 -21.08,9.774 -5.92,4.004 -10.59,9.035 -14,15.078 -3.4,6.055 -5.1,13.672 -5.1,22.883 0,16.777 5.66,29.871 16.99,39.285 11.33,9.414 27.56,14.121 48.68,14.121 30.59,0 52.22,-10.477 64.91,-31.434 l -27.47,-21.464 c -4.85,7.488 -10.3,12.859 -16.36,16.101 -6.04,3.234 -13.24,4.856 -21.59,4.856 -8.35,0 -15.14,-1.516 -20.38,-4.543 -5.23,-3.016 -7.86,-7.774 -7.86,-14.247 0,-5.703 2.73,-10.07 8.18,-13.097 5.45,-3.028 14.96,-6.063 28.5,-9.129 7.49,-1.965 14.9,-4.074 22.23,-6.328 7.32,-2.258 13.86,-5.391 19.62,-9.395 5.75,-4.004 10.37,-9.238 13.86,-15.715 3.49,-6.472 5.24,-14.785 5.24,-24.921" /><path
      id="path52"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2756.37,250.156 v 176.856 h 114.87 v -31.828 h -78.84 v -37.696 h 66.45 v -31.941 h -66.45 v -42.422 h 78.84 v -32.969 h -114.87" /><path
      id="path54"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3050.15,279.422 c -7.57,-11.336 -16.88,-19.656 -27.92,-24.981 -11.03,-5.32 -24.6,-7.988 -40.69,-7.988 -54.77,0 -82.17,30.703 -82.17,92.129 0,31.438 6.95,54.648 20.83,69.641 13.89,14.988 33.91,22.488 60.06,22.488 17.29,0 31.39,-2.918 42.3,-8.75 10.9,-5.84 19.21,-13.484 24.91,-22.938 l -27.98,-21.718 c -4.09,6.133 -7.84,10.554 -11.25,13.281 -3.41,2.723 -7.28,4.773 -11.62,6.133 -4.35,1.367 -9.54,2.051 -15.6,2.051 -14.48,0 -25.5,-4.629 -33.09,-13.868 -7.58,-9.25 -11.37,-24.687 -11.37,-46.32 0,-20.195 3.56,-35.273 10.66,-45.234 7.12,-9.969 18.47,-14.95 34.06,-14.95 10.48,0 18.64,2 24.47,6.004 5.84,4.004 11.14,9.328 15.91,15.977 l 28.49,-20.957" /><path
      id="path56"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3186.78,338.582 c 0,20.957 -2.97,36.223 -8.89,45.813 -5.92,9.578 -15.95,14.375 -30.09,14.375 -14.14,0 -24.17,-4.797 -30.1,-14.375 -5.91,-9.59 -8.87,-24.856 -8.87,-45.813 0,-20.957 2.99,-36.23 9,-45.809 6.01,-9.582 16,-14.375 29.97,-14.375 14.14,0 24.17,4.793 30.09,14.375 5.92,9.579 8.89,24.852 8.89,45.809 z m 37.17,0 c 0,-30.75 -6.49,-53.797 -19.48,-69.129 -12.99,-15.332 -31.88,-23 -56.67,-23 -26.15,0 -45.39,8.067 -57.69,24.211 -12.32,16.141 -18.47,38.789 -18.47,67.918 0,29.309 6.15,51.984 18.47,68.039 12.3,16.063 31.54,24.09 57.69,24.09 26.32,0 45.6,-8.027 57.82,-24.09 12.22,-16.055 18.33,-38.73 18.33,-68.039" /><path
      id="path58"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3404.69,324.012 c 0,-27.684 -5.63,-47.578 -16.87,-59.668 -11.25,-12.098 -29.86,-18.145 -55.84,-18.145 -25.73,0 -44.28,5.977 -55.64,17.949 -11.38,11.965 -17.06,31.926 -17.06,59.864 v 103 h 36.03 V 326.57 c 0,-12.011 1.14,-21.465 3.45,-28.367 2.29,-6.894 5.95,-11.973 10.98,-15.207 5.03,-3.242 12.36,-4.851 21.99,-4.851 13.71,0 23.29,3.64 28.75,10.925 5.45,7.278 8.17,19.61 8.17,36.993 v 100.949 h 36.04 v -103" /><path
      id="path60"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3541.12,372.957 c 0,9.023 -2.28,15.078 -6.83,18.145 -4.57,3.066 -12.08,4.601 -22.56,4.601 h -27.98 v -47.16 h 26.44 c 8.1,0 14.32,0.773 18.67,2.305 4.33,1.535 7.47,3.918 9.38,7.16 1.92,3.23 2.88,8.211 2.88,14.949 z m 6.01,-122.801 -45.11,66.961 h -18.27 v -66.961 h -36.04 v 176.856 h 64.27 c 22.74,0 39.38,-4.094 49.9,-12.278 10.52,-8.171 15.78,-21.589 15.78,-40.242 0,-15.078 -3.17,-27.09 -9.52,-36.035 -6.34,-8.945 -15.35,-14.66 -27.02,-17.129 l 47.28,-71.172 h -41.27" /><path
      id="path62"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3743.08,301.395 c 0,-17.715 -5.81,-31.309 -17.44,-40.762 -11.63,-9.453 -28.77,-14.18 -51.43,-14.18 -15.68,0 -29.28,2.598 -40.82,7.793 -11.55,5.195 -20.94,13.586 -28.18,25.176 l 27.73,21.211 c 4.09,-5.801 7.97,-10.137 11.63,-13.035 3.66,-2.903 8.01,-5.157 13.03,-6.77 5.03,-1.621 10.82,-2.43 17.38,-2.43 9.79,0 17.44,1.68 22.94,5.047 5.49,3.36 8.24,8.496 8.24,15.403 0,3.914 -0.97,7.195 -2.88,9.832 -1.91,2.636 -4.8,4.922 -8.62,6.836 -3.84,1.914 -10.48,4.113 -19.94,6.582 -9.19,2.472 -17.87,4.961 -26,7.472 -8.14,2.52 -15.17,5.77 -21.09,9.774 -5.91,4.004 -10.58,9.035 -13.99,15.078 -3.41,6.055 -5.11,13.672 -5.11,22.883 0,16.777 5.67,29.871 17,39.285 11.32,9.414 27.55,14.121 48.68,14.121 30.58,0 52.21,-10.477 64.91,-31.434 l -27.47,-21.464 c -4.85,7.488 -10.3,12.859 -16.36,16.101 -6.04,3.234 -13.24,4.856 -21.59,4.856 -8.35,0 -15.15,-1.516 -20.38,-4.543 -5.24,-3.016 -7.86,-7.774 -7.86,-14.247 0,-5.703 2.72,-10.07 8.18,-13.097 5.45,-3.028 14.95,-6.063 28.5,-9.129 7.49,-1.965 14.9,-4.074 22.22,-6.328 7.33,-2.258 13.87,-5.391 19.62,-9.395 5.75,-4.004 10.37,-9.238 13.87,-15.715 3.49,-6.472 5.23,-14.785 5.23,-24.921" /><path
      id="path64"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1135.8,585.223 c -2.81,-5.028 -6.01,-9.668 -9.59,-13.926 -3.58,-4.258 -7.83,-7.969 -12.77,-11.121 -4.95,-3.145 -10.72,-5.617 -17.32,-7.403 -6.6,-1.796 -14.33,-2.687 -23.19,-2.687 -25.72,0 -44.68,7.707 -56.87,23.125 -12.18,15.422 -18.267,38.418 -18.267,69.004 0,14.824 1.453,27.961 4.347,39.426 2.9,11.453 7.37,21.074 13.41,28.875 6.05,7.793 13.74,13.711 23.07,17.754 9.33,4.054 20.42,6.074 33.28,6.074 8.26,0 15.64,-0.809 22.11,-2.43 6.48,-1.613 12.16,-3.918 17.06,-6.894 4.89,-2.989 9.15,-6.563 12.78,-10.735 3.62,-4.18 6.79,-8.816 9.52,-13.933 l -14.83,-9.454 c -2.21,4.09 -4.67,7.813 -7.41,11.18 -2.72,3.371 -5.94,6.281 -9.64,8.75 -3.71,2.473 -7.97,4.395 -12.78,5.754 -4.82,1.367 -10.41,2.051 -16.81,2.051 -10.05,0 -18.61,-1.692 -25.68,-5.051 -7.07,-3.367 -12.82,-8.309 -17.25,-14.824 -4.43,-6.512 -7.66,-14.5 -9.7,-23.965 -2.06,-9.453 -3.08,-20.313 -3.08,-32.578 0,-12.266 1.02,-23.125 3.08,-32.586 2.04,-9.453 5.32,-17.441 9.83,-23.957 4.51,-6.512 10.35,-11.453 17.51,-14.824 7.15,-3.368 15.84,-5.047 26.06,-5.047 7.16,0 13.35,0.847 18.6,2.558 5.23,1.7 9.72,3.914 13.47,6.641 3.75,2.723 6.93,5.77 9.53,9.141 2.59,3.359 4.87,6.746 6.83,10.156 l 14.7,-9.074" /><path
      id="path66"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1292.27,642.215 c 0,12.265 -0.9,23.144 -2.68,32.648 -1.79,9.5 -4.69,17.481 -8.69,23.953 -4.01,6.477 -9.29,11.399 -15.84,14.766 -6.57,3.359 -14.57,5.051 -24.03,5.051 -9.54,0 -17.59,-1.672 -24.15,-4.992 -6.56,-3.321 -11.86,-8.223 -15.91,-14.696 -4.05,-6.476 -6.96,-14.453 -8.76,-23.957 -1.78,-9.5 -2.67,-20.418 -2.67,-32.773 0,-12.266 0.89,-23.125 2.67,-32.586 1.8,-9.453 4.71,-17.441 8.76,-23.957 4.05,-6.512 9.35,-11.453 15.91,-14.824 6.56,-3.368 14.61,-5.047 24.15,-5.047 9.46,0 17.46,1.679 24.03,5.047 6.55,3.371 11.83,8.281 15.84,14.757 4,6.473 6.9,14.461 8.69,23.965 1.78,9.493 2.68,20.379 2.68,32.645 z m 18.66,0 c 0,-14.227 -1.23,-27.031 -3.71,-38.399 -2.47,-11.375 -6.47,-21.043 -12.01,-29.003 -5.53,-7.969 -12.76,-14.083 -21.66,-18.34 -8.9,-4.258 -19.74,-6.387 -32.52,-6.387 -12.78,0 -23.6,2.129 -32.46,6.387 -8.86,4.257 -16.05,10.39 -21.59,18.398 -5.54,8.008 -9.56,17.676 -12.08,29.004 -2.51,11.34 -3.77,24.113 -3.77,38.34 0,14.14 1.26,26.894 3.77,38.273 2.52,11.367 6.54,21.035 12.08,29.004 5.54,7.969 12.73,14.102 21.59,18.399 8.86,4.304 19.68,6.453 32.46,6.453 12.78,0 23.62,-2.129 32.52,-6.387 8.9,-4.266 16.13,-10.352 21.66,-18.269 5.54,-7.93 9.54,-17.579 12.01,-28.946 2.48,-11.379 3.71,-24.219 3.71,-38.527" /><path
      id="path68"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1451.15,680.672 c 0,6.223 -0.72,11.523 -2.17,15.91 -1.45,4.395 -3.79,7.969 -7.03,10.742 -3.23,2.762 -7.47,4.785 -12.71,6.063 -5.24,1.281 -11.69,1.914 -19.36,1.914 h -32.71 v -71.172 h 31.94 c 8.01,0 14.7,0.664 20.07,1.984 5.36,1.317 9.67,3.426 12.9,6.328 3.24,2.891 5.56,6.661 6.96,11.309 1.41,4.637 2.11,10.281 2.11,16.922 z m 8.44,-126.883 -45.24,74.883 h -37.18 v -74.883 h -17.25 v 176.856 h 50.21 c 9.72,0 18.26,-0.86 25.63,-2.559 7.36,-1.711 13.56,-4.492 18.58,-8.371 5.03,-3.875 8.82,-8.945 11.38,-15.203 2.56,-6.262 3.84,-13.957 3.84,-23.067 0,-7.843 -0.84,-14.629 -2.5,-20.382 -1.66,-5.75 -4.08,-10.625 -7.28,-14.629 -3.2,-4.004 -7.12,-7.227 -11.75,-9.649 -4.65,-2.43 -9.96,-4.285 -15.91,-5.555 l 48.17,-77.441 h -20.7" /><path
      id="path70"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1611.82,674.93 c 0,7.668 -0.95,14.074 -2.87,19.23 -1.92,5.145 -4.68,9.258 -8.31,12.324 -3.62,3.075 -8.02,5.262 -13.22,6.582 -5.2,1.317 -11.11,1.981 -17.76,1.981 h -30.03 v -83.055 h 26.71 c 7.92,0 14.73,0.645 20.45,1.914 5.7,1.278 10.41,3.516 14.11,6.707 3.71,3.196 6.45,7.559 8.24,13.106 1.8,5.527 2.68,12.597 2.68,21.211 z m 18.53,0.508 c 0,-10.731 -1.23,-19.852 -3.7,-27.344 -2.47,-7.5 -6.24,-13.614 -11.31,-18.34 -5.07,-4.727 -11.44,-8.152 -19.1,-10.281 -7.67,-2.129 -16.7,-3.196 -27.09,-3.196 h -29.52 v -62.488 h -17.25 v 176.856 h 48.94 c 20.27,0 35.18,-4.59 44.72,-13.743 9.54,-9.16 14.31,-22.976 14.31,-41.464" /><path
      id="path72"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 1793.23,600.047 c 0,-9.121 -1.62,-16.824 -4.85,-23.125 -3.25,-6.309 -7.63,-11.465 -13.17,-15.469 -5.53,-4.004 -12.06,-6.894 -19.55,-8.68 -7.5,-1.796 -15.51,-2.687 -24.02,-2.687 -8.95,0 -16.79,0.891 -23.52,2.687 -6.73,1.786 -12.62,4.227 -17.69,7.344 -5.07,3.106 -9.46,6.836 -13.17,11.18 -3.7,4.348 -7,9.074 -9.9,14.18 l 14.83,8.82 c 1.7,-3.41 3.91,-6.797 6.65,-10.156 2.72,-3.371 6.04,-6.418 9.96,-9.141 3.91,-2.727 8.49,-4.941 13.74,-6.641 5.23,-1.711 11.26,-2.558 18.07,-2.558 6.22,0 12.03,0.644 17.44,1.914 5.41,1.277 10.12,3.281 14.13,6.004 4,2.734 7.15,6.203 9.46,10.422 2.29,4.218 3.45,9.265 3.45,15.136 0,5.457 -0.9,10.078 -2.69,13.868 -1.79,3.789 -4.51,7.07 -8.17,9.843 -3.68,2.762 -8.31,5.176 -13.94,7.215 -5.61,2.043 -12.26,4.043 -19.93,6.008 -10.23,2.637 -19.03,5.215 -26.45,7.734 -7.41,2.508 -13.54,5.528 -18.4,9.071 -4.86,3.527 -8.45,7.832 -10.8,12.902 -2.34,5.066 -3.51,11.473 -3.51,19.227 0,7.324 1.19,14.015 3.57,20.07 2.39,6.043 5.99,11.219 10.8,15.515 4.82,4.309 10.84,7.649 18.08,10.04 7.25,2.382 15.72,3.574 25.43,3.574 7.32,0 13.97,-0.75 19.94,-2.235 5.96,-1.496 11.33,-3.625 16.1,-6.386 4.77,-2.774 9.03,-6.114 12.78,-10.032 3.74,-3.925 7.11,-8.347 10.09,-13.289 l -14.44,-9.718 c -2.21,3.839 -4.72,7.355 -7.54,10.546 -2.81,3.196 -6,5.938 -9.59,8.243 -3.57,2.297 -7.59,4.062 -12.07,5.304 -4.47,1.231 -9.48,1.856 -15.02,1.856 -6.72,0 -12.6,-0.774 -17.62,-2.305 -5.03,-1.535 -9.2,-3.703 -12.53,-6.515 -3.32,-2.813 -5.79,-6.219 -7.4,-10.223 -1.63,-4.004 -2.44,-8.477 -2.44,-13.418 0,-4.777 0.75,-8.75 2.24,-11.945 1.48,-3.204 3.98,-5.965 7.47,-8.309 3.5,-2.344 8.14,-4.473 13.94,-6.387 5.79,-1.914 12.98,-3.945 21.59,-6.074 9.11,-2.297 17.29,-4.766 24.53,-7.414 7.25,-2.637 13.4,-5.918 18.47,-9.832 5.06,-3.918 8.94,-8.75 11.63,-14.504 2.67,-5.75 4.02,-12.969 4.02,-21.66" /><path
      id="path74"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2035.05,690.898 c -2.31,4.512 -4.89,8.497 -7.74,11.954 -2.85,3.445 -6.15,6.336 -9.9,8.679 -3.75,2.344 -8.01,4.114 -12.78,5.305 -4.77,1.191 -10.14,1.797 -16.1,1.797 -9.63,0 -17.89,-1.602 -24.79,-4.797 -6.9,-3.191 -12.56,-7.988 -16.99,-14.375 -4.44,-6.387 -7.69,-14.356 -9.78,-23.895 -2.09,-9.543 -3.13,-20.656 -3.13,-33.351 0,-11.934 0.98,-22.617 2.94,-32.07 1.96,-9.461 5.19,-17.469 9.71,-24.024 4.51,-6.562 10.46,-11.59 17.82,-15.078 7.38,-3.496 16.47,-5.242 27.29,-5.242 3.57,0 7.02,0.195 10.35,0.574 3.32,0.383 6.6,0.977 9.84,1.789 3.24,0.809 6.54,1.836 9.9,3.066 3.37,1.239 6.88,2.665 10.54,4.286 v 49.707 h -47.15 v 15.715 h 64.4 v -75.137 c -4.94,-2.551 -9.6,-4.817 -13.99,-6.77 -4.39,-1.961 -8.84,-3.601 -13.35,-4.922 -4.52,-1.316 -9.29,-2.324 -14.31,-3.007 -5.03,-0.672 -10.61,-1.016 -16.75,-1.016 -13.96,0 -25.74,2.148 -35.32,6.457 -9.59,4.297 -17.38,10.449 -23.39,18.457 -6,8.008 -10.33,17.703 -12.97,29.07 -2.64,11.379 -3.96,24.172 -3.96,38.399 0,14.23 1.32,27.004 3.96,38.332 2.64,11.336 6.9,20.957 12.78,28.887 5.87,7.917 13.5,14.003 22.88,18.269 9.36,4.258 20.78,6.387 34.23,6.387 8.35,0 15.73,-0.828 22.11,-2.489 6.39,-1.66 11.97,-4.003 16.74,-7.031 4.77,-3.027 8.9,-6.621 12.4,-10.801 3.49,-4.171 6.56,-8.73 9.19,-13.671 l -14.68,-9.454" /><path
      id="path76"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2194.45,680.672 c 0,6.223 -0.72,11.523 -2.17,15.91 -1.45,4.395 -3.79,7.969 -7.03,10.742 -3.23,2.762 -7.47,4.785 -12.71,6.063 -5.24,1.281 -11.69,1.914 -19.36,1.914 h -32.71 v -71.172 h 31.94 c 8.01,0 14.7,0.664 20.07,1.984 5.36,1.317 9.67,3.426 12.9,6.328 3.24,2.891 5.57,6.661 6.96,11.309 1.41,4.637 2.11,10.281 2.11,16.922 z m 8.44,-126.883 -45.24,74.883 h -37.18 v -74.883 h -17.25 v 176.856 h 50.22 c 9.71,0 18.25,-0.86 25.62,-2.559 7.36,-1.711 13.56,-4.492 18.58,-8.371 5.03,-3.875 8.82,-8.945 11.38,-15.203 2.56,-6.262 3.84,-13.957 3.84,-23.067 0,-7.843 -0.84,-14.629 -2.5,-20.382 -1.66,-5.75 -4.08,-10.625 -7.28,-14.629 -3.2,-4.004 -7.12,-7.227 -11.75,-9.649 -4.65,-2.43 -9.96,-4.285 -15.91,-5.555 l 48.17,-77.441 h -20.7" /><path
      id="path78"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2297.91,620.488 h 63 l -31.57,93.407 z m -40.64,-66.699 61.34,176.856 h 22.24 l 61.72,-176.856 h -18.79 l -17.25,50.984 h -73.86 l -17.25,-50.984 h -18.15" /><path
      id="path80"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2548.49,553.789 -87.65,157.813 V 553.789 h -16.74 v 176.856 h 25.04 l 82.67,-148.868 v 148.868 h 17.13 V 553.789 h -20.45" /><path
      id="path82"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2728.27,642.469 c 0,13.371 -1.13,24.679 -3.39,33.926 -2.25,9.238 -5.92,16.738 -10.99,22.492 -5.06,5.75 -11.68,9.883 -19.87,12.39 -8.17,2.512 -18.18,3.77 -30.03,3.77 H 2641.5 V 569.512 h 22.49 c 11.58,0 21.47,1.211 29.65,3.64 8.17,2.422 14.82,6.496 19.93,12.2 5.11,5.703 8.84,13.222 11.18,22.558 2.35,9.324 3.52,20.84 3.52,34.559 z m 18.65,-0.254 c 0,-17.293 -1.77,-31.629 -5.3,-42.996 -3.54,-11.379 -8.73,-20.43 -15.59,-27.16 -6.85,-6.727 -15.33,-11.454 -25.43,-14.18 -10.09,-2.723 -21.7,-4.09 -34.82,-4.09 h -41.65 v 176.856 h 41.4 c 13.88,0 25.91,-1.497 36.1,-4.473 10.17,-2.988 18.63,-7.93 25.36,-14.824 6.73,-6.907 11.74,-15.996 15.01,-27.285 3.28,-11.29 4.92,-25.235 4.92,-41.848" /><path
      id="path84"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 2791.15,612.949 v 16.231 h 79.47 v -16.231 h -79.47" /><path
      id="path86"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3023.35,642.469 c 0,13.371 -1.14,24.679 -3.39,33.926 -2.26,9.238 -5.92,16.738 -10.99,22.492 -5.07,5.75 -11.69,9.883 -19.87,12.39 -8.17,2.512 -18.18,3.77 -30.03,3.77 h -22.49 V 569.512 h 22.49 c 11.58,0 21.47,1.211 29.65,3.64 8.17,2.422 14.82,6.496 19.93,12.2 5.11,5.703 8.84,13.222 11.18,22.558 2.35,9.324 3.52,20.84 3.52,34.559 z m 18.65,-0.254 c 0,-17.293 -1.77,-31.629 -5.3,-42.996 -3.54,-11.379 -8.73,-20.43 -15.59,-27.16 -6.85,-6.727 -15.33,-11.454 -25.43,-14.18 -10.1,-2.723 -21.7,-4.09 -34.82,-4.09 h -41.65 v 176.856 h 41.39 c 13.89,0 25.92,-1.497 36.11,-4.473 10.17,-2.988 18.63,-7.93 25.36,-14.824 6.73,-6.907 11.74,-15.996 15.01,-27.285 3.28,-11.29 4.92,-25.235 4.92,-41.848" /><path
      id="path88"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3215.94,627.645 c 0,-14.481 -1.11,-26.68 -3.32,-36.61 -2.22,-9.922 -5.82,-17.93 -10.81,-24.015 -4.98,-6.094 -11.47,-10.477 -19.48,-13.165 -8.01,-2.683 -17.76,-4.023 -29.26,-4.023 -12.01,0 -22.11,1.387 -30.28,4.152 -8.18,2.762 -14.74,7.235 -19.68,13.418 -4.95,6.172 -8.48,14.2 -10.61,24.082 -2.13,9.883 -3.2,21.934 -3.2,36.161 v 103 h 17.26 V 630.203 c 0,-12.265 0.72,-22.527 2.17,-30.801 1.44,-8.261 3.96,-14.922 7.54,-19.988 3.58,-5.07 8.33,-8.691 14.25,-10.859 5.91,-2.18 13.36,-3.262 22.29,-3.262 9.03,0 16.5,1.082 22.43,3.262 5.92,2.168 10.61,5.789 14.06,10.859 3.44,5.066 5.87,11.707 7.28,19.93 1.41,8.222 2.11,18.34 2.11,30.351 v 100.95 h 17.25 v -103" /><path
      id="path90"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3402.47,585.223 c -2.81,-5.028 -6.01,-9.668 -9.59,-13.926 -3.58,-4.258 -7.83,-7.969 -12.77,-11.121 -4.95,-3.145 -10.72,-5.617 -17.32,-7.403 -6.6,-1.796 -14.33,-2.687 -23.19,-2.687 -25.72,0 -44.68,7.707 -56.87,23.125 -12.18,15.422 -18.27,38.418 -18.27,69.004 0,14.824 1.46,27.961 4.35,39.426 2.9,11.453 7.37,21.074 13.41,28.875 6.05,7.793 13.74,13.711 23.07,17.754 9.33,4.054 20.42,6.074 33.28,6.074 8.26,0 15.64,-0.809 22.11,-2.43 6.48,-1.613 12.16,-3.918 17.06,-6.894 4.89,-2.989 9.15,-6.563 12.78,-10.735 3.62,-4.18 6.79,-8.816 9.52,-13.933 l -14.83,-9.454 c -2.21,4.09 -4.67,7.813 -7.41,11.18 -2.72,3.371 -5.94,6.281 -9.64,8.75 -3.71,2.473 -7.97,4.395 -12.78,5.754 -4.82,1.367 -10.41,2.051 -16.81,2.051 -10.05,0 -18.61,-1.692 -25.68,-5.051 -7.07,-3.367 -12.82,-8.309 -17.25,-14.824 -4.43,-6.512 -7.66,-14.5 -9.7,-23.965 -2.06,-9.453 -3.08,-20.313 -3.08,-32.578 0,-12.266 1.02,-23.125 3.08,-32.586 2.04,-9.453 5.32,-17.441 9.83,-23.957 4.51,-6.512 10.35,-11.453 17.51,-14.824 7.15,-3.368 15.84,-5.047 26.06,-5.047 7.16,0 13.35,0.847 18.6,2.558 5.23,1.7 9.72,3.914 13.47,6.641 3.75,2.723 6.93,5.77 9.53,9.141 2.59,3.359 4.87,6.746 6.83,10.156 l 14.7,-9.074" /><path
      id="path92"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3473.27,620.488 h 63 l -31.56,93.407 z m -40.64,-66.699 61.34,176.856 h 22.24 l 61.72,-176.856 h -18.79 l -17.25,50.984 h -73.85 l -17.26,-50.984 h -18.15" /><path
      id="path94"
      style="fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none"
      d="m 3619.46,553.789 v 176.856 h 17.25 V 569.512 h 82.93 v -15.723 h -100.18" /></g></g>
  </symbol>
</svg>


<header class="header" >
  <svg class="icon-logo header__logo " style="width:100%">
    <use xlink:href="#icon-logo"></use>
  </svg>
  <span class="col-md-6 txtright" th:text="${servicePlan.label}"></span>
  <span class="col-md-6" th:text="${servicePlan.portalLabel}"></span>
</header>
<div class="oneSchedule" th:each="schedule,scheduleStat : *{allSchedules}">

  <div class="tabs panel planner">
    <div class="tab-header">

      <div class="scheduleDate" th:text="${schedule.date}"></div>
    </div>
    <div class="tabs__content">
      <!-- Positions -->
      <div class="planner__header-jobs">
        <ul class="planner__people-list -placeholder">
          <li class="timeSlotColumn planner__people schedule -actions">

            <svg class="icon-time ng-star-inserted">
              <use xlink:href="#icon-time"></use>
            </svg>


          </li>

          <li th:each="onePosition,OnePositionStat : *{schedule.getPositions()}"
              th:class="${(#strings.equals(onePosition.getCompletionType() ,'DEGRADED')? 'planner\_\_people' : 'planner\_\_people -optional')+' ' + onePosition.getCompletionType().toString().toLowerCase() }"
          >
            <span th:text="${onePosition.getLabel()}"></span>
            <svg th:if="${onePosition.getIcon() != null}">
              <use xlink:href="'#'+[[__${onePosition.getIcon()}__]]"></use>
            </svg>


          </li>

        </ul>
      </div>


    </div>

    <div

      class="tabs__content">

      <ul class="all_planner__people-list planner__people-list2">

        <li class="all_planner__people"
            th:each="oneRow,rowStat : *{schedule.getRows()}"
            id="schedule-[[__${rowStat.index}__]]-content"
        >
          <ul class="planner__people-list">

            <!-- a deplacer au dessus dans le jheader -->
            <!--        <div class="schedule -actions">-->
            <!--          <cgdis-portal-icon *ngIf="schedule" [icon]="timeIcon"></cgdis-portal-icon>-->
            <!--        </div>-->


            <li class="planner-timeslots timeSlotColumn">
              <div
                class="service-planner__schedule-selector schedule -complete"

              >

                <span class="schedule__status status "
                th:classappend="'-'+${#strings.toLowerCase(oneRow.getStatus())}"
                >●</span>

                <span>
                  <span class="schedule__start" th:text="${oneRow.getSlot().getStartTime()}"></span>
                  <span class="schedule__end" th:text="${oneRow.getSlot().getEndTime()}"></span>


      </span>

              </div>
            </li>


<!--            <li class="planner__people" th:each="onePosition,OnePositionStat : *{schedule.getPositions()}"-->
<!--                th:with="prestationForSlot=${prestationsBySlotAndPositions.get(oneRow.getSlot().getTecid())}"-->

<!--            >-->
            <li class="planner__people" th:each="onePosition,OnePositionStat : *{schedule.getPositions()}"
                th:with="prestationForSlot=${prestationsByDatesSlotAndPositions.get(schedule.getDate()).get(oneRow.getSlot().getTecid())}"

            >


              <div class="people" th:with="prestationOptional=${prestationForSlot.get(onePosition.getTecid())}"
                   th:switch="${prestationOptional!=null && prestationOptional.isPresent()}">
<!--                <span th:case="false" th:text="#{service_plan.no_data}"></span>-->
                <!-- only one prestation -->
                <div th:case="true" th:with="prestation=${prestationOptional.get()}"
                     th:class="${prestation.isAvailable()} ? '': 'person-unavailable'">

                  <div #container>

                    <svg class="-job" th:if="${prestation.isProfessional()}">
                      <use xlink:href="'#icon-pro'"></use>
                    </svg>


                    <!-- firstname lastname -->
                    <span
                      th:text="${prestation.getPerson().getFirstName()+ ' ' + prestation.getPerson().getLastName()}"></span>
                    <!-- registration number -->
                    <span class="people__lastname"
                          th:text="${prestation.getPerson().getCgdisRegistrationNumber()}"></span>

                  </div>

                </div>


              </div>

            </li>

          </ul>
        </li>

      </ul>
      <div class="clearfix"></div>

    </div>
  </div>


</div>

</body>
</html>
