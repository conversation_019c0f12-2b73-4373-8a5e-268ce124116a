# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

spring:
  profiles:
        active: dev
        include: swagger
  datasource:
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 40
      minimum-idle: 20
    driver-class-name: org.mariadb.jdbc.Driver
    url: **********************************************************************
    username: cgdis_portal_user
    password: SRnP5t2C7fvkgtSB
  security:
    oauth2:
      client:
        registration:
          ctie:
            provider: ctie
            clientName: CGDIS
            authorizationGrantType: authorization_code
            client-id: account
            client-secret: 51c79d3b-3ab7-49bd-81e8-b3b4a3d0bd2f
            redirectUriTemplate: https://cgdis.portal.fujitsubas.lu/mobile/authorization
            scope:
              - openid
        provider:
          ctie:
            authorizationUri: https://test.keycloak.fujitsubas.lu/auth/realms/cgdis/protocol/openid-connect/auth
            tokenUri: https://test.keycloak.fujitsubas.lu/cgdis/auth/realms/cgdis/protocol/openid-connect/token
            userInfoUri:
            userNameAttribute:
            jwkSetUri: https://test.keycloak.fujitsubas.lu/auth/realms/cgdis/protocol/openid-connect/certs
server:
  port: 8080
logging:
  file: /logs/cgdis-portal.log
  level:
      org.springframework.jpa : error
      org.springframework.jdbc.datasource: error

cgdis-portal:
  webapp:
    portal-url: https://cgdis.portal.fujitsubas.lu
    mobile-url: cgdisportal://index.html
  connector:
    db:
      show-sql: false
      format-sql: false
    ad:
      domain: DOM.AD.FUJITSU
      url: ldaps://drupal.fujitsubas.lu:636/
      mapping:
        ou: PortailCGDIS
        authorities:
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: ChefdeCentre
            authority: ROLE_CENTER_CHIEF
          - groupSuffix: ChefdeZone
            authority: ROLE_ZONE_CHIEF
          - groupSuffix: Volunteer
            authority: ROLE_MEMBER
          - groupSuffix: Volunteer
            authority: ROLE_MEMBER
          - groupSuffix: Volontaire
            authority: ROLE_MEMBER
          - groupSuffix: Perma_Gest
            authority: ROLE_PERMANENCE_MANAGEMENT
          - groupSuffix: Perma_Gest_Light
            authority: ROLE_PERMANENCE_MANAGEMENT_LIGHT
          - groupSuffix: Perma_Gest_Global
            authority: ROLE_PERMANENCE_MANAGEMENT_GLOBAL
#          - groupSuffix: PermaSAP_Gest
#            authority: ROLE_MEMBER
#          - groupSuffix: PermalNCSA_GEST
#            authority: ROLE_MEMBER
          - groupSuffix: Personnes_Audit
            authority: ROLE_PERSON_AUDIT
          - groupSuffix: Personnes_Audit_Global
            authority: ROLE_PERSON_AUDIT_GLOBAL
          - groupSuffix: Personnes_Gest
            authority: ROLE_PERSON_MANAGEMENT
          - groupSuffix: Personnes_Gest_Global
            authority: ROLE_PERSON_MANAGEMENT_GLOBAL
          - groupSuffix: Vehicules_Audit
            authority: ROLE_VEHICLE_AUDIT
          - groupSuffix: Vehicules_Gest
            authority: ROLE_VEHICLE_MANAGEMENT
          - groupSuffix: Admin
            authority: ROLE_ADMIN
          - groupSuffix: AdminOperational
            authority: ROLE_ADMIN_OPERATIONAL
          - groupSuffix: Audit
            authority: ROLE_AUDIT
          - groupSuffix: DATAELS
            authority: ROLE_DCO_DATA_ELS
          - groupSuffix: DPVol
            authority: ROLE_DCO_VOLUNTEER
          - groupSuffix: Helpdesk
            authority: ROLE_HELPDESK
          - groupSuffix: Export_Gest
            authority: ROLE_MANAGEMENT_EXPORT
          - groupSuffix: DMLPagerGest
            authority: ROLE_PAGER_MANAGEMENT
          - groupSuffix: DCOCGOCoord
            authority: ROLE_DCOCGO_COORDINATOR
          - groupSuffix: DCOCGOAssistant
            authority: ROLE_DCOCGO_ASSISTANT
          - groupSuffix: _DCODCOAdministratif
            authority: ROLE_DCODCO_ADMINISTRATIF
          - groupSuffix: _INFSSecretariat
            authority: ROLE_INFS_SECRETARIAT
          - groupSuffix: _MoniteurJeunesP
            authority: ROLE_MONITEUR_JEUNES
          - groupSuffix: _CCIE
            authority: ROLE_CHEF_COMPAGNIE
          - groupSuffix: _directeur
            authority: ROLE_DIRECTOR
          - groupSuffix: _DMSCoordSAMU
            authority: ROLE_DMS_COORDINATOR_SAMU
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: _DAFCompta
            authority: ROLE_DAF_COMPTABILITE
          - groupSuffix: _FO_Gest
            authority: ROLE_FO_GEST
          - groupSuffix: _FO_Gest_Light
            authority: ROLE_FO_GEST_LIGHT
          - groupSuffix: DMSOSCSU
            authority: ROLE_DMS_OFFICIER_SANTE_CSU
          - groupSuffix: DCOCSUCds
            authority: ROLE_DCOCSU_CHEFDESALLE
          - groupSuffix: DCOCSUCadre
            authority: ROLE_DCOCSU_CADRE
          - groupSuffix: DCOCSUReferent
            authority: ROLE_DCOCSU_REFERENT
          - groupSuffix: DCOCSURegulateur
            authority: ROLE_DCOCSU_REGULATEUR
          - groupSuffix: DCOCSUDispatcher
            authority: ROLE_DCOCSU_DISPATCHER
          - groupSuffix: DCOCSUSuppIT
            authority: ROLE_DCOCSU_SUPPORT_IT
          - groupSuffix: INFS_Gestionnaire
            authority: ROLE_INFS_STAGE_GEST
          - groupSuffix: _INFSFormationSpecialisee
            authority: ROLE_INFS_FORM_SPEC
      technical-password: "Welcome1*"
      technical-user: jcv
      use-space-in-entity-name: true
  business:
    visible-vehicle-entity:
      - "POOL LF"
      - "POOL AMB"
      - "CGDIS POOL"
    notifier:
      update-person-informations:
        request-url: http://internal-gateway:15002/person_information_push_request
      update-service-plan:
        request-url: http://internal-gateway:15002/service_plan_update_request/{servicePlanId}
      update-vehicle-state:
        request-url: http://internal-gateway:15002/vehicle_update_state_push_request
      function-operational-url: http://internal-gateway:15002/function_operational_push_request
      refresh-entities-url: http://internal-gateway:15002/entities_push_request
      refresh-vehicles-url: http://internal-gateway:15002/vehicle_push_request
      generic-notifier-url: http://internal-gateway:15002/generic_notifier
      resend-error-message-url : http://internal-gateway:15002/resend_error_message/{tecid}
      resend-error-message-limit-days : 7
      scheduler-properties: http://internal-gateway:15002/scheduler_properties/all
      base-url: http://internal-gateway:15002
      service-plan-send-filling-url: http://internal-gateway:15002/service_plan_send_filling
      prestations-allowances-properties:
        by-date-url: http://internal-gateway:15002/prestations/allowances/all
        by-entity-url: http://internal-gateway:15002/prestations/allowances/entities/{entityId}
        by-person-url: http://internal-gateway:15002/prestations/allowances/persons/{personId}
  export:
    service-plan-properties:
      prestations:
        template-url: "http://cgdis-portal:15001/templates/service-plan/%s/prestations/export?from=%s&to=%s"
        logout-url: "http://cgdis-portal:15001/logout"
        password: "Welcome1*"
        script: "/home/<USER>/cgdis/puppeteer/generate-pdf.sh"
        temp-folder: "/home/<USER>/cgdis/puppeteer/temp"
        username: "admin"
  core:
    environment: INT
management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'


