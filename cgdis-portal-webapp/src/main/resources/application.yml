# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overriden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

spring:
  mvc:
    favicon:
        enabled: true
  devtools:
    restart:
      enabled: false
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 20
  security:
    oauth2:
      client:
        registration:
          ctie:
            provider: ctie
            clientName: CGDIS
            authorizationGrantType: authorization_code
#            client-id: portailcgdis-test
            client-id: account
#            client-secret: clientSecretValue
            client-secret: 51c79d3b-3ab7-49bd-81e8-b3b4a3d0bd2f
#            redirectUriTemplate: https://portailcgdis-test.intranet.etat.lu/mobile/authorization
            redirectUriTemplate: http://192.168.0.45:8080/mobile/authorization
            scope:
              - openid
        provider:
          ctie:
#            authorizationUri: https://idp.intranet.etat.lu/sps/oauth/oauth20/authorize
            authorizationUri: http://fas-linux-vm.fujitsubas.local:10010/auth/realms/cgdis/protocol/openid-connect/auth
#            tokenUri: https://idp.intranet.etat.lu /mga/sps/oauth/oauth20/token
            tokenUri: http://fas-linux-vm.fujitsubas.local:10009/auth/realms/cgdis/protocol/openid-connect/token
            userInfoUri:
            userNameAttribute:
#            jwkSetUri: https://www.servicespublics-test.etat.lu/sps/jwks
            jwkSetUri: http://fas-linux-vm.fujitsubas.local:10010/auth/realms/cgdis/protocol/openid-connect/certs
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration


management:
  server:
    servlet:
      context-path: /manage
  metrics:
    web:
      server:
        auto-time-requests: true
        requests-metric-name: "cgdis-portal-server"

      client:
        max-uri-tags: 1000
        requests-metric-name: "cgdis-portal-client"
  endpoints:
    web:
      base-path: '/9nyNXgcGpNGr2BerMsrA7NmhcqRNHqdubAV4TvVqs8JPVLFEa7'
swagger:
    default-include-pattern: /api/.*
    title: CGDIS Portal API
    description: CGDIS Portal API documentation
    version: 0.0.1

cgdis-portal:
  core:
    check-operational-function: false
    default-date-pattern: "dd/MM/yyyy"
    filling-number-slot : 5
    export:
      period-max-days: 14
      period-default-days: 6
  business:
    notice-text: "Dienstplan nicht besetzt %s"
    max-hours-full-availability : 12
    visible-vehicle-entity:
      - "CGDIS POOL"
    external-interventions-types:
      - "ambulance"
    hidden-role-domains:
      - " AdmGroups"
      - "CGDIS POOL"
      - "_AdmGroups"
      - "CGDISPool"
      - "PortailCGDISTest"
      - "PortailCGDISProd"
    forbidden-role-mobile:
      - "ROLE_PERMISSION_ADMIN_PDS_CREATE"
      - "ROLE_PERMISSION_ADMIN_PDS_DELETE"
      - "ROLE_PERMISSION_ADMIN_PDS_CLOSURE"
      - "ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL"
      - "ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL"
      - "ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE"
      - "ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE"
      - "ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE"
      - "ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE"
      - "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY"
      - "ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE"
      - "ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE"
      - "ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE"
      - "ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE"
      - "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_CREATE"
      - "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_UPDATE"
      - "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_DELETE"
      - "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE"
      - "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE"
      - "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE"
      - "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE"
      - "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE"
      - "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE"
      - "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE"
      - "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE"
      - "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE"
      - "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE"
      - "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE"
      - "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE"
      - "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS"
      - "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY"
      - "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS"
      - "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY"
      - "ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS"
      - "ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS"
    inalienable-role-rights:
      ROLE_ADMIN:
        - ROLE_PERMISSION_ADMIN
        - ROLE_PERMISSION_USER_RIGHTS
        - ROLE_PERMISSION_USER_RIGHTS_VIEW
        - ROLE_PERMISSION_USER_RIGHTS_UPDATE
        - ROLE_PERMISSION_USER_RIGHTS_DELETE
    role-rights-deletion-dependency:
      ROLE_PERMISSION_ADMIN:
        - ROLE_PERMISSION_ADMIN_MODEL_PDS
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE
        - ROLE_PERMISSION_ADMIN_PDS
        - ROLE_PERMISSION_ADMIN_PDS_VIEW
        - ROLE_PERMISSION_ADMIN_PDS_CREATE
        - ROLE_PERMISSION_ADMIN_PDS_DELETE
        - ROLE_PERMISSION_ADMIN_PDS_CLOSURE
        - ROLE_PERMISSION_ADMIN_PDS_UPDATE
        - ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO
        - ROLE_PERMISSION_ADMIN_PDS_VERSION
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE
        - ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_VIEW
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VIEW
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CLOSURE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_VIEW
        - ROLE_PERMISSION_ADMIN_VEHICLES
        - ROLE_PERMISSION_ADMIN_VEHICLES_VIEW
        - ROLE_PERMISSION_ADMIN_BOX
        - ROLE_PERMISSION_ADMIN_BOX_VIEW
        - ROLE_PERMISSION_ADMIN_BOX_UPDATE
        - ROLE_PERMISSION_ADMIN_VEHICLES_ID_VIEW
        - ROLE_PERMISSION_ADMIN_VEHICLES_STATUS_VIEW
        - ROLE_PERMISSION_ADMIN_ENTITIES
        - ROLE_PERMISSION_ADMIN_ENTITIES_VIEW
        - ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE
        - ROLE_PERMISSION_ADMIN_SCHEDULER
        - ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT
        - ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW
      ROLE_PERMISSION_ADMIN_MODEL_PDS:
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION
      ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION:
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY
      ROLE_PERMISSION_ADMIN_PDS:
        - ROLE_PERMISSION_ADMIN_PDS_VIEW
        - ROLE_PERMISSION_ADMIN_PDS_CREATE
        - ROLE_PERMISSION_ADMIN_PDS_DELETE
        - ROLE_PERMISSION_ADMIN_PDS_CLOSURE
        - ROLE_PERMISSION_ADMIN_PDS_UPDATE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION
        - ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO
      ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE:
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_VIEW
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE
        - ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE
      ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY:
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_VIEW
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE
        - ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE
      ROLE_PERMISSION_ADMIN_PDS_TEAM:
        - ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW
        - ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE
        - ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE
        - ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE
      ROLE_PERMISSION_ADMIN_PDS_VERSION:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY
        - ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT
      ROLE_PERMISSION_ADMIN_VEHICLES:
        - ROLE_PERMISSION_ADMIN_VEHICLES_VIEW
        - ROLE_PERMISSION_ADMIN_VEHICLES_ID_VIEW
        - ROLE_PERMISSION_ADMIN_VEHICLES_STATUS_VIEW
      ROLE_PERMISSION_ADMIN_BOX:
        - ROLE_PERMISSION_ADMIN_BOX_VIEW
      ROLE_PERMISSION_ADMIN_ENTITIES:
        - ROLE_PERMISSION_ADMIN_ENTITIES_VIEW
        - ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE
      ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL:
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VIEW
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE
        - ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE
      ROLE_PERMISSION_USER_RIGHTS:
        - ROLE_PERMISSION_USER_RIGHTS_VIEW
        - ROLE_PERMISSION_USER_RIGHTS_UPDATE
        - ROLE_PERMISSION_USER_RIGHTS_DELETE
      ROLE_PERMISSION_USER_MANAGEMENT:
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS:
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL
      ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT:
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE
      ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING:
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE
      ROLE_PERMISSION_PDS_LIST:
        - ROLE_PERMISSION_PDS_LIST_VIEW
      ROLE_PERMISSION_PDS_FILL_:
        - ROLE_PERMISSION_PDS_FILL_VIEW
        - ROLE_PERMISSION_PDS_FILL_ADD_PERSON
        - ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED
        - ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION
        - ROLE_PERMISSION_PDS_FILL_EXPORT
        - ROLE_PERMISSION_PDS_FILL_EXPORT_PDF
        - ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES
      ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_:
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY
      ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS:
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY
      ROLE_PERMISSION_MY_PROFILE_VIEW:
        - ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE
        - ROLE_PERMISSION_MY_PROFILE_PREFERENCES_VIEW
        - ROLE_PERMISSION_MY_PROFILE_PREFERENCES_UPDATE
        - ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW
        - ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS
        - ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL
        - ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW
        - ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW
        - ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW
        - ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS
        - ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY
      ROLE_PERMISSION_ADMIN_EXPORT_PROFILE:
        - ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_VIEW
        - ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_CREATE
        - ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_UPDATE
        - ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_DELETE
      ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE:
        - ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT
      ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT:
        - ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW
    role-rights-dependency:
      ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED:
        - ROLE_PERMISSION_PDS_FILL_ADD_PERSON
      ROLE_PERMISSION_ADMIN_MODEL_PDS:
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION
      ROLE_PERMISSION_ADMIN_PDS:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION
        - ROLE_PERMISSION_ADMIN_PDS_VIEW
      ROLE_PERMISSION_ADMIN_PDS_VIEW:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION
        - ROLE_PERMISSION_ADMIN_VEHICLES_VIEW
      ROLE_PERMISSION_ADMIN_PDS_VERSION:
        - ROLE_PERMISSION_ADMIN_PDS
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW
      ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE:
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE
      ROLE_PERMISSION_ADMIN_PDS_CREATE:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE
      ROLE_PERMISSION_ADMIN_PDS_TEAM:
        - ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW
      ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE
      ROLE_PERMISSION_ADMIN_VEHICLES:
        - ROLE_PERMISSION_ADMIN_VEHICLES_VIEW
      ROLE_PERMISSION_ADMIN_ENTITIES:
        - ROLE_PERMISSION_ADMIN_ENTITIES_VIEW
      ROLE_PERMISSION_USER_RIGHTS:
        - ROLE_PERMISSION_USER_RIGHTS_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT:
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING:
        - ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS:
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK:
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE:
        - ROLE_PERMISSION_USER_MANAGEMENT
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL:
        - ROLE_PERMISSION_USER_MANAGEMENT
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER:
        - ROLE_PERMISSION_USER_MANAGEMENT
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW
      ROLE_PERMISSION_PDS_LIST:
        - ROLE_PERMISSION_PDS_LIST_VIEW
      ROLE_PERMISSION_PDS_FILL_:
        - ROLE_PERMISSION_PDS_FILL_VIEW
      ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_:
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW
      ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS:
        - ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW
      ROLE_PERMISSION_MY_PROFILE_PREFERENCES_VIEW:
        - ROLE_PERMISSION_MY_PROFILE_PREFERENCES_UPDATE
      ROLE_PERMISSION_ADMIN_EXPORT_PROFILE:
        - ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_VIEW
      ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY:
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE
      ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE
      ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT:
        - ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE
      ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS:
        - ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW
      ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS:
        - ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW
    available-preferences:
      LANGUAGE: #The key used in the DB to store the language formatting preference of the user
        FR: "fr" #The key stored in the DB and the value (lowercase, like the suffix of the i18n properties file)
        EN: "en"
      CALENDAR_DAYS: #The key used in the DB to store the number of days to display on calendars
        7: "7"  #7 days week
        5: "5"
        4: "4"
        3: "3"
        2: "2"
        1: "1"
      FIRST_DAY_OF_WEEK:
        1: "1"
        2: "2"
        3: "3"
        4: "4"
        5: "5"
        6: "6"
        7: "7"
      FULL_AVAILABILITY:
        TRUE: "true"
        FALSE: "false"
      OTHER_PRESTATIONS:
        TRUE: "true"
        FALSE: "false"
      ERROR_NOTIFICATION:
        TRUE: "true"
        FALSE: "false"
      INPUT_GEPS_NOTIFICATION:
        TRUE: "true"
        FALSE: "false"
      INPUT_GEPS_ERROR_NOTIFICATION:
        TRUE: "true"
        FALSE: "false"
      ASSIGNMENT_NOTIFICATION:
        TRUE: "true"
        FALSE: "false"
      PRESTATION_NOTIFICATION:
        TRUE: "true"
        FALSE: "false"
      AVAILABILITY_NOTIFICATION:
        TRUE: "true"
        FALSE: "false"
      CLOSED_CALENDAR:
        TRUE: "true"
        FALSE: "false"
      SHOW_PRESTATION:
        TRUE: "true"
        FALSE: "false"
      UPDATE_VEHICLE_6:
        FORMAT: "YYYY-MM-DD HH:mm:ss"
        EMPTY: "null"
      UPDATE_VEHICLE_6C:
        FORMAT: "YYYY-MM-DD HH:mm:ss"
        EMPTY: "null"
      PARTIAL_AVAILABILITY:
        TRUE: "true"
        FALSE: "false"
    permamonitor:
      poj-days-number: 6
    notifier:
      scheduler-properties:
      scheduler-permissions:
        "servicePlan":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_ELS
        "vehicles":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_ELS
        "entities":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_ELS
        "backupGroup":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_ELS
        "personAssignments":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_ASSIGNMENT
        "exportBackup112":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_BACKUP112
        "servicePlanExport":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_EXPORTCSVCIS
        "prestationQueue":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_INTERNAL
        "prestationsAllowances":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_INTERNAL
        "vehicleBox":
          - ROLE_PERMISSION_ADMIN_SCHEDULER_ELS

  connector:
    news:
      entity-parameter: "entities"
      language-parameter: "language"
      limit-parameter: "limit"
      offset-parameter:  "offset"
      role-parameter: "roles"
      url: "http://cgdis.drupal.fujitsubas.lu/api/v1/views/news?"
      language-filter: "language={language}"
      limit-filter: "limit={limit}"
      offset-filter: "offset={offset}"
      role-filter: "target_profile={roles}"
      entity-filter: "target_entity={entities}"
      id-filter: "nid={id}"
      id-parameter: "id"
    ad:
      use-space-in-entity-name: false
  webapp:
    api-base-path: "/"
    portal-url: http://192.168.0.45:9000
#    mobile-url: http://192.168.0.45:9001
    mobile-url: cgdisportal://home/
    export:
      servicePlanFileName: "Export PortailCGDIS %s.csv"
    mobile-staging-api-url: "https://portailcgdis.api-stg.etat.lu/cgdis"
  export:
    default-file-creation-timeout: 20000
    prestations:
      nb-days: 6
      template: "/prestations/export-permanences-list.jrxml"
    service-plan-properties:
      prestations:
        commands:
            - "sudo"
            - "nodejs"
    jasper-templates-path: classpath:/jasper
logging:
  level:
    lu: debug
    lu.fujitsu.ts.cgdis.portal.connector.portaldb: INFO
    lu.fujitsu.ts.cgdis.portal.connector.portaldb.config.RepositoryServiceAspect: info
    lu.fujitsu.ts.cgdis.portal.business.config.BusinessServiceAspect: info
    org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter: INFO
    org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping: info
    org.springframework.boot.actuate.endpoint.web.reactive.ControllerEndpointHandlerMapping: info
    ch.qos.logback.classic.joran.ReconfigureOnChangeTask: info
    com.zaxxer.hikari: info
  pattern:
    file: "%-6X{req.method:-} %X{req.requestURI} %n -%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} - %clr(%-6X{UserId}) - %X{SessionId} - ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
springdoc:
  api-docs:
    enabled: true
    groups:
      enabled: false
    resolve-schema-properties: true
    version: openapi_3_0
  writer-with-order-by-keys: true
  cache:
    disabled: true
  default-flat-param-object: false
  swagger-ui:
    deep-linking: true
    tags-sorter: true
    path: /swagger-ui.html
  override-with-generic-response: true
  sort-converter:
    enabled: true
  writer-with-default-pretty-printer: true
  model-converters:
    polymorphic-converter:
      enabled: true

info:
  app:
    version: '@project.version@'
