# ===================================================================
# Spring Boot configuration for the "production" profile.
#
# This configuration overrides the application.yml file.
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================
spring:
    profiles:
        active: prod
    datasource:
      tomcat:
        test-on-borrow: true
        validation-query: SELECT 1
      hikari:
        connection-test-query: SELECT 1
        maximum-pool-size: 50
        minimum-idle: 20
      driver-class-name: org.mariadb.jdbc.Driver
      url: **************************************************************************
      username: cgdis_portal_user
      password: vVNE5lqSH6jEErdcIVAh
server:
    compression:
        enabled: true
        mime-types: text/html,text/xml,text/plain,text/css, application/javascript, application/json
    servlet:
      session:
        cookie:
          http-only: true
          secure: true
logging:
  file: /var/log/cgdis/cgdis-portal.log
  level:
    org.springframework.jpa : debug
    org.springframework.jdbc.datasource: debug
    lu.fujitsu.ts: debug
    com.zaxxer: debug
cgdis-portal:
  connector:
    news:
      url: "https://INTRANET-01.ad.cgdis.lu/api/v1/views/news?"
    db:
      show-sql: false
      format-sql: false
    ad:
      domain: AD.CGDIS.lu
      url: ldaps://************:636/
      mapping:
        ou: PortailCGDISProd
        authorities:
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: ChefdeCentre
            authority: ROLE_CENTER_CHIEF
          - groupSuffix: ChefdeZone
            authority: ROLE_ZONE_CHIEF
          - groupSuffix: Membre
            authority: ROLE_MEMBER
          - groupSuffix: Perma_Gest
            authority: ROLE_PERMANENCE_MANAGEMENT
          - groupSuffix: Perma_Gest_Light
            authority: ROLE_PERMANENCE_MANAGEMENT_LIGHT
          - groupSuffix: Perma_Gest_Global
            authority: ROLE_PERMANENCE_MANAGEMENT_GLOBAL
          #          - groupSuffix: PermaSAP_Gest
          #            authority: ROLE_MEMBER
          #          - groupSuffix: PermalNCSA_GEST
          #            authority: ROLE_MEMBER
          - groupSuffix: Personnes_Audit
            authority: ROLE_PERSON_AUDIT
          - groupSuffix: Personnes_Audit_Global
            authority: ROLE_PERSON_AUDIT_GLOBAL
          - groupSuffix: Personnes_Gest
            authority: ROLE_PERSON_MANAGEMENT
          - groupSuffix: Personnes_Gest_Global
            authority: ROLE_PERSON_MANAGEMENT_GLOBAL
          - groupSuffix: Vehicules_Audit
            authority: ROLE_VEHICLE_AUDIT
          - groupSuffix: Vehicules_Gest
            authority: ROLE_VEHICLE_MANAGEMENT
          - groupSuffix: Admin
            authority: ROLE_ADMIN
          - groupSuffix: AdminOperational
            authority: ROLE_ADMIN_OPERATIONAL
          - groupSuffix: Audit
            authority: ROLE_AUDIT
          - groupSuffix: DATAELS
            authority: ROLE_DCO_DATA_ELS
          - groupSuffix: DPVol
            authority: ROLE_DCO_VOLUNTEER
          - groupSuffix: Helpdesk
            authority: ROLE_HELPDESK
          - groupSuffix: Export_Gest
            authority: ROLE_MANAGEMENT_EXPORT
          - groupSuffix: DMLPagerGest
            authority: ROLE_PAGER_MANAGEMENT
          - groupSuffix: DCOCGOCoord
            authority: ROLE_DCOCGO_COORDINATOR
          - groupSuffix: DCOCGOAssistant
            authority: ROLE_DCOCGO_ASSISTANT
          - groupSuffix: _DCODCOAdministratif
            authority: ROLE_DCODCO_ADMINISTRATIF
          - groupSuffix: _INFSSecretariat
            authority: ROLE_INFS_SECRETARIAT
          - groupSuffix: _MoniteurJeunesP
            authority: ROLE_MONITEUR_JEUNES
          - groupSuffix: _CCIE
            authority: ROLE_CHEF_COMPAGNIE
          - groupSuffix: _directeur
            authority: ROLE_DIRECTOR
          - groupSuffix: _DMSCoordSAMU
            authority: ROLE_DMS_COORDINATOR_SAMU
          - groupSuffix: _Astreintes_Audit
            authority: ROLE_ASTREINTE_AUDIT
          - groupSuffix: _DAFCompta
            authority: ROLE_DAF_COMPTABILITE
          - groupSuffix: _FO_Gest
            authority: ROLE_FO_GEST
          - groupSuffix: _FO_Gest_Light
            authority: ROLE_FO_GEST_LIGHT
          - groupSuffix: DMSOSCSU
            authority: ROLE_DMS_OFFICIER_SANTE_CSU
          - groupSuffix: DCOCSUCds
            authority: ROLE_DCOCSU_CHEFDESALLE
          - groupSuffix: DCOCSUCadre
            authority: ROLE_DCOCSU_CADRE
          - groupSuffix: DCOCSUReferent
            authority: ROLE_DCOCSU_REFERENT
          - groupSuffix: DCOCSURegulateur
            authority: ROLE_DCOCSU_REGULATEUR
          - groupSuffix: DCOCSUDispatcher
            authority: ROLE_DCOCSU_DISPATCHER
          - groupSuffix: DCOCSUSuppIT
            authority: ROLE_DCOCSU_SUPPORT_IT
          - groupSuffix: INFS_Gestionnaire
            authority: ROLE_INFS_STAGE_GEST
          - groupSuffix: _INFSFormationSpecialisee
            authority: ROLE_INFS_FORM_SPEC
      technical-user: taPCGDISProdNotifications
      technical-password: "rs%UVk7Ckh?I*$12/aEz"
    business:
      notifier:
        update-person-informations:
          request-url: http://*************:8080/person_information_push_request
        update-service-plan:
          request-url: http://*************:8080/service_plan_update_request/{servicePlanId}
        update-vehicle-state:
          request-url: http://*************:8080/vehicle_update_state_push_request
        function-operational-url: http://*************:8080/function_operational_push_request
        refresh-entities-url: http://*************:8080/entities_push_request
        refresh-vehicles-url: http://*************:8080/vehicle_push_request
        generic-notifier-url: http://*************:8080/generic_notifier
        pager-program-request-url: http://*************:8080/pager_program_request
        resend-error-message-url: http://*************:8080/resend_error_message/{tecid}
        resend-error-message-limit-days: 7
        scheduler-properties: http://*************:8080/scheduler_properties/all
        base-url: http://*************:8080
        service-plan-send-filling-url: http://*************:8080/service_plan_send_filling
        prestations-allowances-properties:
          by-date-url: http://*************:8080/prestations/allowances/all
          by-entity-url: http://*************:8080/prestations/allowances/entities/{entityId}
          by-person-url: http://*************:8080/prestations/allowances/persons/{personId}
  export:
    service-plan-properties:
      prestations:
        template-url: "http://localhost:8080/templates/service-plan/%s/prestations/export?from=%s&to=%s"
        logout-url: "http://localhost:8080/logout"
        password: "FGF@ESA1iZ+AgtpBopPt"
        script: "/opt/cgdis/puppeteer/generate-pdf-template.js"
        temp-folder: "/opt/cgdis/puppeteer/temp"
        username: "taPCGDISProdFileExp"
  core:
    environment: PROD
  webapp:
    login-url: "https://portailcgdis.intranet.etat.lu/login"
management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: 'prometheus'
