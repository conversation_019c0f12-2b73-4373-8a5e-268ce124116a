.error-management-datepicker-filter {
  input {
    width: 96%;
  }
}

.informations-datepicker-filter {
  input {
    width: 80%;
  }
}

.informations-text-filter {
  input {
    width: 75%;
  }
}

.activity-datepicker-filter {
  input {
    @media (min-width: 400px) {
      width: 100%;
    }
    @media (max-width: 400px) {
      width: 78%;
    }
  }
}

.activity-text-filter {
  input {
    @media (min-width: 768px) {
      width: 76%;
    }
    @media (max-width: 768px) {
      width: 70%;
    }
  }
}

.number-filter {
  input {
    width: 100%;
  }
}

.mobile-filter {
  input {
    width: 93%;
  }
}

cgdis-portal-person-detail-edit {
  .informations-datepicker-filter {
    input {
      width: 94% !important;
    }
  }

  .informations-text-filter {
    input {
      width: 94% !important;
    }
  }

  .activity-datepicker-filter {
    input {
      width: 96% !important;
    }
  }

  .activity-text-filter {
    input {
      width: 96% !important;
    }
  }
}
