// Container widths
//
// Set the container width, and override it for fixed navbars in media queries.

@if $enable-grid-classes {
  .container {
    @include make-container();
    @include make-container-max-widths();
  }
}

// Fluid container
//
// Utilizes the mixin meant for fixed width containers, but with 100% width for
// fluid, full width layouts.

@if $enable-grid-classes {
  .container-fluid {
    @include make-container();
  }
}

// Row
//
// Rows contain and clear the floats of your columns.

@if $enable-grid-classes {
  .row {
    @include make-row();
    margin: 0;
    max-width: 100%;
  }

  // Remove the negative margin from default .row, then the horizontal padding
  // from all immediate children columns (to prevent runaway style inheritance).
  .no-gutters {
    margin-right: 0;
    margin-left: 0;

    > .col,
    > [class*='col-'] {
      padding-right: 0;
      padding-left: 0;
    }
  }
}

// Columns
//
// Common styles for small and large grid columns

@if $enable-grid-classes {
  @include make-grid-columns();
}

/**
Custom
 */

.page-sidebar {
  width: 100%;
}

@include media-breakpoint-up(lg) {
  $pageSidebarWidth: 25rem;
  .page-sidebar {
    width: $pageSidebarWidth;

    &.hideable {
      //transition: flex-basis 1000ms ease, width 1000ms ease;

      &.hideColumn {
        flex-basis: 0;
        padding: 0;
        overflow: hidden;
      }
    }
  }

  .page-content {
    width: calc(100% - $pageSidebarWidth - $grid-gutter-width);

    &.hideable {
      //transition: max-width 768ms ease-OUT;

      &.hideColumn {
        max-width: 97%;

        .planner__people {
          flex-basis: 19% !important;
        }
      }
    }
  }
}

@include media-breakpoint-up(xl) {
  .page-content {
    &.hideable {
      &.hideColumn {
        .planner__people {
          flex-basis: 15% !important;
        }
      }
    }
  }
}

@include media-breakpoint-down(md) {
  .button-open-date-picker {
    display: flex;
    margin-bottom: 2rem;
    margin-left: 2rem;

    h4 {
      margin-left: 1rem;
    }

    .icon-date-picker,
    .icon-delete {
      height: 2rem;
    }
  }

  .page-sidebar {
    &.hideable {
      transition:
        height 200ms ease,
        min-height 200ms ease;

      &.hideColumn {
        display: none;
        height: 0;
        min-height: 0;
      }
    }
  }
  .page-header-detail {
    display: none !important;
  }
}
