/*------------------------------------*\
    #FOOTER
\*------------------------------------*/
.footer {
  border-top: 0.1rem solid $c-gray-lightest;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  height: 5rem;
  color: $c-secondary;
  font-weight: 300;
  font-size: 1.2rem;

  .footer-nav {
    ul {
      @extend %unstyled-list;

      > li {
        display: inline-block;
        margin-bottom: 2rem;
        padding-right: 1rem;

        a {
          color: inherit;
        }
      }
    }
  }

  .copyright {
    padding-right: 0.5rem;
    padding-left: 1.5rem;
    color: $c-gray;
    @include media-breakpoint-down(sm) {
      padding-bottom: 1rem;
    }
  }

  @include media-breakpoint-up(sm) {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 19;
    background-color: #fff;

    .footer-nav {
      ul {
        li {
          display: inline-block;
          margin-bottom: 0;

          + li {
            margin-left: 3rem;
          }
        }
      }
    }
  }
}
