/*------------------------------------*\
    #HEADER
\*------------------------------------*/
.header {
  display: flex;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  align-items: center;
  z-index: 20;

  @media (min-width: 768px) {
    height: 6.1rem;
  }
  border-bottom: 0.1rem solid $c-gray-lightest;

  a {
    display: inline-block;
    margin-left: 1rem;
    text-decoration: none;

    &:hover,
    &:focus {
      opacity: 1 !important;
    }

    img {
      vertical-align: middle;
      width: 6rem;
      height: 6rem;
    }
  }

  &__title {
    flex-grow: 1;
    margin: 0 15% auto auto;
    padding: 0 1rem 1rem 1rem;
    max-height: 100%;
    font-size: 2.4rem;
    line-height: 5.5rem;
    text-align: center;

    span {
      margin-left: -1rem;
      outline: none;
      color: $c-primary;

      &:hover {
        cursor: pointer;
        color: #576397;
      }
    }

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .icon-logo {
    width: 28rem;
    height: 6rem;
  }

  &__logo {
    display: inline-block;
    vertical-align: middle;
    margin-right: 1rem;
  }

  @include media-breakpoint-up(sm) {
    &__title {
      text-align: center;
    }
  }
}

.generalMessages {
  display: flex;
  position: relative;
  top: 6rem;
  z-index: 10;
  background-color: orange;
  width: 100%;
  min-height: 5rem;
  color: white;

  span {
    width: 98%;
    text-align: center;
  }

  p {
    margin-bottom: 0;
  }
}

.backupActive {
  position: relative;
  top: 6rem;
  z-index: 10;
  width: 100%;
  min-height: 5rem;

  span {
    width: 98%;
    text-align: center;
  }

  p {
    margin-bottom: 0;
  }
}

.backup-message {
  padding-left: 4rem;
}

cgdis-portal-page-header {
  .section__title {
    display: inline-block;
  }

  .return-action {
    display: inline-flex;
    align-items: start;

    .section__title {
      margin-left: 10px;
      margin-right: 8px;
      margin-top: -8px;
    }

    cgdis-portal-icon {
      height: 22px;
      padding-left: 5px;
      background: $c-primary;
      cursor: pointer;

      .icon-back {
        fill: #ffffff;
      }
    }
  }
}
