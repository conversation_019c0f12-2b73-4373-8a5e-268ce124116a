/*------------------------------------*\
    #NAVIGATION
\*------------------------------------*/
.main-nav {
  position: fixed;
  top: 6rem;
  bottom: 0;
  left: -100%;
  z-index: 20;
  transition: left 0.4s ease;
  background: #fff;
  padding-top: 3rem;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  color: $c-gray;
  font-weight: 300;
  font-size: 1.8rem;

  .mat-expansion-panel-header {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  cgdis-portal-menu-item div {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .mat-expansion-panel-header.mat-expanded:focus,
  .mat-expansion-panel-header.mat-expanded:hover {
    background: #fafafa;
  }

  .mat-expansion-panel-body {
    background: #fafafa;
  }

  a {
    display: flex;
    color: inherit;
    line-height: 2.5rem;
    text-decoration: none;

    span.menu-title {
      margin-left: 1rem;
    }
  }

  &[aria-hidden='false'] {
    left: 0;
  }

  > ul {
    @extend %unstyled-list;

    > li {
      vertical-align: middle;
      padding: 1.5rem;

      svg {
        vertical-align: middle;
        margin-right: 1.5rem;
        width: 2.5rem;
        height: 2.5rem;
      }

      &.current {
        position: relative;
        background-color: #fafafa;
        color: $c-secondary-lighter;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          background-color: $c-primary;
          width: 0.3rem;
          content: '';
        }
      }

      > ul {
        @extend %unstyled-list;
        margin-top: 1.5rem;
        margin-left: 6rem;
        font-size: 1.4rem;

        li {
          padding: 0.5rem 0;
        }
      }
    }
  }

  @include media-breakpoint-up(sm) {
    max-width: 35rem;
  }
}

.main-nav-burger {
  flex-grow: 0;
  border: 0;
  background-color: $logo-color;
  padding: 0;
  width: 6rem;
  height: 6rem;
  color: #fff;
  @extend %unstyled-button;

  &__burger {
    display: block;
    position: relative;
    transform: translateY(50%);
    transition: border-color 250ms ease-in-out;
    margin: 0 auto;
    border: 0.1rem solid #fff;
    width: 33%;
    height: 0;

    &:before,
    &:after {
      position: absolute;
      right: -0.1rem;
      left: -0.1rem;
      transition:
        top 250ms ease-in-out,
        bottom 250ms ease-in-out,
        transform 250ms ease-in-out;
      border: 0.1rem solid #fff;
      content: '';
    }

    &:before {
      top: -0.8rem;
    }

    &:after {
      bottom: -0.8rem;
    }

    [aria-expanded='true'] & {
      border-color: transparent;

      &:before {
        top: -0.1rem;
        transform: rotate(-45deg);
      }

      &:after {
        bottom: -0.1rem;
        transform: rotate(45deg);
      }
    }
  }

  &__txt {
    @extend %sr-only;
  }
}

.main-nav-overlay {
  position: fixed;
  top: 6rem;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 19;
  transition: opacity 0.2s ease-in;
  background-color: rgba(0, 0, 0, 0.2);
  padding-top: 3rem;
  width: 100%;
}

.profile-nav__trigger {
  @media (max-width: 768px) {
    display: inline-flex;
    align-items: self-end;
  }
}

.profile-nav__picture {
  margin-right: 5px !important;
}

.profile-nav {
  position: absolute;
  top: 5px !important;
  right: 50px;
  flex-grow: 0;
  padding: 1rem;

  :focus {
    outline: none;
  }

  &__picture {
    display: inline !important;
    margin: 1rem auto auto auto;
    margin-top: -0.2rem !important;
    border-radius: 50%;
    width: auto;
    min-width: 3rem;
    height: auto;
    min-height: 3rem;
    max-height: 3.5rem;
  }

  &__trigger {
    @extend %unstyled-button;

    &[aria-expanded='true'] {
      .icon-chevron.-down {
        transform: rotate(0deg);
      }
    }

    svg {
      transition: transform ease-in 250ms;
      width: 1rem;
      height: 1rem;
      fill: #8790b9;
      @media (max-width: 400px) {
        @media (inverted-colors: inverted) {
          fill: #ffffff;
        }
      }
    }
  }

  &__text {
    margin-left: 1rem;
    color: #000000;
    font-size: 1.5rem;
  }

  &__menu {
    @extend %unstyled-list;
    @extend .-shadow;

    position: absolute;
    top: calc(100% + 0.1rem);
    right: 0;
    z-index: 1;
    border: 0.1rem solid $c-gray-lightest;
    background-color: #fff;
    min-width: 20rem;

    &[aria-hidden='true'] {
      display: none;
    }

    li {
      display: block;
      padding: 1rem;

      + li {
        border-top: 0.1rem solid $c-gray-lightest;
      }
    }

    a {
      color: #0b0b0b;
    }

    .is-active {
      color: $c-primary-red !important;
    }
  }

  &__logout {
    margin-left: auto;
    width: auto;
    height: auto;
  }

  @include media-breakpoint-down(md) {
    &__text {
      display: none;
    }
  }
}

.home-nav {
  @extend %unstyled-list;
  @extend .-shadow;
  justify-content: space-between;
  padding: 1rem 2.5rem;
  //align-items: center;

  > div {
    margin-bottom: 1em;
  }

  li {
    padding: 1rem;
    color: $c-txt;

    a {
      color: inherit;
    }

    svg {
      width: 2rem;
      height: 2rem;
    }
  }

  @include media-breakpoint-up(md) {
    display: flex;
  }
}

.inline-nav {
  @extend %unstyled-list;

  > li {
    display: inline-block;

    & + li {
      margin-left: 2rem;
      border-left: 0.1rem solid $c-gray-lightest;
      padding-left: 2rem;
    }

    &:not(.current) {
      a {
        color: $c-secondary-lighter;
      }
    }
  }
}
