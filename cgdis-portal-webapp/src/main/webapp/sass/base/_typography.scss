/* ==========================================================================
    Typography style based on KNACSS
    http://www.knacss.com/
========================================================================== */
html {
  font-size: 62.5%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}
/* Stupid IE11 - https://connect.microsoft.com/IE/feedback/details/816709/ie-11-calculating-font-sizes-wrong-when-setting-the-bodys-font-size-in-relative-units */
_:-ms-fullscreen,
:root {
  font-size: calc(1em * 0.625);
}

/*
* Titles
*/
h1,
h2,
h3,
h4,
h5,
h6,
.h1-like,
.h2-like,
.h3-like,
.h4-like,
.h5-like,
.h6-like {
  color: $c-secondary-lighter;
  font-weight: normal;
  line-height: 1.2;
  font-family: $ff-heading;

  small {
    display: block;
    color: $c-gray;
    font-weight: 300;
    font-size: 57%;
    font-family: $ff-base;
    @media (max-width: 400px) {
      @media (inverted-colors: inverted) {
        color: #ffffff;
      }
    }
  }
}

h1,
.h1-like {
  font-size: 3.5rem;
}

h2,
.h2-like {
  font-size: 2.8rem;
}

h3,
.h3-like {
  font-size: 2.3rem;
}

h4,
.h4-like {
  color: $c-gray;
  font-size: 1.6rem;
  font-family: $ff-base;
}

h5,
.h5-like {
  color: $c-gray;
  font-size: 1.5rem;
  font-family: $ff-base;
}

h6,
.h6-like {
  color: $c-gray;
  font-size: 1.4rem;
  font-family: $ff-base;
}

/*
* Alternate font sizing
*/
.smaller {
  font-size: 0.71em;
}

.small {
  font-size: 0.86em;
}

.big {
  font-size: 1.14em;
}

.bigger {
  font-size: 1.29em;
}

.biggest {
  font-size: 1.43em;
}

em,
.em,
i {
  font-style: italic;
}

.no-em {
  font-style: normal;
}

strong,
.strong,
b {
  font-weight: bold;
}

.no-strong {
  font-weight: normal;
}

sup,
sub {
  position: relative;
  vertical-align: 0;
}
sup {
  bottom: 1ex;
}
sub {
  top: 0.5ex;
}

/**
* Links
*/
a {
  transition: opacity ease-in 250ms;
  color: $c-link;
  text-decoration: none;

  &:hover,
  &:focus {
    opacity: 0.8;
  }
}
