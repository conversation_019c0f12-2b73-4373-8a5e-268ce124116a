/*
* Some additions to bootstrap reboot
*/

/*
* Selection
*/
::-moz-selection {
  background: $c-selection-background;
  color: $c-selection-color;
  text-shadow: none;
}

::selection {
  background: $c-selection-background;
  color: $c-selection-color;
  text-shadow: none;
}

/*
* Placeholder
*/
::-webkit-input-placeholder {
  opacity: 1;
  color: rgba($c-secondary-lighter, 0.8);
}

input:-moz-placeholder,
input::-moz-placeholder,
textarea:-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  color: rgba($c-secondary-lighter, 0.8);
}

/*
* Responsive elements
*/
img,
table,
td,
blockquote,
code,
pre,
textarea,
input,
video {
  max-width: 100%;
}

/*
* Form
*/
label {
  cursor: pointer;
}

button,
[type='button'],
[type='reset'],
[type='submit'] {
  cursor: pointer;
}

legend {
  border: 0;
}

textarea {
  min-height: 5em;
}

/*
* Remove browsers default style
*/

input,
button,
select,
textarea {
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-border-radius: 0;
}

select::-ms-expand {
  display: none;
}

/*
* Disable weird chrome outline
*/
.main,
.main-nav {
  &:focus {
    outline: 0;
  }
}
