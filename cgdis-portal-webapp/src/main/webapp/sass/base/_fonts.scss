/*------------------------------------*\
    #FONTS DECLARATION
    Use this fill to declare new fonts using @font-face
\*------------------------------------*/

/* This stylesheet generated by Transfonter (https://transfonter.org) on December 5, 2017 9:54 AM */

@font-face {
  font-style: italic;
  font-weight: normal;
  src: url('../../content/webfonts/Roboto-Italic.eot');
  src:
    local('Roboto Italic'),
    local('Roboto-Italic'),
    url('../../content/webfonts/Roboto-Italic.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/Roboto-Italic.woff2') format('woff2'),
    url('../../content/webfonts/Roboto-Italic.woff') format('woff'),
    url('../../content/webfonts/Roboto-Italic.ttf') format('truetype'),
    url('../../content/webfonts/Roboto-Italic.svg#Roboto-Italic') format('svg');
  font-family: 'Roboto';
}

@font-face {
  font-style: italic;
  font-weight: normal;
  src: url('../../content/webfonts/ArchivoNarrow-Italic.eot');
  src:
    local('Archivo Narrow Italic'),
    local('ArchivoNarrow-Italic'),
    url('../../content/webfonts/ArchivoNarrow-Italic.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/ArchivoNarrow-Italic.woff2') format('woff2'),
    url('../../content/webfonts/ArchivoNarrow-Italic.woff') format('woff'),
    url('../../content/webfonts/ArchivoNarrow-Italic.ttf') format('truetype'),
    url('../../content/webfonts/ArchivoNarrow-Italic.svg#ArchivoNarrow-Italic')
      format('svg');
  font-family: 'Archivo Narrow';
}

@font-face {
  font-style: italic;
  font-weight: 300;
  src: url('../../content/webfonts/Roboto-LightItalic.eot');
  src:
    local('Roboto Light Italic'),
    local('Roboto-LightItalic'),
    url('../../content/webfonts/Roboto-LightItalic.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/Roboto-LightItalic.woff2') format('woff2'),
    url('../../content/webfonts/Roboto-LightItalic.woff') format('woff'),
    url('../../content/webfonts/Roboto-LightItalic.ttf') format('truetype'),
    url('../../content/webfonts/Roboto-LightItalic.svg#Roboto-LightItalic')
      format('svg');
  font-family: 'Roboto';
}

@font-face {
  font-style: normal;
  font-weight: normal;
  src: url('../../content/webfonts/ArchivoNarrow-Regular.eot');
  src:
    local('Archivo Narrow Regular'),
    local('ArchivoNarrow-Regular'),
    url('../../content/webfonts/ArchivoNarrow-Regular.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/ArchivoNarrow-Regular.woff2') format('woff2'),
    url('../../content/webfonts/ArchivoNarrow-Regular.woff') format('woff'),
    url('../../content/webfonts/ArchivoNarrow-Regular.ttf') format('truetype'),
    url('../../content/webfonts/ArchivoNarrow-Regular.svg#ArchivoNarrow-Regular')
      format('svg');
  font-family: 'Archivo Narrow';
}

@font-face {
  font-style: normal;
  font-weight: normal;
  src: url('../../content/webfonts/Roboto-Regular.eot');
  src:
    local('Roboto'),
    local('Roboto-Regular'),
    url('../../content/webfonts/Roboto-Regular.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/Roboto-Regular.woff2') format('woff2'),
    url('../../content/webfonts/Roboto-Regular.woff') format('woff'),
    url('../../content/webfonts/Roboto-Regular.ttf') format('truetype'),
    url('../../content/webfonts/Roboto-Regular.svg#Roboto-Regular')
      format('svg');
  font-family: 'Roboto';
}

@font-face {
  font-style: normal;
  font-weight: 300;
  src: url('../../content/webfonts/Roboto-Light.eot');
  src:
    local('Roboto Light'),
    local('Roboto-Light'),
    url('../../content/webfonts/Roboto-Light.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/Roboto-Light.woff2') format('woff2'),
    url('../../content/webfonts/Roboto-Light.woff') format('woff'),
    url('../../content/webfonts/Roboto-Light.ttf') format('truetype'),
    url('../../content/webfonts/Roboto-Light.svg#Roboto-Light') format('svg');
  font-family: 'Roboto';
}
@font-face {
  font-style: italic;
  font-weight: 500;
  src: url('../../content/webfonts/Roboto-MediumItalic.eot');
  src:
    local('Roboto Medium Italic'),
    local('Roboto-MediumItalic'),
    url('../../content/webfonts/Roboto-MediumItalic.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/Roboto-MediumItalic.woff2') format('woff2'),
    url('../../content/webfonts/Roboto-MediumItalic.woff') format('woff'),
    url('../../content/webfonts/Roboto-MediumItalic.ttf') format('truetype'),
    url('../../content/webfonts/Roboto-MediumItalic.svg#Roboto-MediumItalic')
      format('svg');
  font-family: 'Roboto';
}

@font-face {
  font-style: normal;
  font-weight: 500;
  src: url('../../content/webfonts/Roboto-Medium.eot');
  src:
    local('Roboto Medium'),
    local('Roboto-Medium'),
    url('../../content/webfonts/Roboto-Medium.eot?#iefix')
      format('embedded-opentype'),
    url('../../content/webfonts/Roboto-Medium.woff2') format('woff2'),
    url('../../content/webfonts/Roboto-Medium.woff') format('woff'),
    url('../../content/webfonts/Roboto-Medium.ttf') format('truetype'),
    url('../../content/webfonts/Roboto-Medium.svg#Roboto-Medium') format('svg');
  font-family: 'Roboto';
}
