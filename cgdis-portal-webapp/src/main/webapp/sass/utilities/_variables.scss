@import 'utilities/functions';

/**
 * CONTENTS
 *
 * VARIABLES
 * Debug............ Debug mode
 * Colors........... All website colors
 * Themes........... Themes for clinic
 * Typography....... Font-family, font-size, line-height
 * Buttons.......... Buttons informations
 * Form............. Input, select style
 * Media Queries ... Breakpoint for responsive
 * Grid ............ Grid col and gutter
 */

/*------------------------------------*\
    #DEBUG
    True for activate debug mode
\*------------------------------------*/
$debug: false !default;

/*------------------------------------*\
    #COLORS
\*------------------------------------*/
$logo-color: rgb(44, 62, 123);
/* Brand colors */
$c-primary: $logo-color;
$c-primary-red: #c1002e;
$c-secondary: #252c36;
$c-tertiary: #06d6a0;
$c-quaternary: #596dff;

$c-professional: #c1002e;

$c-primary-lighter: #ff5964;
$c-secondary-lighter: #3d3b54;

/* 50 shades of greys */
$c-gray-clear: #fbfbfb;
$c-gray-mid-clear: #f5f5f5;
$c-gray-lightest: #e9eef5;
$c-gray-mid-light: #e6e6e6;
$c-gray-late-light: #999999;
$c-gray-disable: #a8a8a8;
$c-gray-lighter: #a1acbc;
$c-gray-light: #9e9aa7;
$c-gray: #93979d;
$c-gray-mid-dark: #666666;
$c-gray-dark: #333;
$c-gray-darker: #252c36;

$disabledColor: $c-gray-mid-dark;

/* Text color */
$c-txt: #333;

/* Body bg color */
$c-body: #fbfcfd;

/* Selection color */
$c-selection-color: #fff;
$c-selection-background: $c-primary;

/* Links colors */
$c-link: $c-primary-red;

/* Notifications colors */
$message-types: (
  error: (
    color: #d9534f,
    bg: #fcf0ec,
    border: #d9534f,
  ),
  warn: (
    color: #f0ad4e,
    bg: #fef7ee,
    border: #f0ad4e,
  ),
  success: (
    color: #5cb85c,
    bg: #f2f8f1,
    border: #5cb85c,
  ),
  info: (
    color: #5bc0de,
    bg: #f2f9fc,
    border: #5bc0de,
  ),
);

/*------------------------------------*\
    #TYPOGRAPHY
\*------------------------------------*/
$fs-base: 1.6em;
$ff-base:
  'Roboto',
  -apple-system,
  system-ui,
  BlinkMacSystemFont,
  'Segoe UI',
  'Helvetica Neue',
  Arial,
  sans-serif;
$ff-heading: 'Archivo Narrow', Georgia, 'Times New Roman', Times, serif;
$lh-base: 1.6;
$fw-base: 400;

$dt-font-weight: 700;

/*------------------------------------*\
    #FORMS
\*------------------------------------*/
$input-bg: #fff;
$input-radius: 0;
$input-padding: 0.5rem;
$input-border: 0 0 1px solid $c-gray-light 0;
$input-line-height: 2.4rem;
$mobile-input-line-height: 1rem;
$input-border-bottom-size: 0.1rem;
$border-width: 1px !default;
$input-btn-border-width: $border-width !default;
$input-border-width: $input-btn-border-width !default;
$input-height-border: $input-border-width * 2 !default;
$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`
$line-height-base: 1.428571429; // 20/14
$input-btn-line-height: $line-height-base !default;
$input-btn-padding-y: 0.375rem !default;
$input-height-inner: ($font-size-base * $input-btn-line-height) +
  ($input-btn-padding-y * 2) !default;
$input-height: calc(#{$input-height-inner} + #{$input-height-border}) !default;

/*------------------------------------*\
    #GRID
\*------------------------------------*/
$enable-grid-classes: true !default;

// Grid breakpoints
//
// Define the minimum dimensions at which your layout will change,
// adapting to different screen sizes, for use in media queries.

$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
) !default;

@include _assert-ascending($grid-breakpoints, '$grid-breakpoints');
@include _assert-starts-at-zero($grid-breakpoints);

// Grid containers
//
// Define the maximum width of `.container` for different screen sizes.

$container-max-widths: (
  sm: 700px,
  md: 900px,
  lg: 1100px,
  xl: 95%
) !default;

@include _assert-ascending($container-max-widths, '$container-max-widths');

// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-columns: 12 !default;
$grid-gutter-width-for-mobile: 15px !default;
$grid-gutter-width: 30px !default;
