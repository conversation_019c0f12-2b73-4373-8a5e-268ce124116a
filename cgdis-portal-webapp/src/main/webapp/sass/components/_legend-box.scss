.bottom-tab {
  .legend-box {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
    padding-top: 1rem;
    padding-bottom: 0;
  }
}

/*.calendar-mobile-container{
  .bottom-tab{
    .legend-box{
      margin-top: -1rem;
    }
  }
}*/

cgdis-portal-legend {
  @media (max-width: 500px) {
    display: inline-flex;
  }
}

.legend-box {
  @extend %clearfix;
  padding: 2rem;
  font-size: 1.4rem;
  text-align: left;

  @media (max-width: 768px) {
    span {
      display: flex;
      align-items: center;
    }
  }

  &__item {
    display: inline-block;
    margin-right: 0.5em;
    margin-left: 0.5em;
    &.-mobile {
      float: left !important;
      clear: left !important;
      line-height: 2rem;
    }
    /*@media (min-width: 1100px) {
          clear: right !important;
        }*/
  }

  &__image {
    display: inline-block;
    margin-right: 0.5em;
    margin-left: 0.5em;
    &.-mobile {
      float: left !important;
      clear: left !important;
    }
    /*@media (min-width: 1100px) {
        clear: right !important;
      }*/
  }

  &__title {
    display: inline-block;
    clear: right;
    &.-mobile {
      float: left !important;
      margin-bottom: 0;
      height: 2rem;
    }
  }

  &.-compact {
    padding: 0;
    text-align: left;

    [class^='icon-'] {
      width: 1.8rem;
      height: 1.8rem;
    }
  }

  &.-inline {
    padding: 1em;
    font-size: 1.2rem;
    @media (max-width: 500px) {
      display: flex;
      flex-wrap: wrap;
      padding: 2rem 0;
    }
  }

  @include media-breakpoint-up(sm) {
    &:not(.-inline) {
      .legend-box__image,
      .legend-box__item {
        float: left;
        clear: left;

        margin-right: 0.5em;

        .legend-box__title + .legend-box {
          margin-left: 0;
        }
      }

      .legend-box__title {
        float: left;
        clear: right;
      }
    }
    &.-legend-vertical {
      padding: 0;
      span {
        width: 100%;
        .legend-box__title {
          display: inline;
          float: inherit;
        }
        .legend-box__image,
        .legend-box__item {
          float: inherit;
          margin-left: 0.3em;
        }
      }
    }
  }
}

.-align-bottom-right {
  position: absolute;
  right: 0;
  bottom: -2.5rem;
}

dl.legend-box.-inline.-align-bottom-center {
  padding: unset;
  font-size: 1.2rem;
  text-align: center;
}

.-align-left {
  text-align: left;
}
