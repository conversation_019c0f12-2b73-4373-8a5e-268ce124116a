.vehicle {
  &__type &__id {
    font-weight: 500;
  }

  &__immat,
  &__desc {
    font-weight: 300;
  }

  @include media-breakpoint-down(sm) {
    &__select {
      + .select2 {
        margin: 1rem;
        max-width: calc(100% - 2rem);
      }
    }

    &__table {
      display: block;
      padding: 1rem;

      thead {
        display: none;
      }

      tbody {
        tr:not(.selected),
        th {
          display: none;
        }

        tr {
          td {
            display: block;
            padding: 1rem;

            &:before {
              display: block;
              content: attr(aria-label);
              color: $c-secondary-lighter;
              font-weight: normal;
              font-size: 1.6rem;
              font-family: $ff-heading;
            }

            &:not(:first-of-type) {
              border-top: 0.1rem solid #f6f6f6;
            }
          }
        }
      }
    }
  }

  @include media-breakpoint-up(md) {
    &__select {
      + .select2 {
        display: none;
      }
    }

    &__table {
      th,
      td {
        padding: 2rem;
      }

      tbody {
        tr {
          &:not(:first-child) {
            th,
            td {
              border-top: 0.1rem solid #f6f6f6;
            }
          }
        }
      }
    }
  }
}

#admin-vehicle-list-table-Id {
  .datatable-body-row {
    //height: 70px;
  }

  .chevron-details {
    svg {
      transform: rotate(90deg);
      height: 0.9rem;
    }
  }
}
