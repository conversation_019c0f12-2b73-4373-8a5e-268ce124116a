datatable-body-cell {
  word-break: break-word;
}

datatable-scroller {
  width: 100% !important;
}

cgdis-portal-total-information-prestation-details {
  .mobile-table {
    display: flex;
    margin: 1rem 0;
    border-bottom: 1px solid #e9eef5;

    tr {
      margin-bottom: 0 !important;
    }
  }

  table {
    @include media-breakpoint-up(md) {
      display: flex;
      padding-left: 5rem !important;
    }
  }
}

cgdis-portal-allowance-configuration-list {
  .configuration-list {
    padding-left: 4rem;
    @include media-breakpoint-up(md) {
      padding-left: 6.5rem;
    }
  }
}

datatable-body {
  datatable-selection {
    display: block;

    datatable-scroller {
      display: inherit !important;
      width: auto !important;
    }

    datatable-row-wrapper {
      display: inherit !important;
    }
  }
}

cgdis-portal-allowance-details,
cgdis-portal-allowance-search {
  @include media-breakpoint-down(md) {
    datatable-header-cell {
      cgdis-portal-datatable-datepicker-filter,
      cgdis-portal-datatable-number-filter,
      cgdis-portal-datatable-text-filter {
        display: none;
      }
    }
  }
}

cgdis-portal-allowance-configuration-list {
  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(md) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6),
      &:nth-child(8) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        flex-grow: 2;
      }
    }
  }
}

cgdis-portal-admin-service-plan-model-list {
  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2) {
        display: none !important;
      }

      &:nth-child(4) {
        flex-grow: 1;
      }

      &:nth-child(5) {
        flex-grow: 3;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(4) {
        flex-grow: 6;
      }
    }
  }
}

cgdis-portal-admin-position-template-edit-version-table {
  datatable-body-cell,
  datatable-header-cell {
    &:nth-child(4) {
      .datatable-body-cell-label,
      .datatable-header-cell-template-wrap {
        display: grid;
        text-align: center;
      }
    }
  }

  /*@include media-breakpoint-down(xl) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3) {
        flex-grow: 0.5;
      }
    }
  }*/

  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3) {
        flex-grow: 0.5;
      }

      &:nth-child(4) {
        flex-grow: 1.5;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-person-function-operational-table {
  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter {
        display: none;
      }
    }
  }
}

cgdis-portal-admin-function-operationnal-list,
cgdis-portal-admin-position-template-list,
cgdis-portal-admin-position-template-edit-version-table {
  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(2) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-admin-service-plan-edit-version-table {
  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4),
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(3) {
        flex-grow: 3;
      }

      &:nth-child(6) {
        flex-grow: 2;
      }

      &:nth-child(6) {
        .datatable-body-cell-label {
          display: grid;
          text-align: center;
        }
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        display: none !important;
      }

      &:nth-child(1) {
        flex-grow: 4;
      }

      &:nth-child(2),
      &:nth-child(3) {
        flex-grow: 2;
      }
    }
  }
}

cgdis-portal-admin-service-plan-team-table {
  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8),
      &:nth-child(9),
      &:nth-child(10) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-admin-entity-list {
  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(4),
      &:nth-child(5) {
        flex-grow: 1;
      }
    }
  }
  @include media-breakpoint-down(sm) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-number-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-admin-export-list {
  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        flex-grow: 4;
      }

      &:nth-child(2) {
        flex-grow: 2;
      }
    }
  }
  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(2) {
        flex-grow: 0.5;
      }

      &:nth-child(4) {
        flex-grow: 4;
      }

      &:nth-child(5) {
        flex-grow: 3;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4),
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(2) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-admin-public-holiday-list {
  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-number-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }
  }
}

cgdis-portal-admin-service-plan-list {
  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(3) {
        flex-grow: 2;
      }

      &:nth-child(4) {
        flex-grow: 3;
      }
    }
  }
  @include media-breakpoint-down(sm) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter,
      cgdis-portal-datatable-number-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2) {
        display: none !important;
      }

      &:nth-child(6) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(5),
      &:nth-child(6) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(4) {
        flex-grow: 2;
      }
    }
  }
}

cgdis-portal-admin-vehicle-list {
  @include media-breakpoint-down(md) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-text-with-null-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1),
      &:nth-child(3),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8) {
        display: none !important;
      }

      &:nth-child(2) {
        flex-grow: 0.5;
      }

      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1),
      &:nth-child(3),
      &:nth-child(6) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-audit-management-list-logas {
  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(3),
      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-datepicker-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-audit-management-list-service-plan {
  datatable-body-cell,
  datatable-header-cell {
    &:nth-child(1) {
      flex-grow: 1;

      .datatable-body-cell-label {
        justify-content: center;
      }
    }
  }

  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        flex-grow: 0.5;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3),
      &:nth-child(4) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-datepicker-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6) {
        display: none !important;
      }

      &:nth-child(5) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-error-management-list {
  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5),
      &:nth-child(6) {
        display: none !important;
      }

      &:nth-child(1) {
        flex-grow: 0.7;
      }

      &:nth-child(2),
      &:nth-child(3) {
        flex-grow: 2;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-datepicker-filter,
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(7) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-availabilities-information {
  @include media-breakpoint-down(md) {
    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(4),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8) {
        display: none !important;
      }

      &:nth-child(2) {
        flex-grow: 2;
      }

      &:nth-child(3),
      &:nth-child(5) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-diplomas {
  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(2) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-driver-license-information {
  @include media-breakpoint-down(xs) {
    .error-detail-container {
      display: flex !important;
    }

    datatable-header-cell {
      cgdis-portal-datatable-text-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-interventions-information {
  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3),
      &:nth-child(4) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    .error-detail-container {
      display: flex !important;
    }

    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }
    }
  }
}

cgdis-portal-managerial-occupations {
  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(9),
      &:nth-child(11) {
        display: none !important;
      }

      &:nth-child(3),
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8),
      &:nth-child(9),
      &:nth-child(10) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2),
      &:nth-child(4) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2) {
        display: flex !important;
      }

      /*&:nth-child(10) {
        display: none !important;
      }*/

      &:nth-child(1) {
        flex-grow: 0.5;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-aptitudes-information {
  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(4),
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(3) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-active-assignments-information {
  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(5) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    .error-detail-container {
      display: flex !important;
    }

    datatable-header-cell {
      cgdis-portal-datatable-text-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(3),
      &:nth-child(5) {
        display: none !important;
      }

      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-audit-management-list-model {
  datatable-body-cell,
  datatable-header-cell {
    &:nth-child(1) {
      flex-grow: 1;

      .datatable-body-cell-label {
        justify-content: center;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(4),
      &:nth-child(5) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(5) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-admin-general-message-list {
  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1),
      &:nth-child(5) {
        flex-grow: 6;
      }

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-up(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-admin-service-plan-model-edit-version-table {
  @include media-breakpoint-down(sm) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-number-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1),
      &:nth-child(2) {
        flex-grow: 6;
      }

      &:nth-child(3),
      &:nth-child(4) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-operational-dates {
  @include media-breakpoint-down(xs) {
    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(2),
      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-operational-grades {
  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(9),
      &:nth-child(10),
      &:nth-child(11),
      &:nth-child(12) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8),
      &:nth-child(9),
      &:nth-child(10),
      &:nth-child(11),
      &:nth-child(12) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(4) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-operational-occupations {
  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(9),
      &:nth-child(10),
      &:nth-child(11) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8),
      &:nth-child(9),
      &:nth-child(10),
      &:nth-child(11) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(4) {
        display: none !important;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(3) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-prestation-information {
  datatable-body-cell,
  datatable-header-cell {
    &:nth-child(6),
    &:nth-child(8),
    &:nth-child(9),
    &:nth-child(10) {
      .datatable-body-cell-label {
        display: grid;
        text-align: center;
      }
    }
  }

  @include media-breakpoint-down(lg) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5),
      &:nth-child(7) {
        display: none !important;
      }

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(7),
      &:nth-child(8) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(md) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6),
      &:nth-child(7) {
        display: none !important;
      }

      &:nth-child(4),
      &:nth-child(6),
      &:nth-child(7),
      &:nth-child(8) {
        flex-grow: 1;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(5),
      &:nth-child(8) {
        display: none !important;
      }

      &:nth-child(1) {
        flex-grow: 0.1;
      }

      &:nth-child(3) {
        flex-grow: 0.5;
      }

      &:nth-child(6) {
        flex-grow: 1.5;
      }

      &:nth-child(9),
      &:nth-child(10) {
        flex-grow: 0.4;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(2) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-restrictions-information {
  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-select-filter,
      .cgdis-portal-datatable-select-filter,
      cgdis-portal-datatable-datepicker-filter {
        display: none;
      }
    }

    .error-detail-container {
      display: flex !important;
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(1) {
        display: flex !important;
      }

      &:nth-child(3) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(4) {
        flex-grow: 1;
      }
    }
  }
}

cgdis-portal-totals-information {
  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3),
      &:nth-child(4) {
        .datatable-body-cell-label {
          display: grid;
          text-align: center;
        }
      }
    }
  }
}

cgdis-portal-admin-people-management-list {
  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(6) {
        display: none !important;
      }

      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5) {
        flex-grow: 1;
      }

      &:nth-child(1) {
        flex-grow: 0.5;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    datatable-header-cell {
      cgdis-portal-datatable-text-filter {
        display: none;
      }
    }

    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(5) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-admin-people-medical-information-list {
  cgdis-portal-cgdisdatatable {
    datatable-body-cell,
    datatable-header-cell {
      padding: 0.5em !important;
    }
  }

  @include media-breakpoint-down(lg) {
    cgdis-portal-cgdisdatatable {
      datatable-body-cell,
      datatable-header-cell {
        &:nth-child(1) {
          flex-grow: 2;
        }

        &:nth-child(2),
        &:nth-child(3) {
          flex-grow: 2;
        }

        &:nth-child(5) {
          flex-grow: 9;
        }

        &:nth-child(6) {
          display: none !important;
        }
      }
    }
  }
}

cgdis-portal-scheduler-management-list {
  @include media-breakpoint-down(xs) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3),
      &:nth-child(4) {
        display: none !important;
      }
    }
  }
}

cgdis-portal-admin-vehicle {
  @media (min-width: 768px) {
    .els-datacell {
      .form-item {
        display: flex;
        align-items: center;
      }

      .els-badge {
        margin-bottom: 0 !important;
      }

      .els-label {
        margin-top: 0 !important;
      }
    }
  }

  datatable-body-cell,
  datatable-header-cell {
    &:nth-child(3) {
      flex-grow: 1;
    }
  }
}

cgdis-portal-admin-entity {
  datatable-body-cell,
  datatable-header-cell {
    &:nth-child(1) {
      flex-grow: 1;
    }
  }

  @include media-breakpoint-down(sm) {
    datatable-body-cell,
    datatable-header-cell {
      &:nth-child(3) {
        display: none !important;
      }
    }
  }
}

.ngx-datatable {
  .datatable-header {
    .datatable-header-cell {
      .sort-btn {
        width: 1.1rem;
      }
    }

    .datatable-header-cell-wrapper {
      max-width: calc(100% - 1.1rem);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      svg:not(.datatable-header-icon__notabsolute) {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.text-ellipsis {
  padding-top: 1px;
  width: 100%;
  //width: 69%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis--no-padding {
  width: 100%;
  //width: 69%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.medical-multi-select .text-ellipsis {
  font-size: 8px;
}

.close-icon,
.search-icon {
  position: absolute;
  right: 2rem;
  bottom: 15%;
  border: none;
  background: transparent;
  color: white;
}

.filter-ellipsis {
  padding-right: 4em !important;
  text-overflow: ellipsis;
}

.cgdis-datatable-input-button-block {
  .close-icon {
    position: relative;
    @include media-breakpoint-down(md) {
      top: 0;
      right: 3rem;
    }
  }
}

.cgdis-portal-vehicule-els-history-list {
}

.els-label {
  margin-top: 1.5em;
  font-weight: normal !important;
}

.svgsmall {
  svg {
    width: 1.5rem;
  }
}

.els-badge {
  margin: 0.5em;
  border: 1px solid #757575 !important;
  border-radius: 0.6rem !important;
  padding: 1em 1.2em !important;
  font-size: small;
}

.badge {
  @media (min-width: 992px) {
    min-width: 48px;
    min-height: 41px;
  }
  @media (max-width: 992px) {
    min-width: 10px;
    min-height: 10px;
  }
}

.els-datacell {
  .form-item {
    margin-bottom: 0 !important;
  }

  .els-badge {
    vertical-align: middle;
    margin-bottom: 15px !important;
  }

  .els-label {
    margin-bottom: 0 !important;
  }
}

.els-history {
  max-height: 280px;
  font-size: small;
}

.scrollable-content_vehicle {
  padding-top: 1px;

  .ng-scrollbar-view {
    max-height: calc(280px);
    @media (max-width: 768px) {
      max-height: 100%;
    }
    padding-right: 0;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.badge-secondary {
  background-color: #8e9499;
}

.badge-success .badge-secondary {
  color: #fff;
}

.badge-light .badge-warning .badge-danger {
  color: #0b0b0b;
}

.ngx-datatable {
  table {
    .list-label {
      font-weight: 500;
      //padding-left: 1rem;
      font-size: 1.2em;

      &::after {
        content: ':';
      }
    }

    .list-value {
      font-weight: 100;
      font-size: 1.2em;

      p {
        margin-top: unset;
        margin-bottom: unset;
      }

      b {
        font-weight: bold;
      }

      @media (max-width: 768px) {
        padding-left: 1rem;
      }
    }

    ul {
      padding: 0;
      list-style-type: none;
    }

    td {
      vertical-align: top;
      height: unset;
      line-height: 2.5rem;
    }

    tr {
      background-color: transparent !important;
      @media (max-width: 768px) {
        display: grid;
        margin-bottom: 1rem;
      }
    }

    strong {
      font-weight: 500;
    }

    b {
      font-weight: 500;
    }
  }

  &.datable-with-expand-row {
    .datatable-body {
      .datatable-body-row {
        cursor: pointer;
      }
    }

  }
}

.ngx-datatable .datatable-body {
  .datatable-row-detail {
    @include media-breakpoint-down(md) {
      .error-detail-container {
        > :only-child {
          flex-grow: 1;
        }
      }
    }
  }


}

cgdis-portal-audit-management-list-allowance {
  .ngx-datatable .datatable-body .datatable-body-row {
    cursor: default;
  }
}

cgdis-portal-optional-backup-group-management-list {

  .ngx-datatable .datatable-body .datatable-body-row {
    cursor: pointer;
  }

}

cgdis-portal-optional-backup-group-detail {
  .ngx-datatable .datatable-body .datatable-body-row {
    cursor: default;
  }
}

cgdis-portal-datatable-entity-filter,
.alert-group-selector-wrapper {
  cgdis-portal-entity-filter,
  ngx-select {
    .ngx-select__toggle-buttons {
      width: 100%;

      //.dropdown-toggle {
      //  padding-right: 2rem !important;
      //}
    }

    .ngx-select__selected {
      &:has(.ngx-select__selected-single) {
        .ngx-select__toggle-buttons {
          width: auto;
        }
      }

      .ngx-select__toggle-buttons {
        width: 100%;

        .dropdown-toggle {
          padding-right: 2rem !important;
        }
      }
    }

    &.external {
      .ngx-select__placeholder {
        position: absolute;
        top: -3px;
      }

      .ngx-select__toggle {
        .dropdown-toggle {
          transform: rotate(0deg);
          background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgNTEyIDUxMiIKICAgaWQ9Imljb24tY2hldnJvbiI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE4NyI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxODUiIC8+CiAgPHBhdGgKICAgICBzdHlsZT0iZmlsbDojYzEwMDJlIgogICAgIGQ9Im0gMjU1Ljk0MjcyLDI5OC4wNTcyOCAtMTc0LC0xNjcgYyAtNCwtNCAtMTEsLTQgLTE2LDAgbCAtMzAsMzAgYyAtNSw0IC01LDExIC0xLDE2IGwgMjEzLDIwNCBjIDIsMiA1LDMgOCwzIDMsMCA2LC0xIDgsLTMgbCAyMTMsLTIwNCBjIDQsLTUgNCwtMTIgMCwtMTYgbCAtMzEsLTMwIGMgLTQsLTQgLTExLC00IC0xNiwwIHoiCiAgICAgaWQ9InBhdGg0MTgxIiAvPgo8L3N2Zz4K')
            no-repeat right 0.5rem top 50% !important;

          background-size: 1.6rem !important;
          padding-right: 1.5rem;
        }
      }

      .ngx-select__selected-single {
        color: $c-txt;
      }
    }
  }
}
