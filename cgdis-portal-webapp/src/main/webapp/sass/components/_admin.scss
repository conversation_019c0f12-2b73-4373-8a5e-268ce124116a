.functions-helper-text {
  color: $c-primary;
}

.form-item {
  &.displayFunction {
    label {
      color: $c-secondary-lighter;
      font-style: normal;
      font-weight: $inputFontWeight;
      font-stretch: normal;
      font-family: $ff-base;
    }
  }
}

.service-plan-version {
  cgdis-portal-link-with-icon-copy {
    position: absolute;
    top: 10px;
    right: 15px;
  }
}

.service-plan-model-version {
  cgdis-portal-link-with-icon-copy {
    position: absolute;
    top: 10px;
    right: 15px;
  }
  .service-plan-model-version__versionfield {
    .form-item {
      label {
        display: block;
      }
    }
  }
}

#admin-entity-list-table-Id {
  .datatable-body-row {
    height: 70px;
  }
}

cgdis-portal-admin-entity-edit {
  .parent {
    width: 100%;
    .child {
      @media (max-width: 768px) {
        width: 50%;
      }
      @media (min-width: 768px) {
        width: 33%;
      }
      display: inline-block;
      padding-right: 1em;
      padding-left: 1em;
    }
    .separator {
      @media (max-width: 768px) {
        border-top: 1px solid #e0e0e0;
      }
      @media (min-width: 768px) {
        border-left: 1px solid #e0e0e0;
      }

      background: #fbfbfb;
    }
    .margin-none {
      margin-right: 0;
      margin-left: 0;
    }
  }

  .accordion__panel {
    padding-bottom: 0;
  }

  .form-actions {
    margin-top: 2em;
  }
}

cgdis-portal-person-detail-edit,
cgdis-portal-optional-backup-group-detail {
  .datatable-header-cell:first-child {
    display: block !important;
  }
}
