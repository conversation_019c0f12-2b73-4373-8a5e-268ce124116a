.language-switcher {
  position: relative;

  &__trigger {
    @extend .btn;
    position: relative;
    transition: color 0.3s;
    background-color: transparent;
    padding: 1.5rem 2rem 1.5rem 1rem;
    font-weight: normal;

    svg {
      margin-left: 1rem;
      width: 1rem;
      height: 1rem;
      fill: #8790b9;
      transition: transform ease-in 250ms;
    }

    &[aria-expanded='true'] {
      .icon-chevron.-down {
        transform: rotate(0deg);
      }
    }

    &:hover,
    &:focus {
      outline: none;
      color: $c-primary;

      &:after {
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj4NCjxwYXRoIGZpbGw9IiNCREQ0MkEiIGQ9Ik0yNTYuMSwyMTQuMWwxNzQsMTY3YzQsNCwxMSw0LDE2LDBsMzAtMzBjNS00LDUtMTEsMS0xNmwtMjEzLTIwNGMtMi0yLTUtMy04LTNzLTYsMS04LDNsLTIxMywyMDQNCgljLTQsNS00LDEyLDAsMTZsMzEsMzBjNCw0LDExLDQsMTYsMEwyNTYuMSwyMTQuMXoiLz4NCjwvc3ZnPg0K');
      }
    }
  }

  &__list {
    @extend %unstyled-list;
    @extend .-shadow;

    position: absolute;
    top: calc(100% + 0.1rem);
    right: 0;
    z-index: 10;
    border: 0.1rem solid $c-gray-lightest;
    background-color: #fff;
    min-width: 100%;
    text-align: center;

    &[aria-hidden='true'] {
      display: none;
    }

    li {
      display: block;
      padding: 1rem;

      + li {
        border-top: 0.1rem solid $c-gray-lightest;
      }

      &:hover {
        cursor: pointer;
        color: $c-primary;
      }
    }
  }

  @include media-breakpoint-down(xs) {
    display: none;
  }
}
