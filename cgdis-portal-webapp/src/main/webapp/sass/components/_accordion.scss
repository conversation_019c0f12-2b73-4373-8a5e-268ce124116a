.accordion {
  &__group {
    @extend .panel;
    margin-bottom: 1rem;
    padding: 1rem 0;
    /*@media (min-width: 768px) {
      padding: 1.5rem 2rem;
    }*/

    &__form {
      padding: 1rem;
      //padding: 1rem 2rem;
      /*@media (min-width: 768px) {
        padding: 1.5rem 2rem;
      }*/
    }
  }

  &__trigger {
    position: relative;
    @extend %unstyled-button;
    padding: 0.5rem 3.2rem 0.5rem 1.2rem;
    width: 100%;
    color: $c-secondary-lighter;
    font-size: 1.8rem;
    font-family: $ff-heading;
    text-align: left;

    &:after {
      display: block;
      position: absolute;
      top: 50%;
      right: 1.2rem;
      transform: translateY(-50%);
      transition: transform ease-in 250ms;
      background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgNTEyIDUxMiIKICAgaWQ9Imljb24tY2hldnJvbiI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE4NyI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxODUiIC8+CiAgPHBhdGgKICAgICBzdHlsZT0iZmlsbDojYzEwMDJlIgogICAgIGQ9Im0gMjU1Ljk0MjcyLDI5OC4wNTcyOCAtMTc0LC0xNjcgYyAtNCwtNCAtMTEsLTQgLTE2LDAgbCAtMzAsMzAgYyAtNSw0IC01LDExIC0xLDE2IGwgMjEzLDIwNCBjIDIsMiA1LDMgOCwzIDMsMCA2LC0xIDgsLTMgbCAyMTMsLTIwNCBjIDQsLTUgNCwtMTIgMCwtMTYgbCAtMzEsLTMwIGMgLTQsLTQgLTExLC00IC0xNiwwIHoiCiAgICAgaWQ9InBhdGg0MTgxIiAvPgo8L3N2Zz4K')
        no-repeat;
      background-size: 1rem;
      width: 1rem;
      height: 1rem;
      content: '';
    }

    &[aria-expanded='true'] {
      &:after {
        transform: translateY(-50%) rotate(180deg);
      }
    }
  }

  &__panel {
    border-top: 0.1rem solid rgba(#757f8d, 0.2);
    padding-top: 1.25rem;

    /*@media (min-width: 768px) {
      padding: 3rem 2rem;
    }*/

    &.-no-title {
      border-top: 0;
      padding-top: 0;
    }

    &[aria-hidden='true'] {
      display: none;
    }
  }
}

.form {
  padding: 0;
}
