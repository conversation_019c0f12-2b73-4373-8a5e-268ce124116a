$tileBorderColor: #aaaaaa;
$tileBorderHoverColor: #222222;

cgdis-portal-tile {
  display: block;
  position: relative;
  border: 1px solid $tileBorderColor;
  background: #fff;
  height: 100%;
  //font-size: 1.8rem;
  min-height: 30rem;
  //width: 26rem;
  overflow: visible;
  font-weight: 300;
  text-align: center;

  &:hover {
    transition: border-color 0.3s;
    border-color: $tileBorderHoverColor;
  }

  .tile-container {
    height: 100%;
  }

  .tile-close-button {
    position: absolute;
    top: 0;
    right: 0;
    transition: background 0.3s;
    border: none;
    background-color: $c-primary-red;
    color: white;
    &:hover {
      background-color: $c-primary;
    }
  }

  .tile-content {
    padding: 1.5rem;
    width: 100%;
    height: 100%;
    //.form-item{
    //  > label {
    //    text-align: left;
    //  }
    //}
  }
}

.tile-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: center;
  margin-bottom: 15px;

  .tile-wrapper-item {
    template + .tile-group-item-arrows {
      display: none;
    }
    cgdis-portal-tile {
      padding-bottom: 2em;
    }
  }

  .tile-group-item-arrows {
    position: absolute;
    bottom: 1em;
    width: calc(100% - #{$grid-gutter-width});
    @include make-row();
    margin-right: 0;
    margin-left: 0;
  }
}

.tile-add-button {
  position: absolute;
  top: calc(50% - 3rem);
  left: calc(50% - 2.7rem);
  width: 6rem;
  height: 6rem;
  &:hover {
    cursor: pointer;
    color: $tileBorderHoverColor;
  }
  &-icon {
    width: 100%;
    height: 100%;
  }
}

svg.icon-add-fireman.tile-add-button-icon {
  color: $c-primary;
}
