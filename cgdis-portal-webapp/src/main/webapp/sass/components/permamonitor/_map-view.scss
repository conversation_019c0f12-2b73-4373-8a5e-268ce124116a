cgdis-portal-map-view {
  .map-legend {
    position: absolute;
    right: 0;

    .legend-box {
      display: flex;
      flex-direction: column;
    }
  }

  .highcharts-point {
    stroke-width: 25px !important;
    stroke: #000000 !important;
  }

  .highcharts-map-series .highcharts-point-hover {
    transition:
      fill 500ms,
      fill-opacity 500ms;
    fill-opacity: 1;
    stroke-width: 2px;
    cursor: pointer;
  }

  .map-view-content {
    display: flex;

    .chart-wrapper {
      position: relative;
      margin-right: auto;
      margin-left: auto;
      width: 100%;

      //@include media-breakpoint-down(lg) {
      //  height: 500px;
      //}

      .fade {
        transition: opacity 250ms ease-in-out; /* Unified duration for fade */
      }

      .fade-out {
        opacity: 0;
      }

      .fade-in {
        opacity: 1;
      }

      .drillup-button {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
      }
    }

    .information {
      display: block;
      margin-right: 20px;
      min-width: 350px;

      height: 500px;

      @include media-breakpoint-down(lg) {
        display: none;
      }

      .legend-box {
        padding: 0;
        margin-top: 20px;
        font-size: 12px;
      }

      .info-box {
        margin-bottom: 16px;
        box-shadow: 0 0.3rem 1.2rem 0.3rem rgba(162, 195, 229, 0.3);
        border-radius: 0.4rem;
        background: #ffffff;
        padding: 20px;
        min-height: 250px;
        color: $c-primary;

        .title {
          color: #12326e;
          font-weight: bold;
          margin-bottom: 16px;
          text-transform: uppercase;
        }

        .service-plan-row {
          display: flex;
          font-size: 13px;
          line-height: 20px;

          .legend-box__item {
            margin-left: 0;

            &.status {
              font-size: 25px;
              line-height: 16px;
            }
          }

          .service-plan-row-text {
            display: flex;
            gap: 5px;
          }
        }
      }
    }
  }
}

cgdis-portal-map-view-mobile-popup {
  .title {
    color: #ffffff;
    font-weight: bold;
  }

  .info-box {
    color: $c-primary;

    .title {
      color: #12326e;
      font-weight: bold;
      text-transform: uppercase;
    }

    .service-plan-row {
      display: flex;
      font-size: 12px;

      .legend-box__item {
        margin-left: 0;

        &.status {
          font-size: 18px;
          line-height: 17px;
        }
      }

      .service-plan-row-text {
        display: flex;
        gap: 5px;
      }
    }
  }
}
