cgdis-portal-permamonitor {
  .mat-mdc-tab-links {
    transition: none;
    margin-bottom: 20px;
    width: fit-content;

    .mat-mdc-tab-link {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
      height: 30px;

      &:last-child {
        border-right: none;
      }

      &:first-child {
        border-left: none;
      }

      .mdc-tab__ripple::before {
        background-color: transparent;
        color: #000000;
      }

      &:hover {
        .mdc-tab__ripple::before {
          opacity: 1;
          background-color: transparent;
          color: #000000;
        }

        .mdc-tab__text-label {
          color: #000000;
        }
      }

      &:focus {
        .mdc-tab__text-label {
          color: $c-primary;
        }
      }
    }

    .mat-mdc-focus-indicator {
      opacity: 1;
      background-color: transparent;
      text-decoration: none;
    }

    .mat-mdc-focus-indicator:hover {
      opacity: 1;
      background-color: transparent;
      text-decoration: none;
    }

    .mdc-tab__text-label {
      color: #000000;
      font-weight: normal;
    }

    .mdc-tab-indicator--active,
    .mdc-tab--active {
      color: $c-primary;

      .mdc-tab__text-label {
        color: $c-primary;
        font-weight: bold;
      }

      &:hover {
        .mdc-tab__text-label {
          color: $c-primary;
        }
      }
    }

    .mdc-tab-indicator__content--underline {
      display: none;
    }
  }
}
