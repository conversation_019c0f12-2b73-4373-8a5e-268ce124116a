cgdis-portal-pds-config-table {
  .ngx-datatable.fixed-row
    .datatable-scroll
    .datatable-body-row
    .datatable-body-cell,
  .ngx-datatable.fixed-row
    .datatable-scroll
    .datatable-body-row
    .datatable-body-group-cell {
    overflow: visible;
  }

  .datatable-body-cell {
    .datatable-body-cell-label {
      overflow: visible;
    }
  }

  .ngx-select {
    min-width: 100px;

    .ngx-select__toggle {
      height: 25px;

      &.btn {
        padding: 5px !important;
      }
    }

    .ngx-select__choices {
      height: unset;
      //overflow: visible;
    }
  }
}
