cgdis-portal-poj-configuration-table {
  .no-cis-note {
    display: flex;
    justify-content: center;
    padding-top: 150px;
    padding-bottom: 150px;
  }

  .poj-table {
    display: flex;

    .poj-table-row {
      width: 100%;

      &:first-child {
        .poj-table-cell {
          border-left: 1px solid $c-gray;

          &.poj-table-header {
            border-top-left-radius: 0.4rem;
            border-left: 1px solid $c-primary;
          }

          &:last-child {
            border-bottom-left-radius: 0.4rem;
          }
        }
      }

      &:last-child {
        .poj-table-cell {
          border-right: 1px solid $c-gray;

          &.poj-table-header {
            border-top-right-radius: 0.4rem;
            border-right: 1px solid $c-primary;
          }

          &:last-child {
            border-bottom-right-radius: 0.4rem;
          }
        }
      }

      .poj-table-cell {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 35px;
        border-left: 1px solid $c-gray;

        &.time-cell {
          border-bottom: 0.1rem solid rgba(117, 127, 141, 0.2);
        }

        &:nth-child(2n + 1) {
          background: #f7f7f7;
        }

        &.highlight-row {
          background: $c-primary;
          color: #ffffff;
        }

        &.poj-table-header {
          border-left: 1px solid #ffffff;

          flex-direction: column;
          background: $c-primary;
          height: 50px;
          color: #ffffff;

          font-weight: $dt-font-weight;
          font-size: 1.3rem;
          text-transform: uppercase;

          &:last-child {
            border-right: 1px solid #ffffff;
          }

          &.time-header {
            display: flex;
            flex-direction: row;
            gap: 5px;
          }

          .day-header-title {
            display: block;
            border-bottom: 1px solid #ffffff;
            width: 100%;
            height: 25px;
            text-align: center;
          }
        }

        .values {
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;

          .value {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            text-align: center;
            background: transparent;
            z-index: 10;
            border-left: 2px solid transparent;
            border-right: 2px solid transparent;

            &.highlight-row {
              border-top: 2px solid $c-primary;
              border-bottom: 2px solid $c-primary;
            }

            &.highlight-col {
              border-left: 2px solid $c-primary;
              border-right: 2px solid $c-primary;
            }

            &.selection {
              border: none;
            }

            &.with-delta {
              font-weight: bold;
            }

          }

          &.header-title {
            height: 25px;

            .crit {
              border-left: 1px solid #ffffff;
              border-right: 1px solid #ffffff;
            }
          }
        }

        &:last-child {
          border-bottom: 1px solid $c-gray;
        }
      }
    }
  }
}

cgdis-portal-permamonitor-poj-configuration {
  .page-actions {
    padding-top: 10px;
    padding-bottom: 10px;
    display: flex;
    justify-content: center;
  }
}
