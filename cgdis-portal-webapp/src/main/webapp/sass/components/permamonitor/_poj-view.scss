cgdis-portal-poj-view {
  .poj-view-table-value {
    font-weight: bold;

    &.poj-view-table-value-unacceptable {
      color: #f00;
    }

    &.poj-view-table-value-critical {
      color: #f7a35c;
    }

    &.poj-view-table-value-minimal {
      color: #f7a35c;
    }

    &.poj-view-table-value-optimal {
      color: #06b535;
    }
  }

  .highcharts-axis-line {
    stroke: #000000;
  }

  .table-graph {
    display: flex;
    column-gap: 20px;
    align-items: center;
    //height: 580px;

    .poj-table {
      width: 40%;
    }

    .graph-panel {
      display: flex;
      align-items: center;
      width: 60%;
      height: 100%;
    }
  }

  @include media-breakpoint-down(md) {
    .table-graph {
      flex-direction: column;
      height: unset;

      .poj-table,
      .graph-panel {
        width: 100%;
      }
    }
  }

  .highcharts-series-inactive {
    opacity: 1;
  }

  .graph {
    width: 100%;
  }

  .graph-panel {
    &.group7day {
      display: flex;
      flex-direction: row;
      width: 100%;

      .graph {
        width: 50%;
      }

      @include media-breakpoint-down(md) {
        flex-direction: column;
        .graph {
          width: 100%;
        }
      }
    }
  }

  cgdis-portal-cgdisdatatable {
    ngx-datatable {
      overflow: hidden !important;
      //width: 104% !important;
    }

    .datatable-body-cell,
    .datatable-header-cell {
      min-height: unset;
      min-width: 50px;
    }

    .datatable-row-wrapper:nth-child(7) {
      border-top: 4px solid $c-gray;
      border-bottom: 4px solid $c-gray;
    }
  }

  cgdis-portal-poj-view-table {
    display: block;
    height: 550px;

    .datatable-header-cell-wrapper {
      max-width: 100% !important;
      text-overflow: unset !important;
    }
  }
}
