cgdis-portal-live-view {
  .live-data {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .live-row {
      display: flex;
      flex-direction: column;
      border-radius: 2rem;
      padding: 12px;

      .time {
        margin-left: auto;
      }
    }
  }
}

cgdis-portal-live-view-popup {
  .info-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
    color: $c-primary;
    font-weight: normal;
    font-size: 14px;

    .label {
      color: #0055a3;
    }

    .data {
      color: $c-txt;
    }
  }

  .backup-group {
    margin-left: -5px;
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
    fill: $c-primary-red;
    color: $c-primary-red;

    &.inactive {
      fill: #0055a3;
      color: #0055a3;
    }
  }
}
