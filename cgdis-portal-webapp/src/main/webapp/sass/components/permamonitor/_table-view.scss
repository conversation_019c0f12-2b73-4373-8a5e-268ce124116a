cgdis-portal-permamonitor-table-body {
  .table-view-body-content {
    display: flex;

    .information {
      margin-right: 20px;
      width: 300px;

      .legend-box {
        padding: 0;
        margin-top: 20px;
        font-size: 12px;
      }

      .info-box {
        margin-bottom: 16px;
        box-shadow: 0 0.3rem 1.2rem 0.3rem rgba(162, 195, 229, 0.3);
        border-radius: 0.4rem;
        background: white;
        padding: 20px;
        min-height: 250px;
        color: $c-primary;

        .title {
          color: #12326e;
          font-weight: bold;
          text-transform: uppercase;
        }

        .info-group {
          display: flex;
          flex-direction: column;
          margin-bottom: 8px;
          font-weight: normal;
          font-size: 14px;

          .date {
            font-size: 12px;
          }

          .label {
            color: #0055a3;
          }

          .data {
            color: $c-txt;
          }
        }

        .backup-group {
          margin-left: -5px;
          font-weight: bold;
          font-size: 12px;
          text-transform: uppercase;
          fill: $c-primary-red;
          color: $c-primary-red;

          &.inactive {
            fill: #0055a3;
            color: #0055a3;
          }
        }
      }
    }

    .chart-wrapper-table {
      width: calc(100% - 10px);

      .highlight-bar-hover {
        opacity: 1 !important;
        stroke-width: 20px !important;
        stroke: #ff5733 !important; /* Choose the hover color */
      }

      .default-bar {
        opacity: 0.7;
        transition: opacity 0.2s ease-in-out;
      }

      .sticky-x-axis {
        position: sticky;
        top: 0;
        z-index: 10;

        .highcharts-background {
          fill: $c-primary !important;
        }

        .highcharts-tick {
          stroke: #ffffff;
        }
      }

      .ng-scroll-content {
        padding-right: 10px !important;
      }

      cgdis-portal-scroll {
        .highcharts-series-inactive {
          opacity: 0.3;
          transition: opacity 50ms;
        }

        .non-hovered {
          opacity: 0.3;
          transition: opacity 50ms;
        }

        .highcharts-plot-line {
          stroke: $c-primary;
          opacity: 0.6;
          stroke-width: 4px;
          pointer-events: none;
        }
      }
    }
  }
}
