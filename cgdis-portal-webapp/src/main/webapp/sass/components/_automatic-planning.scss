@use 'sass:math';
.automatic-planning {
  padding: math.div($grid-gutter-width, 2);
  overflow: hidden;

  &__tab {
    &[role='tab'],
    &[role\.tab='tab'] {
      padding: 0;
      font-size: 1.8rem;

      &:hover {
        cursor: default;
      }
    }
    &:not([role='tab']),
    &:not([role\.tab='tab']) {
      display: none;
    }
  }

  @include media-breakpoint-up(md) {
    position: relative;
    padding: 0;

    .tabs__content {
      .-placeholder {
        .planner__people {
          &:before {
            display: none;
          }
        }
      }
    }

    &__tab {
      display: flex;
      flex-direction: row;
      min-height: 6rem;
      &[role='tab'][aria-selected='true'],
      &[role\.tab='tab'][aria-selected='true'] {
        outline: none;
        box-shadow: none;
        &:before {
          display: none;
        }
      }
      &:not([role='tab']),
      &:not([role\.tab='tab']) {
        display: flex;
        min-height: 5rem;
      }
    }
  }
}

.tab-header {
  @extend .planner__people;

  &__icon {
    display: inline-block;
    margin-right: 3rem;
    padding: 0;
    width: 3.5rem;
    line-height: 3.5rem;
    text-align: center;
  }

  &__text {
  }

  &:first-child {
    &:before {
      display: none;
    }
  }

  @include media-breakpoint-up(md) {
    display: flex;
    flex: 1 1 0;
    justify-content: center;
    align-items: center;
    max-width: none;

    &:before {
      display: none;
    }

    &__icon {
      display: block;
      margin: auto;
      width: 1.2rem;
      height: 1.2rem;
      line-height: 1;
    }

    &:first-child {
      .tab-header__text {
        display: none;
      }
    }

    .-placeholder & {
      background-color: $c-primary;
      text-align: center;

      .tab-header__icon {
        display: inline-block;
        margin-right: auto;
        width: 1.2rem;
        height: 1.2rem;
        color: #fff;
      }
    }
  }
}
