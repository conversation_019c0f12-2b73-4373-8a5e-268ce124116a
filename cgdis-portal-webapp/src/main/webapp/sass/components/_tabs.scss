.tabs {
  background-color: #fff;

  &__header {
    display: flex;
    position: relative;
    justify-content: left;
    align-items: center;
  }

  &__next,
  &__prev {
    @extend %unstyled-button;
    flex-shrink: 0;
    z-index: 2;
    background-color: transparent;
    width: 3rem;
    height: 3rem;
    color: $c-primary;
  }

  [role='tablist'] {
    position: relative;
    @extend %unstyled-list;
    display: flex;
    flex-grow: 1;
    flex-direction: row;
    z-index: 1;
    margin: 0 auto;
    overflow-x: hidden;

    .tablist-items-row {
      width: 100%;
    }
  }
  [role='tab'],
  [role\.tab='tab'] {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: 100%;
    padding: 1.7rem;
    font-family: $ff-heading;
  }

  &:not(._vertical) {
    [role='tab']:not(.tabs-not-clickable),
    [role\.tab='tab']:not(.tabs-not-clickable) {
      cursor: pointer;

      &:focus,
      &:hover {
        background: transparent;
        color: $c-primary;
      }
    }
  }

  [role='tabpanel'] {
    @extend %unstyled-list;
    position: relative;

    &[aria-hidden='true'] {
      display: none;
    }
  }

  @include media-breakpoint-up(lg) {
    &:not(._vertical) {
      [role='tab'],
      [role\.tab='tab'] {
        flex-basis: 25%;

        &[aria-selected='true'] {
          position: relative;
          outline: none;
          box-shadow: 0 0 6.6px 1.4px rgba(194, 192, 180, 0.55);
          color: $c-primary;

          &:before {
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            background: $c-primary;
            height: 0.7rem;
            content: '';
          }
        }
      }
    }

    &._vertical {
      display: flex;
      flex-direction: row;
      justify-content: left;
      align-items: stretch;
      padding-bottom: 2rem;
      width: 100%;

      [role='tablist'],
      [role\.tab='tablist'] {
        display: flex;
        flex: 0 0 auto;
        flex-direction: column;
        overflow-x: initial;
      }

      [role='tab'],
      [role\.tab='tab'] {
        flex-basis: auto;
        align-items: center;
        cursor: default;
      }

      .tabs__content {
        width: 100%;
        overflow-x: auto;
      }
    }
  }

  @include media-breakpoint-up(xl) {
    &__next,
    &__prev {
      display: none;
    }

    &:not(._vertical) {
      [role='tab'],
      [role\.tab='tab'] {
        flex-basis: 0;
      }
    }

    [role='tablist'] {
      overflow-x: initial;
    }
  }
}

.cgdis-tabs-headers {
  .owl-popup {
    .owl-carousel {
      padding: 0;
    }
  }

  .cgdis-tabs-headers__list {
    margin: 0;
    padding: 0;
    overflow: hidden;
    list-style-type: none;

    li {
      float: left;

      &:first-child {
        button {
          padding-left: 0rem;
        }
      }

      button {
        border: none;
        background: none;
        padding-right: 1.5rem;
        padding-left: 1.5rem;
        color: #000000;
      }

      &.separator {
        border-right: 1px solid rgba(0, 0, 0, 0.1);

        &:last-child {
          border-right: none;
        }

      }

    }

    .cgdis-tabs-headers__list-selected {
      color: $c-primary;
      font-weight: bold;
    }

    .seperator {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}
