/*------------------------------------*\
    #ICONS - SVG SPRITE
\*------------------------------------*/
[class^='icon-'],
[class*=' icon-'] {
  width: 2.4rem;
  height: 2.4rem;
  line-height: 1;
  fill: currentColor;
  vertical-align: middle;
  &.-small {
    width: 1.4rem;
    height: 1.4rem;
  }
}

.icon-chevron {
  &.-left {
    transform: rotate(-90deg);
  }
  &.-right {
    transform: rotate(90deg);
  }
  &.-down {
    transform: rotate(180deg);
  }
  &.-up {
  }
}

.icon-plus {
  &.-delete {
    transform: rotate(-45deg);
  }
}

.icon-valider {
  color: map-get(map-get($message-types, 'success'), 'color');
}

.warning-icon {
  padding-right: 1rem;
}

// Apply this class to an icon if you want its stroke to be currentColor
// and its path definition does not have a hardcoded stroke color.
.icon-stroke-current-color {
  stroke: currentColor;
}
