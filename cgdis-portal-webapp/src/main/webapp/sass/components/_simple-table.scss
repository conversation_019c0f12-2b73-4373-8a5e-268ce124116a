@mixin listEmpty() {
  padding-bottom: 2em;
  font-weight: 500;
  line-height: 275px;
  text-align: center;
}

@mixin simpleTableBorder() {
  border: 1px solid $c-primary;
}

@mixin simpleTableHeaderRow() {
  background-color: $c-primary;
  color: #fff;
  font-weight: normal;
  font-size: 1.3rem;
  font-family: $ff-heading;
  text-transform: uppercase;
  white-space: nowrap;
}

@mixin simpleTableHeaderColumn() {
  font-weight: 700;
  @include simpleTableHeaderAndColumn();
}

@mixin simpleTableHeaderAndColumn() {
  margin: 0;
  padding: 0.5em 1em;
  font-size: 0.875em;

  @media (max-width: 576px) {
    padding: 0.5em;
  }
}

@mixin simpleTableColumnTitle() {
  padding-top: 1rem;
  font-size: 1.3rem;
}

/**
NG2-SMART-TABLE
(overrode style)
 */
th.ng2-smart-th.type {
  padding: 2rem;
  color: white;
}

a.ng2-smart-sort-link.sort {
  color: white;
  font-size: 1.3rem;
  text-decoration: none;
}

ng2-smart-table-cell,
datatable-body-cell {
  padding: 2rem;
  font-size: 1.1em !important;
}

datatable-body-cell {
  @include simpleTableHeaderAndColumn();
}

ul.ng2-smart-pagination.pagination {
  font-size: initial;
}

.pagination > li > a,
.pagination > li > span {
  border: none;
}

.pagination > .active > a,
.pagination > .active > a:hover,
.pagination > .active > span,
.pagination > .active > span:hover {
  background-color: transparent;
  color: $c-primary;
}

.pagination > li > a,
.pagination > li > span {
  color: black;
}

.page-item.active .page-link {
  border-color: transparent;
  background-color: transparent;
}

a.ng2-smart-page-link {
  span {
    font-size: initial;
  }
}

input-filter {
  color: black;
}

nav.ng2-smart-pagination-nav {
  margin-top: 1em;
  margin-bottom: 1em;
  width: 100%;
  text-align: center;
}

li.ng2-smart-page-item.page-item {
  padding: 5px;
}

cgdis-portal-cgdisdatatable {
  ep-datatable {
    &.ep-datatable-row-clickable {
      .datatable-body {
        .datatable-body-row {
          cursor: pointer;
        }
      }
    }

    cgdis-portal-form {
      [type='text'] {
        border-bottom: 0.1rem solid rgba(117, 127, 141, 0.2) !important;
      }
    }
  }
}

ep-datatable {
  &.hide-results-count {
    .page-count {
      display: none;
    }
  }

  tr:nth-child(2n) {
    background: rgba(255, 0, 0, 0.05);
  }

  select-filter {
    color: black;
  }

  ng2-st-column-title,
  .datatable-header-cell {
    @include simpleTableColumnTitle();
  }

  //th.ng2-smart-th.id {
  //  padding-top: 1rem;
  //  padding-bottom: 1rem;
  //  padding-left: 1rem;
  //}

  ul.ng2-smart-pagination.pagination {
    margin-bottom: 0;
  }

  li.ng2-smart-page-item.page-item.active {
    background: $c-primary;
  }

  .pagination > .active > span {
    color: white;

    :hover {
      cursor: pointer;
    }
  }

  span.ng2-smart-page-link.page-link:hover {
    color: white;
  }

  ul.ng2-smart-pagination.pagination {
    margin-bottom: 0;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  /*Hide placeholder*/
  ::-webkit-input-placeholder {
    color: transparent; /* WebKit browsers */
  }

  :-moz-placeholder {
    color: transparent; /* Mozilla Firefox 4 to 18 */
  }

  ::-moz-placeholder {
    color: transparent; /* Mozilla Firefox 19+ */
  }

  :-ms-input-placeholder {
    color: transparent; /* Internet Explorer 10+ */
  }

  thead,
  datatable-header {
    @include simpleTableHeaderRow();
    border-top-right-radius: 0.4rem;

    border-top-left-radius: 0.4rem;

    .-sortable {
      &:after {
        display: inline-block;
        vertical-align: top;
        padding-left: 1rem;
        width: 1em;
        content: '▴▾';
        font-size: 0.7em;
        line-height: 1em;
        text-align: center;
        white-space: normal;
        word-break: break-all;
      }
    }

    select,
    input {
      outline: none;
      border-radius: 4px;
      color: black;
    }
  }

  tbody,
  .datatable-body {
    font-size: 1.2rem;

    .datatable-row-odd {
      background: rgba(0, 0, 0, 0.03);

      & + div.datatable-row-detail {
        background: rgba(0, 0, 0, 0.03);
      }
    }

    tr {
      &:nth-child(2n) {
        background: rgba(0, 0, 0, 0.03);
      }

      &:hover {
        cursor: pointer;
      }
    }
  }

  [_nghost-c3] .ng2-smart-row.selected[_ngcontent-c3] {
    background: rgba(193, 0, 46, 0.1);
  }

  table,
  ngx-datatable {
    //@include simpleTableBorder();
    datatable-body-cell {
      display: flex !important;
      align-items: center;
      min-height: 5em;

      .datatable-body-cell-label {
        display: flex;
        flex: 1;
        align-items: center;
        height: 100%;

        p {
          margin-bottom: 0;
        }
      }
    }
  }

  [type='date'],
  [type='time'],
  [type='datetime'],
  [type='text'],
  [type='password'],
  [type='email'],
  [type='search'],
  [type='tel'],
  [type='number'],
  ngx-select {
    border: 0 !important;
    background-color: $input-bg;
    height: 34px;
    max-height: 34px;
    font-style: normal;
    font-weight: $inputFontWeight;
    font-stretch: normal;
    line-height: $input-line-height;
    font-family: $ff-base;

    a,
    .dropdown-item {
      font-weight: 300;
      font-size: 1.4rem;
    }

    div {
      vertical-align: middle;
      max-height: 34px;

      .ngx-select__selected-plural.btn.btn-default.btn-secondary.btn-xs {
        max-height: 28px;
      }
    }

    .ngx-select__toggle,
    input {
      padding: 0.5rem !important;
    }

    .ngx-select__toggle.btn {
      font-weight: $inputFontWeight;
    }

    .ngx-select__toggle {
      &.btn {
        padding: 0 !important;
      }
    }

    select {
      &.-inline {
        padding-top: 0;
      }
    }

    .dropdown {
      position: relative;
      border: none;
    }

    .float-right {
      float: right !important;
    }

    i {
      float: right !important;
    }
  }

  input {
    margin: 0 !important;
    border: 0 !important;
    border-radius: 2px !important;
    padding: 5px !important;
    height: 2em !important;
    max-height: 2em !important;
  }

  .page-count {
    flex: 1 1 35% !important;
    padding-left: 5px !important;
    font-style: normal;
    font-weight: $inputFontWeight;
    font-stretch: normal;
    font-size: 1.2rem;
    font-family: $ff-base;
  }
}

cgdis-portal-cgdisdatatable {
  > div {
    .cgdis-datatable-loader {
      position: absolute;
      top: 0;
      left: 0;
      border-top: 0;
      width: 100%;
      height: 100%;
      min-height: 200px;
    }
  }
}

cgdis-portal-people-management-function-table {
  .ngx-datatable {
    height: inherit;
  }
}

ngx-datatable,
cgdis-portal-people-management-function-table {
  margin-bottom: 0;

  overflow: visible !important;

  &.ngx-datatable {
    height: inherit;
  }

  &.ngx-datatable {
    .datatable-header {
      overflow: visible;

      .datatable-row-center {
        transform: none !important;
      }

      .datatable-header-cell {
        overflow: visible;
      }
    }
  }

  .pager {
    .pages {
      &.active {
        background-color: $c-primary;

        a {
          color: $c-selection-color;
        }
      }
    }

    [class^='datatable-icon-']:before,
    [class*=' datatable-icon-']:before {
      line-height: 1.8em;
    }
  }

  .empty-row {
    @include listEmpty();
    padding-top: 2em;
    line-height: 5rem;
  }

  datatable-header-cell {
    @include simpleTableHeaderColumn();

    [class^='datatable-icon-']:before,
    [class*=' datatable-icon-']:before {
      line-height: 1.6em;
    }
  }

  .datatable-filter {
    width: 100%;

    input,
    select,
    p {
      padding: 0.375em 0.75em;
      width: 100%;
      font-weight: 400;
      line-height: normal;
    }

    .ngx-select__choices {
      min-width: auto;
    }
  }

  cgdis-portal-simple-table-button {
    > div {
      display: inline-block;
      margin: 0.5rem 0.25rem 0.5rem 0.25rem;
      $heightAndWidth: 3rem;
      //padding: 1rem;
      width: $heightAndWidth; // Mandatory, if no margin, icon added in cgdis-portal-simple-table-button are not clickable
      height: $heightAndWidth;

      &:first-child {
        //margin-left: 0;
      }

      .linkWithIconComponent {
        display: block;
        width: 100%;
        height: 100%;

        .btn.-rounded.-primary.-small {
          width: 3rem;
          height: 3rem;
          line-height: 2.5rem;

          [class*='icon-'] {
            margin-top: 0.1rem;
            height: 2rem;
            width: 2rem;
          }
        }

        //.btn.-rounded.-primary:not(.-disabled):focus {
        //  background-color: $c-primary;
        //  color: white;
        //  &:hover {
        //    background-color: white;
        //    color: $c-primary;
        //  }
        //}
      }
    }
  }
}

cgdis-portal-simple-table-button {
  > div {
    display: inline-block;
    margin-left: 1rem;
    $heightAndWidth: 3rem;
    //padding: 1rem;
    width: $heightAndWidth; // Mandatory, if no margin, icon added in cgdis-portal-simple-table-button are not clickable
    height: $heightAndWidth;

    .linkWithIconComponent {
      display: block;
      width: 100%;
      height: 100%;

      .btn.-rounded.-primary.-small {
        width: 3rem;
        height: 3rem;
        line-height: 2.5rem;

        &:focus {
          opacity: 1;
        }
      }

      //.btn.-rounded.-primary:not(.-disabled):focus {
      //  background-color: $c-primary;
      //  color: white;
      //  &:hover {
      //    background-color: white;
      //    color: $c-primary;
      //  }
      //}
    }
  }
}

.likeSimpleTable {
  + .likeSimpleTable {
    margin-top: 2em;
  }

  @include simpleTableBorder();

  .likeSimpleTableHeader {
    @include simpleTableHeaderRow();
  }

  .likeSimpleTableHeaderFormField {
    .form-item {
      label {
        @include simpleTableHeaderRow();
        @include simpleTableColumnTitle();
        @include simpleTableHeaderColumn();
      }
    }
  }

  .likeSimpleTableHeaderFormFieldRequired {
    .form-item {
      label {
        @include simpleTableHeaderRow();
        @include simpleTableColumnTitle();
        @include simpleTableHeaderColumn();
      }
    }
  }
}

datatable-body-cell {
  font-size: 1em;
}

.datatable-input {
  background-color: transparent !important;
  color: white !important;
  /*@media (max-width: 400px) {
    border-bottom: none;
    text-align: right;
    padding-right: 4em !important;
  }*/
}

/*cgdis-portal-datatable-text-with-null-filter,
cgdis-portal-datatable-text-filter{
  input{
    padding-right: 4em !important;
    padding-left: 0 !important;
  }
}*/

cgdis-portal-datatable-datepicker-filter,
cgdis-portal-datatable-number-filter,
cgdis-portal-datatable-text-with-null-filter,
cgdis-portal-datatable-text-filter {
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  //margin-bottom: 1em;
}

cgdis-portal-datatable-number-filter,
cgdis-portal-datatable-text-with-null-filter,
cgdis-portal-datatable-text-filter {
  display: flex;
  align-items: center;
  background-position: right center;
  background-size: 1.5rem;
  //background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUwIDUwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MCA1MDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxnPjxwYXRoIGZpbGw9IndoaXRlIiBkPSJNNDcuNCw0NS42TDM0LjMsMzIuNUMzNy4yLDI5LjEsMzksMjQuOCwzOSwyMEMzOSw5LjUsMzAuNSwxLDIwLDFTMSw5LjUsMSwyMHM4LjUsMTksMTksMTljNC4zLDAsOC4yLTEuNCwxMS40LTMuOGwxMy4yLDEzLjJDNDUsNDguOCw0NS41LDQ5LDQ2LDQ5czEtMC4yLDEuNC0wLjZDNDguMiw0Ny42LDQ4LjIsNDYuNCw0Ny40LDQ1LjZ6IE01LDIwYzAtOC4zLDYuNy0xNSwxNS0xNWM4LjMsMCwxNSw2LjcsMTUsMTVjMCw4LjMtNi43LDE1LTE1LDE1QzExLjcsMzUsNSwyOC4zLDUsMjB6Ii8+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0yMS44LDguMmMtNS40LTAuMy0xMC41LDIuNi0xMi45LDcuNUM4LjQsMTYuNyw4LDE3LjksNy43LDE5Yy0wLjIsMS4xLDAuNSwyLjEsMS42LDIuNGMwLjEsMCwwLjMsMCwwLjQsMGMwLjksMCwxLjgtMC43LDItMS42YzAuMi0wLjgsMC40LTEuNiwwLjgtMi40YzEuNy0zLjQsNS4zLTUuNSw5LjEtNS4zYzEuMSwwLjEsMi0wLjgsMi4xLTEuOVMyMi45LDguMiwyMS44LDguMnoiLz48L2c+PC9zdmc+);
  background-repeat: no-repeat;
  width: 100%;

  i {
    padding-left: 1rem;
    color: white;
  }

  input {
    padding-right: 3rem !important;
    overflow: hidden;
    //padding-bottom: 0 !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    //height: 2.3rem !important;
  }

  .close-icon {
    right: 4rem;
  }

  .close-icon,
  .search-icon {
    @media (max-width: 768px) {
      top: 2.2rem;
    }
  }
}

cgdis-portal-datatable-select-filter,
.cgdis-portal-datatable-select-filter {
  .ngx-select__toggle {
    .dropdown-toggle {
      -webkit-transform: rotate(180deg);
      transform: rotate(180deg);
      background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUwIDUwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+IDxwYXRoIGZpbGw9IndoaXRlIiBkPSJtMjU2IDIxNGwxNzQgMTY3YzQgNCAxMSA0IDE2IDBsMzAtMzBjNS00IDUtMTEgMS0xNmwtMjEzLTIwNGMtMi0yLTUtMy04LTMtMyAwLTYgMS04IDNsLTIxMyAyMDRjLTQgNS00IDEyIDAgMTZsMzEgMzBjNCA0IDExIDQgMTYgMHoiLz48L2c+PC9zdmc+) !important;
      background-position: right 1rem top;
      background-size: 1.6rem;
      background-repeat: no-repeat;
      padding-right: 1.5rem;

      &::after {
        visibility: hidden;
      }
    }
  }
}

cgdis-portal-datatable-datepicker-filter {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUwIDUwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MCA1MDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPiA8cGF0aCBmaWxsPSJ3aGl0ZSIgZD0iTTQ2LjEsNC44aC02Yy0wLjcsMC0xLjMsMC42LTEuMywxLjNjMCwwLjcsMC42LDEuMywxLjMsMS4zaDZjMC43LDAsMS4zLDAuNiwxLjMsMS4zdjguMUgzVjguN2MwLTAuNywwLjYtMS4zLDEuMy0xLjNoNi44IGMwLjcsMCwxLjMtMC42LDEuMy0xLjNjMC0wLjctMC42LTEuMy0xLjMtMS4zSDQuM2MtMi4xLDAtMy45LDEuNy0zLjksMy45djM1LjFjMCwyLjEsMS43LDMuOSwzLjksMy45aDQxLjhjMi4xLDAsMy45LTEuNywzLjktMy45IFY4LjdDNTAsNi41LDQ4LjIsNC44LDQ2LjEsNC44eiBNNDcuNCw0My44YzAsMC43LTAuNiwxLjMtMS4zLDEuM0g0LjNjLTAuNywwLTEuMy0wLjYtMS4zLTEuM1YxOS4zaDQ0LjRMNDcuNCw0My44TDQ3LjQsNDMuOHogTTE5LjksNy40aDEwLjRjMC43LDAsMS4zLTAuNiwxLjMtMS4zYzAtMC43LTAuNi0xLjMtMS4zLTEuM0gxOS45Yy0wLjcsMC0xLjMsMC42LTEuMywxLjNDMTguNiw2LjgsMTkuMiw3LjQsMTkuOSw3LjR6IE0xNS4yLDE0LjQgYzEuNSwwLDIuNy0xLjIsMi43LTIuN2MwLTEtMC42LTEuOS0xLjQtMi4zVjEuNmMwLTAuNy0wLjYtMS4zLTEuMy0xLjNjLTAuNywwLTEuMywwLjYtMS4zLDEuM3Y3LjdjLTAuOCwwLjUtMS40LDEuMy0xLjQsMi4zIEMxMi41LDEzLjIsMTMuOCwxNC40LDE1LjIsMTQuNHogTTM1LjEsMTQuNGMxLjUsMCwyLjctMS4yLDIuNy0yLjdjMC0xLTAuNi0xLjktMS40LTIuM1YxLjZjMC0wLjctMC42LTEuMy0xLjMtMS4zIGMtMC43LDAtMS4zLDAuNi0xLjMsMS4zdjcuN2MtMC44LDAuNS0xLjQsMS4zLTEuNCwyLjNDMzIuNCwxMy4yLDMzLjYsMTQuNCwzNS4xLDE0LjR6Ii8+IDxwYXRoIGZpbGw9IndoaXRlIiBkPSJNNDIuNSw0Mi41aC01LjRjLTEuMSwwLTItMC45LTItMnYtNS40YzAtMS4xLDAuOS0yLDItMmg1LjRjMS4xLDAsMiwwLjksMiwydjUuNEM0NC41LDQxLjYsNDMuNiw0Mi41LDQyLjUsNDIuNXoiLz48L3N2Zz4=);
  background-position: right 0.5rem top;
  background-size: 1.6rem;
  background-repeat: no-repeat;

  input.-with-datepicker,
  input[type='date'] {
    background-image: unset;
    padding: 0.5rem !important;
    height: 2em;
    font-size: 1.05em;
  }

  @media (max-width: 768px) {
    background-position: right 0.5rem top 0.5rem;
  }

  .close-icon {
    right: 4rem;
    bottom: 1.2rem;
  }
}

datatable-header-cell {
  cgdis-portal-datatable-select-filter,
  .cgdis-portal-datatable-select-filter {
    .ngx-select__clear-icon {
      color: white;
    }
  }
}

.cgdis-portal-datatable-select-filter {
  > * {
    width: 100%;
  }
}

cgdis-portal-datatable-select-filter,
.cgdis-portal-datatable-select-filter {
  span.ngx-select__selected-plural.btn.btn-default.btn-secondary.btn-xs {
    align-items: baseline;
  }

  .ngx-select a.ngx-select__clear {
    margin-top: -0.2em !important;
  }

  .ngx-select__toggle.btn.form-control {
    //margin-top: 0.2rem;
    flex-direction: row;
    align-items: baseline;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 0;
    background: transparent !important;
    padding: 0.5rem !important;
    height: 2em;
    color: white;
    font-weight: 400;
    font-size: 1.05em;
    line-height: normal;

    .ngx-select__toggle-buttons {
      justify-content: right;
    }

    .pull-left {
      display: inline-grid;
      width: 80%;
    }
  }

  .ngx-select.dropdown {
    outline: none;
    border: none;
  }
}

/*.datatable-row-center.datatable-row-group.ng-star-inserted {
  @media (min-width: 768px) {
    padding-left: 2rem;
    padding-right: 2rem;
  }

}*/

.datatable-row-center.ng-star-inserted {
  /*@media (min-width: 768px){
    padding-left: 2rem;
    padding-right: 2rem;
  }*/

  padding-top: 0.2rem;
}

mat-expansion-panel {
  cgdis-portal-cgdisdatatable {
    .datatable-body {
      border: 1px solid #e4edf7;
    }
  }
}

.ngx-datatable .datatable-body .datatable-row-detail {
  overflow-y: visible !important;
}

.ngx-datatable.fixed-row {
  .datatable-scroll {
    .datatable-body-row {
      .datatable-body-cell {
      }
    }
  }
}

.datatable-icon-prev {
  &:before {
    display: block;
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUwIDUwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MCA1MDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwYXRoIGZpbGw9IiNjMTAwMmUiIGQ9Ik0yMi4yLDYuN2MtMS4xLTEuMS0yLjktMS4xLTQsMGMwLDAsMCwwLDAsMEwxLjgsMjNjLTEuMSwxLjEtMS4xLDIuOSwwLDRjMCwwLDAsMCwwLDBsMTYuMywxNi4zYzEuMSwxLjEsMi45LDEuMSw0LDBjMS4xLTEuMSwxLjEtMi45LDAtNGwwLDBMNy45LDI1bDE0LjMtMTQuM0MyMy4zLDkuNiwyMy4zLDcuOCwyMi4yLDYuN0MyMi4yLDYuNywyMi4yLDYuNywyMi4yLDYuN3oiLz48cGF0aCBmaWxsPSIjYzEwMDJlIiBkPSJNNDcuMiwyLjFjLTEuNC0xLjQtMy43LTEuNC01LjEsMGMwLDAsMCwwLDAsMEwyMS43LDIyLjVjLTEuNCwxLjQtMS40LDMuNywwLDUuMWMwLDAsMCwwLDAsMGwyMC40LDIwLjRjMS40LDEuNCwzLjcsMS40LDUuMSwwYzEuNC0xLjQsMS40LTMuNywwLTUuMWwwLDBMMjkuMywyNUw0Ny4yLDcuMUM0OC42LDUuNyw0OC42LDMuNSw0Ny4yLDIuMUM0Ny4yLDIuMSw0Ny4yLDIuMSw0Ny4yLDIuMXoiLz48L3N2Zz4=) !important;
    background-size: 10px 10px;
    background-repeat: no-repeat;
    width: 10px;
    height: 10px;
    content: ' ';
  }
}

.datatable-icon-skip {
  &:before {
    display: block;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUwIDUwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MCA1MDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwYXRoIGZpbGw9IiNjMTAwMmUiIGQ9Ik0yMi4yLDYuN2MtMS4xLTEuMS0yLjktMS4xLTQsMGMwLDAsMCwwLDAsMEwxLjgsMjNjLTEuMSwxLjEtMS4xLDIuOSwwLDRjMCwwLDAsMCwwLDBsMTYuMywxNi4zYzEuMSwxLjEsMi45LDEuMSw0LDBjMS4xLTEuMSwxLjEtMi45LDAtNGwwLDBMNy45LDI1bDE0LjMtMTQuM0MyMy4zLDkuNiwyMy4zLDcuOCwyMi4yLDYuN0MyMi4yLDYuNywyMi4yLDYuNywyMi4yLDYuN3oiLz48cGF0aCBmaWxsPSIjYzEwMDJlIiBkPSJNNDcuMiwyLjFjLTEuNC0xLjQtMy43LTEuNC01LjEsMGMwLDAsMCwwLDAsMEwyMS43LDIyLjVjLTEuNCwxLjQtMS40LDMuNywwLDUuMWMwLDAsMCwwLDAsMGwyMC40LDIwLjRjMS40LDEuNCwzLjcsMS40LDUuMSwwYzEuNC0xLjQsMS40LTMuNywwLTUuMWwwLDBMMjkuMywyNUw0Ny4yLDcuMUM0OC42LDUuNyw0OC42LDMuNSw0Ny4yLDIuMUM0Ny4yLDIuMSw0Ny4yLDIuMSw0Ny4yLDIuMXoiLz48L3N2Zz4=) !important;
    background-size: 10px 10px;
    background-repeat: no-repeat;
    width: 10px;
    height: 10px;
    content: ' ';
  }
}

.datatable-icon-down,
.datatable-icon-right {
  pointer-events: none;
}

.ngx-datatable
  .datatable-header
  .datatable-header-cell.resizeable:hover
  .resize-handle {
  display: none;
}
