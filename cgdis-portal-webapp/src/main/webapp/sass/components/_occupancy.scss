.occupancy {
  padding: 2.5rem 2rem;

  &__slider {
    position: relative;
  }

  &__more {
    padding: 1.5rem 0;
    font-size: 1.2rem;
    text-align: center;
    :hover {
      cursor: pointer;
    }
  }

  &__list {
    @extend %unstyled-list;
    padding: 4rem 2.5rem;
  }

  &__date {
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
    font-family: $ff-base;
  }

  &__chart {
  }

  &__add {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    min-height: 20rem;
    font-size: 1.2rem;
    svg {
      display: block;
      margin: 0 auto 1rem;
      width: 5rem;
      height: 5rem;
    }
  }

  &__name {
    margin-top: 6px;
    color: $c-secondary-lighter;
    font-size: 1.8rem;
    line-height: 1.2;
    font-family: $ff-heading;
  }

  &__vehicule {
    margin-bottom: 1rem;
    color: $c-primary;
    font-size: 1.3rem;
  }

  &__spname {
    display: inline-block;
    height: auto;
    max-height: 64px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__details {
    @extend %clearfix;
    font-weight: 300;
    font-size: 1.2rem;
    line-height: 1;

    dt {
      clear: left;
      margin-right: 0.5em;
      font-weight: inherit;
    }

    dt,
    dd {
      float: left;
    }
  }

  @include media-breakpoint-up(md) {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 33.33333%;

    &__list {
      display: flex;
      overflow-x: auto;
    }

    & + & {
    }

    &__more {
      padding: 1.5rem 4rem;
    }
  }
}

.js-occupancy-slider {
  padding: 3rem 4rem 5rem;

  + .occupancy__more {
    position: relative;
    z-index: 9;
    margin: -5rem 4rem 0;
  }

  .owl-item {
    .occupancy {
      position: relative;

      &:after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        background-color: #e0e0e0;
        width: 0.1rem;
        content: '';
      }
    }
  }

  @include media-breakpoint-up(md) {
    display: flex;
  }
}

.occupancy {
  cursor: pointer;
}

.occupancy-rates {
  &__subtitle {
    font-size: 1.3rem;
  }

  &__title {
    font-weight: 600;
    font-size: 1.5rem;
  }
}
