/**
Override Angular Material styles
*/
html {
  --mat-form-field-container-vertical-padding: 6px;
  --mat-form-field-container-height: 24px;

  --mdc-outlined-text-field-outline-color: rgba(117, 127, 141, 0.2);
  --mdc-outlined-text-field-outline-width: 0.1rem;
  --mat-select-enabled-trigger-text-color: $c-txt;

  --mat-select-trigger-text-weight: 300;
  --mat-option-hover-state-layer-color: #428bca;
  --mat-option-focus-state-layer-color: #428bca;
  --mat-option-selected-state-layer-color: #428bca;
  --mat-option-selected-state-label-text-color: #ffffff;
}

.mat-mdc-form-field {
  &.mat-form-field-hide-placeholder {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-flex {
        .mat-mdc-floating-label {
          top: calc((var(--mat-form-field-container-height) / 2) + 5px);
        }
      }
    }
  }
}

mat-expansion-panel-header {
  &:hover {
    background: unset !important;
  }
}

.mat-mdc-paginator-container {
  justify-content: space-between !important;

  .mat-mdc-paginator-range-label {
    margin: 0 10px !important;
  }
}

.main-nav .mat-expansion-panel-header.mat-expanded:hover {
  background-color: #fafafa !important;
}

.mat-mdc-select {
  .mat-mdc-select-arrow {
    @include select_dropdown_icon();
    width: 24px;
    height: 24px;

    svg {
      display: none;
    }
  }
}

.mat-mdc-option {
  min-height: 30px !important;

  &:hover:not(.mdc-list-item--disabled) {
    color: #ffffff;
  }
}

.mat-mdc-select-panel {
  .mdc-list-item {
    .mat-mdc-option-pseudo-checkbox {
      display: none;
    }
  }
}
