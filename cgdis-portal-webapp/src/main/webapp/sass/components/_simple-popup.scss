.scrollable-entity-popup {
  width: 100%;
  height: 100%;

  ::-webkit-scrollbar {
    background: transparent; /* make scrollbar transparent */
    width: 0px;
  }
}

.availability-popup {
  width: 55%;
  height: 55%;

  .form {
    .form-actions {
      padding: 1rem 4rem 1rem 4rem !important;
      //position: absolute;
      //bottom: 0;
      //width: 100%;
    }
  }

  .themed-popup__content {
    // padding: 2rem 4rem 0 4rem !important;
  }

  .popup-bottom-actions {
    margin: 1rem 0 !important;
  }

  .add-event {
    position: relative;
    height: 100%;
  }
}

.send-filling-popup {
  width: 55%;
  height: 55%;

  .ck-editor {
    max-height: 20rem;

    &__main {
      div {
        width: 100rem;
        height: 26rem;
      }
    }
  }
}

.simple-popup {
  // center popup on screen
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto !important;
  width: 55%;
  height: auto;

  .form {
    padding: 0;

    .form-actions {
      padding: 4rem;
    }
  }

  .mat-mdc-dialog-container {
    box-sizing: border-box;
    outline: 0;
    box-shadow:
      0 11px 15px -7px rgba(0, 0, 0, 0.2),
      0 24px 38px 3px rgba(0, 0, 0, 0.14),
      0 9px 46px 8px rgba(0, 0, 0, 0.12);
    background: white;
    padding: 0;
    width: 100%;
    height: auto;
    overflow: auto;
  }

  .popup-close-button {
    position: absolute;
    top: 0.5vh;
    right: 0.5vw;
    border: none;
    background-color: transparent;
    width: 1.5em;
    height: 1.5em;
    color: white;
    font-size: 1.5em;
  }

  .popup-bottom-actions {
    margin-top: 2em;
    text-align: center;
  }

  ::-webkit-scrollbar {
    background: transparent; /* make scrollbar transparent */
    width: 0px;
  }
}

.simple-popup,
.simple-popup-mobile {
  // center popup on screen
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto !important;
  width: 100%;
  height: 100%;

  .form {
    padding: 0;

    .form-actions {
      padding: 4rem;
    }
  }

  .mat-mdc-dialog-container {
    box-sizing: border-box;
    outline: 0;
    box-shadow:
      0 11px 15px -7px rgba(0, 0, 0, 0.2),
      0 24px 38px 3px rgba(0, 0, 0, 0.14),
      0 9px 46px 8px rgba(0, 0, 0, 0.12);
    background: white;
    padding: 0;
    width: 98%;
    height: auto;
    max-height: 100%;
    overflow: auto;
  }

  .popup-close-button {
    position: absolute;
    top: 0.3vh;
    right: 1vw;
    border: none;
    background-color: transparent;
    padding: 0;
    color: white;

    svg {
      max-width: unset;
      height: 2rem;
    }
  }

  .popup-bottom-actions {
    margin-top: 2em;
    text-align: center;
  }
}

.xl-simple-popup {
  .form {
    .form-actions {
      padding: 4rem 4rem 1rem 4rem !important;
    }
  }

  //.themed-popup__content {
  //  padding: 4rem 4rem 0 4rem !important;
  //}
}

.xl-simple-popup-mobile {
  .form {
    .form-actions {
      padding: 4rem 4rem 1rem 4rem !important;
    }
  }

  //.themed-popup__content {
  //  padding: 4rem 4rem 0 4rem !important;
  //}
}

/**
Dialogs on mobile phone
 */

.cdk-overlay-pane {
  &.simple-popup-mobile {
    width: 100% !important;
    max-width: unset !important;
    height: 100%;
    //overflow: auto !important;
  }

  &.simple-popup {
    max-width: unset !important;
    height: 100%;
  }
}

.cdk-overlay-pane {
  &.xl-simple-popup-mobile {
    width: 90% !important;
    max-width: unset !important;
    height: 100%;
    //overflow: auto !important;
  }
}

.cdk-overlay-pane {
  &.xl-simple-popup {
    width: 90% !important;
    max-width: unset !important;
    height: auto;
    overflow: auto !important;
  }
}

cgdis-portal-simple-yes-no-popup {
  .popup-bottom-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
}
