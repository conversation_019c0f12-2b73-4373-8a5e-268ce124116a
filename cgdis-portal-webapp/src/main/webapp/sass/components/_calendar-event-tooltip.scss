.qtip-event {
  background-color: #fff;
  padding: 0;

  .qtip-content {
    padding: 1.5rem 1rem 1.5rem 2rem;
    min-width: 16rem;
  }

  &.-availability {
    border-color: #b1d1f4;

    .qtip-content {
      background-color: rgba(#b1d1f4, 0.05);
    }
  }

  &.-planning {
    border-color: #7ccb5d;

    .qtip-content {
      background-color: rgba(#7ccb5d, 0.05);
    }
  }
  &.-professional {
    border-color: $c-professional;

    .qtip-content {
      background-color: rgba($c-professional, 0.05);
    }
  }

  &.-holidays {
    border-color: #fff9e7;

    .qtip-content {
      background-color: rgba(#fff9e7, 0.1);
    }
  }

  &.-noData {
    border-color: $c-gray-mid-dark;

    .qtip-content {
      background-color: rgba($c-gray-mid-dark, 0.1);
    }
  }
}
