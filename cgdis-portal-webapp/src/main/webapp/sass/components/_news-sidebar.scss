.news-sidebar {
  position: relative;
  margin: 0;
  box-shadow: 0px 3px 12px 3px rgba(162, 195, 229, 0.3);
  border-radius: 0.4rem;
  background-color: $logo-color;
  padding: 2rem 0;
  min-height: 100%;
  color: #fff;

  article {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(162, 195, 229, 0.3);
    img {
      box-shadow: 0 2px 6px 2px #042074;
      border-radius: 0.2rem;
    }
  }

  &__title {
    margin-bottom: 2rem;
    color: inherit;
    text-align: center;
  }

  &__news {
    @extend %unstyled-list;

    > li {
      position: relative;
      margin-bottom: 1.5rem;
      padding-bottom: 1.5rem;

      &:after {
        position: absolute;
        right: 0;
        left: 0;
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00f6f6f6', endColorstr='#00f6f6f6',GradientType=.1 ); /* IE6-9 */
        /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#f6f6f6+0,f6f6f6+100&0+0,1+10,1+90,0+100 */
        background: -moz-linear-gradient(
          left,
          rgba(246, 246, 246, 0) 0%,
          rgba(246, 246, 246, 0.1) 10%,
          rgba(246, 246, 246, 0.1) 90%,
          rgba(246, 246, 246, 0) 100%
        ); /* FF3.6-15 */
        background: -webkit-linear-gradient(
          left,
          rgba(246, 246, 246, 0) 0%,
          rgba(246, 246, 246, 0.1) 10%,
          rgba(246, 246, 246, 0.1) 90%,
          rgba(246, 246, 246, 0) 100%
        ); /* Chrome10-25,Safari5.1-6 */
        background: linear-gradient(
          to right,
          rgba(246, 246, 246, 0) 0%,
          rgba(246, 246, 246, 0.1) 10%,
          rgba(246, 246, 246, 0.1) 90%,
          rgba(246, 246, 246, 0) 100%
        ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
        height: 0.1rem;
        content: '';
      }
    }
  }

  &__empty {
    margin-bottom: 2rem;
  }

  .news {
    &__img {
      text-align: center;
      img {
        max-width: 82% !important;
      }
    }

    &__date {
      color: rgba($c-gray, 1);
      font-size: 1.4rem;
      font-family: $ff-heading;
    }

    &__summary {
      max-height: 8.5rem;
      color: rgba($c-gray-lighter, 0.8);
      font-weight: 300;
      font-size: 1.4rem;
      text-align: justify;

      a {
        color: rgba($c-gray-lighter, 0.8);
      }
    }
  }
}
