cgdis-portal-rici-pagers-common-form-fields {
  .entity-filter {
    outline: 0;
    position: relative;
    text-align: left !important;
    color: #333;
    background-color: #fff;
    border-color: #ccc;
    display: inline-flex;
    align-items: stretch;
    justify-content: space-between;
  }

  .maintenance-password-container {
    display: flex;
    align-items: center; // Align items vertically
    gap: 10px; // Space between input and button

    .form-field-container {
      flex-grow: 1; // Allow input field to take available space
      margin-bottom: 0 !important; // Remove default margin if needed
    }

    .reset-password-icon {
      &.disabled-link {
        color: $c-gray;
      }
    }
  }

  .redirect-links {
    display: inline-flex;
    gap: 10px;
    margin-left: 10px;

    cgdis-portal-button-link {
      font-size: 1.6rem;
    }
  }
}

cgdis-portal-rici-pagers-edit {
  .form-actions {
    .row {
      flex-wrap: nowrap;
      gap: 10px;

      cgdis-portal-button-submit,
      cgdis-portal-button-cancel,
      cgdis-portal-button-primary {
        width: 100%;
      }
    }
  }
}
