.select2-container {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  box-sizing: border-box;
  margin: 0;

  @import 'single';
  @import 'multiple';
}

@import 'dropdown';

.select2-close-mask {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  opacity: 0;
  z-index: 99;
  filter: alpha(opacity=0);
  margin: 0;
  border: 0;

  // styles required for IE to work

  background-color: #fff;
  padding: 0;
  width: auto;
  min-width: 100%;
  height: auto;
  min-height: 100%;
}

.select2-hidden-accessible {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  position: absolute !important;
  margin: -1px !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  overflow: hidden !important;
}

@import 'theme/default/layout';
@import 'theme/classic/layout';
