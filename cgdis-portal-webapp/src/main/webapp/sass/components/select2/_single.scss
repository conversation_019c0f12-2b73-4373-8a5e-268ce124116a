.select2-selection--single {
  display: block;

  cursor: pointer;
  box-sizing: border-box;

  padding: 1rem 0;

  user-select: none;
  -webkit-user-select: none;

  .select2-selection__rendered {
    display: block;
    padding-right: 20px;
    padding-left: 8px;

    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .select2-selection__clear {
    position: relative;
  }
}

&[dir='rtl'] {
  .select2-selection--single {
    .select2-selection__rendered {
      padding-right: 8px;
      padding-left: 20px;
    }
  }
}
