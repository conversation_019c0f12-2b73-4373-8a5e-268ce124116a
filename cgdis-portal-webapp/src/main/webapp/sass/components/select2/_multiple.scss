.select2-selection--multiple {
  display: block;

  cursor: pointer;
  box-sizing: border-box;

  min-height: 32px;

  user-select: none;
  -webkit-user-select: none;

  .select2-selection__rendered {
    display: inline-block;
    padding-left: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.select2-search--inline {
  float: left;

  .select2-search__field {
    box-sizing: border-box;
    margin-top: 5px;
    border: none;
    padding: 0;
    font-size: 100%;

    &::-webkit-search-cancel-button {
      -webkit-appearance: none;
    }
  }
}
