.select2-selection--multiple {
  cursor: text;
  border: 1px solid #aaa;
  border-radius: 4px;
  background-color: white;

  .select2-selection__rendered {
    box-sizing: border-box;
    margin: 0;
    padding: 0 5px;
    width: 100%;
    list-style: none;

    li {
      list-style: none;
    }
  }

  .select2-selection__placeholder {
    float: left;

    margin-top: 5px;
    color: #999;
  }

  .select2-selection__clear {
    float: right;
    cursor: pointer;
    margin-top: 5px;
    margin-right: 10px;
    font-weight: bold;
  }

  .select2-selection__choice {
    float: left;
    cursor: default;
    margin-top: 5px;

    margin-right: 5px;

    border: 1px solid #aaa;
    border-radius: 4px;
    background-color: #e4e4e4;
    padding: 0 5px;
  }

  .select2-selection__choice__remove {
    display: inline-block;
    cursor: pointer;

    margin-right: 2px;
    color: #999;
    font-weight: bold;

    &:hover {
      color: #333;
    }
  }
}

&[dir='rtl'] {
  .select2-selection--multiple {
    .select2-selection__choice,
    .select2-selection__placeholder,
    .select2-search--inline {
      float: right;
    }

    .select2-selection__choice {
      margin-right: auto;
      margin-left: 5px;
    }

    .select2-selection__choice__remove {
      margin-right: auto;
      margin-left: 2px;
    }
  }
}

&.select2-container--focus {
  .select2-selection--multiple {
    outline: 0;
    border: solid black 1px;
  }
}

&.select2-container--disabled {
  .select2-selection--multiple {
    cursor: default;
    background-color: #eee;
  }

  .select2-selection__choice__remove {
    display: none;
  }
}
