.select2-selection--single {
  outline: 0;

  border: 1px solid $border-color;
  border-radius: $border-radius;
  background-color: mix($selection-bg-top-color, $selection-bg-bottom-color);

  @include gradient-vertical(
    $selection-bg-top-color,
    $selection-bg-bottom-color,
    50%,
    100%
  );

  &:focus {
    border: 1px solid $focus-border-color;
  }

  .select2-selection__rendered {
    color: #444;
    line-height: 28px;
  }

  .select2-selection__clear {
    float: right;
    cursor: pointer;
    margin-right: 10px;
    font-weight: bold;
  }

  .select2-selection__placeholder {
    color: #999;
  }

  .select2-selection__arrow {
    position: absolute;

    top: 1px;
    right: 1px;

    border: none;
    border-left: 1px solid $border-color;
    border-top-right-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
    background-color: #ddd;

    width: 20px;

    height: 26px;

    @include gradient-vertical(#eeeeee, #cccccc, 50%, 100%);

    b {
      position: absolute;

      top: 50%;
      left: 50%;
      margin-top: -2px;

      margin-left: -4px;
      border-width: 5px 4px 0 4px;
      border-style: solid;
      border-color: #888 transparent transparent transparent;
      width: 0;

      height: 0;
    }
  }
}

&[dir='rtl'] {
  .select2-selection--single {
    .select2-selection__clear {
      float: left;
    }

    .select2-selection__arrow {
      right: auto;

      left: 1px;
      border: none;
      border-right: 1px solid $border-color;

      border-radius: 0;
      border-top-left-radius: $border-radius;
      border-bottom-left-radius: $border-radius;
    }
  }
}

&.select2-container--open {
  .select2-selection--single {
    border: 1px solid $focus-border-color;

    .select2-selection__arrow {
      border: none;
      background: transparent;

      b {
        border-width: 0 4px 5px 4px;
        border-color: transparent transparent #888 transparent;
      }
    }
  }

  &.select2-container--above {
    .select2-selection--single {
      border-top: none;
      border-top-right-radius: 0;
      border-top-left-radius: 0;

      @include gradient-vertical(
        $selection-opened-bg-bottom-color,
        $selection-opened-bg-top-color,
        0%,
        50%
      );
    }
  }

  &.select2-container--below {
    .select2-selection--single {
      border-bottom: none;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;

      @include gradient-vertical(
        $selection-opened-bg-top-color,
        $selection-opened-bg-bottom-color,
        50%,
        100%
      );
    }
  }
}
