.select2-selection--multiple {
  cursor: text;

  outline: 0;

  border: 1px solid $border-color;
  border-radius: $border-radius;
  background-color: white;

  &:focus {
    border: 1px solid $focus-border-color;
  }

  .select2-selection__rendered {
    margin: 0;
    padding: 0 5px;
    list-style: none;
  }

  .select2-selection__clear {
    display: none;
  }

  .select2-selection__choice {
    float: left;

    cursor: default;
    margin-top: 5px;

    margin-right: 5px;

    border: 1px solid $border-color;
    border-radius: $border-radius;
    background-color: #e4e4e4;
    padding: 0 5px;
  }

  .select2-selection__choice__remove {
    display: inline-block;
    cursor: pointer;

    margin-right: 2px;
    color: $remove-color;
    font-weight: bold;

    &:hover {
      color: $remove-hover-color;
    }
  }
}

&[dir='rtl'] {
  .select2-selection--multiple {
    .select2-selection__choice {
      float: right;
    }

    .select2-selection__choice {
      margin-right: auto;
      margin-left: 5px;
    }

    .select2-selection__choice__remove {
      margin-right: auto;
      margin-left: 2px;
    }
  }
}

&.select2-container--open {
  .select2-selection--multiple {
    border: 1px solid $focus-border-color;
  }

  &.select2-container--above {
    .select2-selection--multiple {
      border-top: none;
      border-top-right-radius: 0;
      border-top-left-radius: 0;
    }
  }

  &.select2-container--below {
    .select2-selection--multiple {
      border-bottom: none;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
