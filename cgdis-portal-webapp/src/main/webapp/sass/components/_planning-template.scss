.planning-template {
  margin: 3rem 0;
  font-weight: 300;
  font-size: 1.4rem;

  &__list {
    @extend %unstyled-list;
    margin-bottom: 0.5em;

    > li {
      display: block;
      padding: 0.25em 0;
    }

    svg {
      margin: 0 1em;
      width: 1.8rem;
      height: 1.8rem;
    }
  }

  &__offset {
    span {
      display: inline-block;
      margin-left: 0.5em;
      border-bottom: 0.1rem solid rgba(117, 127, 141, 0.2);
      min-width: 2em;
      text-align: center;
    }
  }

  &__actions {
    margin-top: 1em;
    margin-bottom: 1em;
  }
}

.vertical-list {
  display: block;
  list-style: none;
  li {
    cgdis-portal-icon {
      color: $c-primary;
      svg {
        padding-bottom: 0.4em;
        width: 1em;
      }
    }
  }
}
.column-3 {
  columns: 3;
  -webkit-columns: 3;
  -moz-columns: 3;

  -webkit-column-rule: 1px double $c-gray; /* Chrome, Safari, Opera */
  -moz-column-rule: 1px double $c-gray; /* Firefox */
  column-rule: 1px double $c-gray;
}
.underline {
  text-decoration: underline;
}
