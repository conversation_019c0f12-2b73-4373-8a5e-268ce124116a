/*------------------------------------*\
    #BUTTONS
\*------------------------------------*/
.btn {
  display: inline-block;
  text-align: center;
  @media (min-width: 768px) {
    padding: 1em 2em;
  }
  vertical-align: middle;
  transition: background 250ms ease-in,
  color 250ms ease-in,
  box-shadow 250ms ease-in;
  border: none;
  border-radius: 0.2rem;
  font-size: inherit;
  user-select: none;
  text-decoration: none;

  &.-disabled {
    background-color: $disabledColor;
  }

  &.-compact {
    padding: 0.5em 1em;
  }

  &.-block {
    display: block;
    margin: 0.5rem auto;
    width: 100%;
  }

  &.-primary {
    border: 0.1rem solid $c-primary;
    background: $c-primary;
    color: #fff;

    /*@media (max-width: 768px) {
        &:hover {
          background: #fff;
          color: $c-primary;
        }
      }*/
  }

  &.-outline-primary {
    border: 0.1rem solid $c-primary;
    background: transparent;
    color: $c-primary;

    /*@media (max-width: 768px) {
        &:hover {
          background: $c-primary;
          color: #fff;
        }
      }*/
  }

  &.-outline-white {
    border: 0.1rem solid #fff;
    background: transparent;
    padding: 1em 1em;
    color: #fff;

    @media (max-width: 768px) {
      &:hover {
        background: #fff;
        color: $c-gray-darker;
      }
    }
  }

  /*&.-secondary {
        border: .1rem solid #fff;
        background: #fff;
        color: $c-primary;

        &:hover,
        &:focus {
            background: $c-primary;
            color: #fff;
        }
    }*/

  &.-link {
    @extend %unstyled-button;
    color: $c-link;
    text-align: left;

    @media (max-width: 768px) {
      &:hover {
        outline: none;
        text-decoration: underline;
      }
    }
  }

  &.-linkOk {
    color: map-get(map-get($message-types, 'success'), 'color');
  }

  &.-rounded {
    @extend %unstyled-button;
    @extend .-shadow;
    border: 0.1rem solid #fff;
    border-radius: 50%;
    background: #fff;
    width: 3rem;
    height: 3rem;
    color: $c-primary;

    [class*='icon-'] {
      width: 1.4rem;
      height: 1.4rem;
    }

    &.-linkOk {
      background-color: map-get(map-get($message-types, 'success'), 'bg');
    }

    &:not(.-disabled) {
      @media (max-width: 768px) {
        &:hover {
          outline: none;
          box-shadow: 0.1rem 0.2rem 0.1rem rgba(162, 195, 229, 0.1) inset;
        }
      }
    }

    &.-primary {
      border: 0.1rem solid $c-primary-red;
      background: $c-primary-red;

      width: 4rem;
      height: 4rem;
      color: #fff;
      line-height: 3.6rem;

      &.-linkOk {
        border-color: map-get(map-get($message-types, 'success'), 'border');
        background-color: map-get(map-get($message-types, 'success'), 'border');
        @media (max-width: 768px) {
          &:hover {
            background: #fff;
            color: map-get(map-get($message-types, 'success'), 'color');
          }
        }
      }

      &.-disabled {
        border-color: $disabledColor;
        background: $disabledColor;
      }

      &.-small {
        width: 2rem;
        height: 2rem;
        line-height: 1.5rem;

        [class*='icon-'] {
          margin-top: 0.1rem;
          height: 1.2rem;
        }

        [class*='icon-delete'] {
          width: 1.2rem;
          height: 1.2rem;
          //margin-top: 0.2rem;
          @media (max-width: 1400px) {
            //margin-top: 0.2rem;
            margin-right: 0.1rem;
          }
          @media (max-width: 1300px) {
            //margin-top: 0.2rem;
            margin-right: 0;
          }
          @media (max-width: 1000px) {
            //margin-top: 0.2rem;
          }
        }
      }

      [class*='icon-'] {
        width: 1.5rem;
        height: 1.5rem;
      }

      &:not(.-disabled) {
        @media (max-width: 768px) {
          &:hover {
            background: #fff;
            color: $c-primary-red;
          }
        }
      }
    }
  }

  &.bold {
    font-weight: bold;
  }
}

.icon-margin-top-0 {
  [class*='icon-delete'] {
    // margin-top: 0rem !important;
    vertical-align: baseline !important;
  }
}

cgdis-portal-current-situation-detail,
cgdis-portal-admin-people-medical-information-list cgdis-portal-panel {
  .mat-expansion-indicator::after {
    @media (max-width: 992px) {
      color: white !important;
    }
  }
}

.mat-expansion-indicator::after {
  margin-bottom: 3px;
}


cgdis-portal-button-link {
  .btn {
    &.disabled {
      color: $disabledColor;
      cursor: inherit;
    }
  }
}

/*
** BTN SECONDARY
*/
//.btn-secondary {
//    border: .1rem solid $c-secondary;
//    background: $c-secondary;
//    color: #fff;
//
//    &:hover,
//    &:focus {
//        background: #fff;
//        color: $c-secondary;
//    }
//}
//
//.btn-outline-secondary {
//    border: .1rem solid $c-secondary;
//    background: transparent;
//    color: $c-secondary;
//
//    &:hover,
//    &:focus {
//        background: $c-secondary;
//        color: #fff;
//    }
//}
