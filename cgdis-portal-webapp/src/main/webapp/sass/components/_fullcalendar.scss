.fc-toolbar {
  display: none;
}
.fc-view {
  .fc-head {
    display: none;
  }
}

a.fc-time-grid-event.fc-v-event.fc-event.fc-start.fc-not-end.-availability.fullCalendarAvailability {
  padding-top: 1px;
}
a.fc-time-grid-event.fc-v-event.fc-event.fc-start.fc-end {
  padding-top: 0;
}

.fc-content > .fc-time {
  display: none !important;
}

.fullcalendar-agenda {
  clear: both;

  .day-selector {
    padding-right: 0;
    padding-left: 0;
    //padding-left: 40px;
  }

  .fc-content-skeleton {
    //min-height: 100%;
    /*
fullCalendarPrestation
fullCalendarProfessional
fullCalendarAvailability
*/

    table {
      &,
      td {
        &,
        .fc-content-col,
        .fc-event-container {
          .fc-time-grid-event {
            &.fullCalendarPrestation {
              //left:0!important;

              right: 0 !important;
              z-index: 2 !important;
              margin-right: 0 !important;

              &:nth-child(1) {
                left: 0 !important;
                z-index: 4 !important;
              }
              &:nth-child(2) {
                left: 5% !important;
                z-index: 5 !important;
              }
              &:nth-child(3) {
                left: 50% !important;
                z-index: 6 !important;
              }
            }
            &.fullCalendarProfessional,
            &.fullCalendarAvailability {
              right: 0 !important;
              left: 0 !important;
              z-index: 1 !important;
              margin-right: 0 !important;
            }

            &.fullCalendarPublicHolidays {
              right: 0 !important;
              left: 0 !important;
              z-index: 0 !important;
              margin-right: 0 !important;
            }

            &:hover {
              opacity: 1 !important;
              z-index: 5 !important;
              background-color: white !important;
              height: max-content;
            }
          }
        }
      }
    }
  }

  .fc-axis {
    width: 31px !important;
  }
}

.fc {
  th,
  td {
    border: 0;
  }
  .fc-axis {
    font-size: 1.2rem;
    line-height: 1.4;
  }

  .fc-slats,
  .fc-bg {
    tr,
    th,
    td {
      border: 0.1rem solid $c-gray-lightest;

      &.fc-today {
        background-color: transparent;
      }
    }
  }

  .fc-bg {
    .planning & {
      background: #fff;
    }
  }

  .fc-time-grid {
    .fc-slats {
      table {
        border: 0.1rem solid $c-gray-lightest;
      }

      td {
        height: 2em;
      }
    }
  }

  .fc-day-header {
    padding: 2rem;
    color: $c-secondary-lighter;
    font-weight: bold;
    font-size: 1.3rem;

    &.fc-today {
      @extend .-shadow;
      position: relative;

      span {
        &:before {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          border-top: 0.7rem solid $c-primary;
          content: '';
        }
      }
    }

    .js-day-duplicate {
      display: block;
      text-align: center;

      svg {
        width: 1.3rem;
        height: 1.3rem;
      }
    }

    .day__name {
      display: block;
      font-weight: normal;
      font-size: 1.3rem;

      .-short {
        display: none;
      }

      @include media-breakpoint-up(lg) {
        .-short {
          display: block;
        }

        .-long {
          display: none;
        }
      }
    }

    .day__number {
      font-weight: bold;
      font-size: 1.8rem;
      font-family: $ff-heading;
    }
  }

  .fc-toolbar {
    font-size: 2.3rem;
    font-family: $ff-heading;

    > .fc-center {
      > * {
        display: inline-block;
        vertical-align: middle;
        float: none;
      }

      > .fc-button {
        margin: 0;
        box-shadow: none;

        .fc-icon-right-single-arrow,
        .fc-icon-left-single-arrow {
          background: no-repeat 50% 50%;
          background-size: 100%;
          width: 2.3rem;
          height: 2.3rem;

          &:after {
            display: none;
          }
        }

        .fc-icon-left-single-arrow {
          background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICBpZD0ic3ZnNDQzNCIKICAgdmlld0JveD0iMCAwIDUxMiA1MTIiPgogIDxtZXRhZGF0YQogICAgIGlkPSJtZXRhZGF0YTQ0NDEiPgogICAgPHJkZjpSREY+CiAgICAgIDxjYzpXb3JrCiAgICAgICAgIHJkZjphYm91dD0iIj4KICAgICAgICA8ZGM6Zm9ybWF0PmltYWdlL3N2Zyt4bWw8L2RjOmZvcm1hdD4KICAgICAgICA8ZGM6dHlwZQogICAgICAgICAgIHJkZjpyZXNvdXJjZT0iaHR0cDovL3B1cmwub3JnL2RjL2RjbWl0eXBlL1N0aWxsSW1hZ2UiIC8+CiAgICAgICAgPGRjOnRpdGxlPjwvZGM6dGl0bGU+CiAgICAgIDwvY2M6V29yaz4KICAgIDwvcmRmOlJERj4KICA8L21ldGFkYXRhPgogIDxkZWZzCiAgICAgaWQ9ImRlZnM0NDM5IiAvPgogIDxwYXRoCiAgICAgc3R5bGU9ImZpbGw6I2MxMDAyZSIKICAgICBkPSJNIDIxNCwyNTYgMzgxLDgyIGMgNCwtNCA0LC0xMSAwLC0xNiBMIDM1MSwzNiBjIC00LC01IC0xMSwtNSAtMTYsLTEgTCAxMzEsMjQ4IGMgLTIsMiAtMyw1IC0zLDggMCwzIDEsNiAzLDggbCAyMDQsMjEzIGMgNSw0IDEyLDQgMTYsMCBsIDMwLC0zMSBjIDQsLTQgNCwtMTEgMCwtMTYgeiIKICAgICBpZD0icGF0aDQyMzUiIC8+Cjwvc3ZnPgo=');
        }

        .fc-icon-right-single-arrow {
          background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9Imljb24tY2hldnJvbiIKICAgdmlld0JveD0iMCAwIDUxMiA1MTIiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDI0MSI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQyMzkiIC8+CiAgPHBhdGgKICAgICBpZD0icGF0aDQyMzUiCiAgICAgZD0iTSAyOTgsMjU2IDEzMSw4MiBjIC00LC00IC00LC0xMSAwLC0xNiBsIDMwLC0zMCBjIDQsLTUgMTEsLTUgMTYsLTEgbCAyMDQsMjEzIGMgMiwyIDMsNSAzLDggMCwzIC0xLDYgLTMsOCBMIDE3Nyw0NzcgYyAtNSw0IC0xMiw0IC0xNiwwIGwgLTMwLC0zMSBjIC00LC00IC00LC0xMSAwLC0xNiB6IgogICAgIHN0eWxlPSJmaWxsOiNjMTAwMmU7ZmlsbC1vcGFjaXR5OjEiIC8+Cjwvc3ZnPgo=');
        }
      }

      > h2 {
        @extend .h3-like;
        margin: 0 0.5rem;
      }
    }
  }

  .fc-header-toolbar {
    .planning & {
      margin-top: 1rem;
    }
  }

  .fc-button {
    @extend %unstyled-button;
    height: auto;
  }

  .performances-calendar & {
    .fc-slats,
    .fc-bg {
      tr,
      th,
      td {
        border: 0;
      }
    }

    .fc-time-grid {
      .fc-slats {
        td {
          height: 2rem;
        }
      }
    }

    .fc-axis {
      display: none;
    }

    .fc-bg {
      table {
        border: 0.1rem solid $c-gray-lightest;

        th,
        td {
          &:not(.fc-axis) {
            border-right: 0.1rem solid $c-gray-lightest;
            border-left: 0.1rem solid $c-gray-lightest;
          }

          &.fc-sat,
          &.fc-sun {
            background-image: url('../../content/images/calendar-stripe.png');
          }
        }
      }
    }

    .fc-content-skeleton {
      //min-height: 100%;

      table {
        &,
        td {
          &,
          .fc-content-col,
          .fc-event-container {
            height: 100%;
            .fc-time-grid-event {
            }
          }
          .fc-helper-container {
            height: auto;
          }
          .fc-event-container:not(.fc-helper-container) {
            display: table;
            margin: 0;
            width: 100%;
          }
        }
      }

      .fc-event {
        display: table-row;
        box-sizing: border-box;

        .fc-content {
          padding: 0.5rem 1rem 1.5rem 1rem;
        }

        .fc-bg {
          border: 0.1rem solid #000;
        }
      }
    }
  }
}

.legend-box {
  svg {
    width: 1em;
    height: 1em;
    fill: $c-primary;
  }
}

.fc-time-grid-container {
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}
.qtip-event,
.fc-event {
  border: 0.1rem solid $c-gray-lightest;
  border-radius: 0;
  background-color: #fff;
  padding: 1.5rem 1rem;
  color: $c-gray-light;
  font-size: 1.2rem;

  .fc-time {
    margin-bottom: 1.5rem;
    color: $c-secondary-lighter;
    font-size: 1em;

    .planning & {
      display: none;
    }
  }

  .fc-title {
    margin-bottom: 1.5rem;
    color: $c-secondary-lighter;
    font-weight: 500;

    cgdis-portal-icon {
      svg {
        width: 1em;
        height: 1em;
        fill: $c-primary;
      }
    }
  }

  .fc-description {
    color: $c-gray-light;
  }

  &.fc-short {
    .fc-content {
      white-space: normal;
    }
  }

  &:before {
    position: absolute;
    top: 0.3rem;
    bottom: 0.3rem;
    left: 0.3rem;
    background-color: $c-gray-lightest;
    width: 0.3rem;
    content: '';
  }

  &.-availability {
    .fc-bg,
    &:before {
      background-color: #b1d1f4;
    }
  }
  &.-planning {
    .fc-bg,
    &:before {
      background-color: #7ccb5d;
    }
  }
  &.-professional {
    .fc-bg,
    &:before {
      background-color: $c-professional;
    }
  }
  &.-holidays {
    .fc-bg,
    &:before {
      background-color: #fff9e7;
    }
  }
  &.-noData {
    .fc-bg,
    &:before {
      background-color: $c-gray-mid-dark;
    }
  }

  .fc-bg {
    opacity: 0.05;
  }
}

.fc-bgevent {
  opacity: 0.6;
  background: rgb(255, 249, 231);
}

.toggle-split {
  position: relative;
  top: 1rem;
  z-index: 1;
  padding: 1rem;
  text-align: right;
}

.toggle-closed {
  position: absolute;
  top: -2.5rem;
  right: 1rem;
}
.header-prestations {
  display: flex;
  justify-content: space-between;
  align-items: baseline;

  .toggle-prestations {
    margin: 0.5rem;

    @include media-breakpoint-up('sm') {
      //position: absolute;
      top: 2rem;
      left: 1.1rem;
      margin: 0;
      width: 50%;
    }
  }
}

cgdis-portal-availability-full-calendar {
  .tabs__next,
  .tabs__prev {
    display: none;
  }
  .copy-button {
    float: right;
    margin: 0.5rem;
  }

  @include media-breakpoint-up('lg') {
    .copy-button {
      position: absolute;
      top: 1.2rem;
      right: 4rem;
      float: none;
      z-index: 1;
      margin: 0;
    }
  }
}

cgdis-portal-availability-full-calendar-non-editable-popup div,
cgdis-portal-availability-full-calendar-popup div,
cgdis-portal-availability-full-calendar-prestation-popup div {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

cgdis-portal-datetimepicker-field label {
  min-width: 45px;
}

cgdis-portal-availability-full-calendar-popup {
  .form-item {
    margin-bottom: 0;
  }
  .themed-popup__title {
    margin-bottom: 2rem;
  }

  .themed-popup__content {
    @media (max-width: 400px) {
      padding: 2rem;
    }
  }

  input.form-control {
    border-radius: 0;
  }

  cgdis-portal-form {
    cgdis-portal-popup-template {
      fieldset {
        @media (min-width: 400px) {
          width: 15rem;
          max-width: 15rem;
        }

        @media (max-width: 400px) {
          width: 10rem;
          max-width: 10rem;
        }
      }

      .ngb-tp {
        background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSI1MHB4IiBoZWlnaHQ9IjUwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiNmZmZmZmY7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik0yNSwwLjRDMTEuNCwwLjQsMC40LDExLjQsMC40LDI1czExLDI0LjYsMjQuNiwyNC42czI0LjYtMTEsMjQuNi0yNC42bDAsMEM0OS42LDExLjQsMzguNiwwLjQsMjUsMC40eg0KCSBNMjYuOCw0Ni4xdi00LjNoLTMuNXY0LjNDMTMsNDUuMiw0LjgsMzcsMy45LDI2LjhoNC4zdi0zLjVIMy45QzQuOCwxMywxMyw0LjgsMjMuMiwzLjl2NC4zaDMuNVYzLjlDMzcsNC44LDQ1LjIsMTMsNDYuMSwyMy4yaC00LjMNCgl2My41aDQuM0M0NS4yLDM3LDM3LDQ1LjIsMjYuOCw0Ni4xeiIvPg0KPHBvbHlnb24gY2xhc3M9InN0MCIgcG9pbnRzPSIyNSwyNC43IDE4LjIsMTIuNiAxNS4yLDE0LjMgMjIuOSwyOC4yIDM1LjYsMjguMSAzNS42LDI0LjYgIi8+DQo8L3N2Zz4NCg==')
          no-repeat right 1rem top 50%;
        background-size: 1.4rem;
        padding-right: 3.5rem;
        @media (min-width: 768px) {
          width: 10rem;
          max-width: 10rem;
        }
      }

      .ngb-tp-hour {
        input {
          margin: 0.5rem auto;
          border: 0;
          border-bottom: 0.1rem solid #757f8d;
          background-color: transparent;
          padding-right: 2rem;
          color: rgba(#fff, 0.8);
          font-weight: 300;
          font-size: 1.6rem;
          line-height: 2.4rem;
          &:focus {
            background: transparent;
            color: rgba(#fff, 0.8);
          }
          &:read-only {
            border-bottom: 0.1rem solid #757f8d;
            background: transparent;
          }
        }
      }

      .ngb-tp-minute {
        pointer-events: none;
        input {
          margin: 0.5rem auto;
          border: 0;
          background-image: none;
          background-color: transparent;
          padding-right: 2rem;
          color: rgba(#fff, 0.8);
          font-weight: 300;
          font-size: 1.6rem;
          line-height: 2.4rem;
          &:read-only {
            border-bottom: 0.1rem solid #757f8d;
            background: transparent;
          }
        }
      }
    }
  }
}

cgdis-portal-agenda-full-calendar,
.fullcalendar-agenda {
  cgdis-portal-tabs-list,
  .tabs {
    cgdis-portal-tabs-list-item,
    .service-planner__day-selector,
    [role='tab'] [role\.tab='tab'] {
      flex-shrink: 1;
      flex-basis: 25%;
      padding: 1.5rem 0.5rem;
    }
  }

  cgdis-portal-tabs-list-next,
  cgdis-portal-tabs-list-previous {
    display: none;
  }
}
