.plan-travail {
  padding-bottom: 4rem;

  .day-selector {
    &__name {
      @extend h3;

      .-short {
        display: none;
      }
    }
    [role='tab'],
    [role\.tab='tab'] {
      box-shadow: none !important;
      text-align: center;

      &:before {
        display: none !important;
      }
    }
  }

  &__body {
    display: flex;
    flex-direction: row;

    .tabs__content {
      flex: 7rem 0 0;
      width: 7rem;
      max-width: 7rem;
    }
  }

  &__filters {
    margin-bottom: 1.26rem;
    label {
      margin-bottom: 0;
      color: $c-secondary-lighter;
      font-weight: normal;
      font-size: 1.8rem;
    }

    input {
      font-size: 1.8rem;

      &::-moz-placeholder,
      &::placeholder {
        color: $c-gray-light;
      }
    }
  }

  &__day {
    > * {
      border: 0.1rem solid #e4edf7;
      padding: 1.5rem;
      width: 7rem;
      height: 7rem;
      color: $c-secondary-lighter;
      font-size: 1.8rem;
      font-family: $ff-base;
      text-align: center;

      + * {
        border-top: 0;
      }
    }

    svg {
      width: 4rem;
      height: 4rem;
      line-height: 7rem;
    }

    .holiday {
      background-color: #b3bcff;
    }

    .sick {
      background-color: #ffd7da;
    }
  }

  @include media-breakpoint-up(lg) {
    position: relative;
    padding: 4rem 0;

    &__filters {
      width: 13rem;

      label {
        font-size: 1.4rem;
      }

      input {
        font-size: 1.4rem;
        line-height: 1;
      }
    }

    .day-selector {
      padding-right: 0;
      padding-left: 0;
      //position: absolute;
      //left: 15rem;
      //right: 1.5rem;
      //z-index: 2;

      .tabs__next,
      .tabs__prev {
        display: inline-block;
      }

      &__name {
        font-weight: bold;
        font-size: 1.3rem;
        line-height: 1;

        .-short {
          display: block;
        }
        .-long {
          display: none;
        }
      }

      &__number {
        font-weight: bold;
        font-size: 1.5rem;
        line-height: 1;
      }

      [role='tablist'] {
        overflow-x: initial;
      }
      [role='tab'],
      [role\.tab='tab'] {
        flex-basis: 0;
        outline: none;
        padding: 1.6rem 0.5rem;

        &[aria-selected='true'] {
          &,
          .day-selector__name {
            color: $c-primary;
          }
        }
      }
    }

    &__body {
      .tabs__content {
        display: flex;
        flex: 1 1 0;
        margin-right: 3rem; // size of the next day button
        width: auto;
        max-width: none;

        [role='tabpanel'] {
          flex: 1 0 4.6rem;

          &[aria-hidden='true'] {
            display: block;
          }

          &:first-child {
            .plan-travail__day {
              > * {
                border-left-width: 0.1rem;
              }
            }
          }
        }
      }
    }

    &__day {
      > * {
        border-left-width: 0;
        padding: 0.5rem;
        width: auto;
        height: 5rem;
        line-height: 4rem;

        + * {
          border-top: 0;
        }
      }

      svg {
        width: 3rem;
        height: 3rem;
        line-height: 5rem;
      }
    }
  }
}
