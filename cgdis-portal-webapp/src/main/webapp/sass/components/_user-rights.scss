cgdis-portal-admin-user-rights-roles,
cgdis-portal-user-rights-permissions {
  .description {
    white-space: wrap;
  }

  .granted-permission {
    border-radius: 0.2rem;
    padding: 0.3rem 0.6rem;
    text-wrap: wrap;

    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }

    .permission-description {
      text-wrap: wrap;
    }
  }

  .parent-permission {
    &::after {
      padding-left: 0.5em;
      content: '*';
      color: $c-primary-red;
    }
  }

  .disclaimer-parent-permission {
    margin-top: 1rem;
    font-style: italic;

    &::before {
      padding-right: 0.5em;
      content: '*';
      color: $c-primary-red;
    }
  }

  .details {
    padding: 2rem;
  }

  .permission-select {
    .ngx-select.dropdown {
      border: 1px solid #e3e5e8;
    }
  }

  .permission-search {
    position: relative;
    margin-bottom: 1rem;
    border: 1px solid #e3e5e8;

    input[type='text'] {
      outline: none;
      background: white;
      width: 100%;

      &::-webkit-input-placeholder {
        opacity: 1;
        color: #6c757d !important;
      }

      &::placeholder {
        opacity: 1;
        color: #6c757d !important;
      }

      &:-ms-input-placeholder {
        opacity: 1;
        color: #6c757d !important;
      }

      &::-moz-placeholder {
        opacity: 1;
        color: #6c757d !important;
      }

      &:-moz-placeholder {
        opacity: 1;
        color: #6c757d !important;
      }
    }

    button {
      position: absolute;
      top: 5%;
      right: 0;
      border: none;
      background: none;
      color: $c-primary;
      font-size: 1.5rem;
    }
  }
}

cgdis-portal-user-rights-permissions {
  .ngx-select__toggle.btn.form-control {
    background: none;
  }

  .select-permissions {
    .ngx-select.dropdown {
      border: none !important;

      &.open {
        width: 20em;
      }
    }
  }

  ngx-select {
    background: transparent;

    .ngx-select__clear {
      margin-right: 0 !important;
      margin-left: 0.75em;
    }

    .ngx-select a.ngx-select__clear {
      margin-top: 0;
    }

    .ngx-select__clear-icon {
      width: 0.75em !important;
    }
  }

  .ngx-select__choices {
    width: unset !important;
    min-width: 100% !important;
  }

  span.ngx-select__placeholder.text-muted.ng-star-inserted,
  span.ngx-select__selected-single {
    line-height: 2.4em !important;
  }

  .ngx-select__toggle.btn.form-control {
    @media (min-width: 768px) {
      padding: 0;
      //height: 2.4em;
      max-height: 2.4em;
      line-height: 0.25em;
      span {
        line-height: normal;
      }
    }
  }

  input.ngx-select__search.form-control.ng-star-inserted {
    height: 2em;
  }
}
