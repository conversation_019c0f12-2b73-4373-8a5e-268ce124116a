.pie-chart-style {
  height: 300px;
}

.members {
  .tabs.panel {
    box-shadow: none;
  }

  .edit-button {
    position: absolute;
    top: 1.2rem;
    right: 4rem;
    z-index: 1;
  }

  .dashboard-planning {
    display: inline-block;
    margin-right: 15px;
    margin-left: 15px;
    min-height: 500px;

    overflow: hidden;

    .week {
      float: left;
      border: 0.1rem solid #e9eef5;
      background-image: url('../../content/images/calendar-stripe.png');
      width: 110px;
      max-width: 110px;
      height: 500px;

      .slot {
        border-radius: 0;
        background-color: #fff;
        padding: 0.25rem;
        color: #9e9aa7;

        font-size: 1.2rem;

        .fc-title {
          margin-bottom: 1.5rem;
          color: #3d3b54;
          font-weight: 500;
          svg {
            width: 1em;
            height: 1em;
            fill: $c-primary;
          }
        }

        .fc-description {
          color: #9e9aa7;
        }

        .fc-time {
          margin-bottom: 1.5rem;
          color: #3d3b54;
          font-size: 1em;
        }

        .availability {
          border-left: 3px solid #b1d1f4;
          background-color: rgba(#b1d1f4, 0.05);
          padding: 0.25rem;
        }

        .professional {
          border-left: 3px solid $c-professional;
          background-color: rgba($c-professional, 0.05);
          padding: 0.25rem;
        }

        .planning {
          border-left: 3px solid #7ccb5d;
          background-color: rgba(#7ccb5d, 0.05);
          padding: 0.25rem;
        }
      }
    }
  }

  .fc-now-indicator {
    display: none;
  }
}

chart {
  display: block;
  width: 100% !important;
}

.inherit-parent-height {
  height: 100% !important;
}

cgdis-portal-general-information,
cgdis-portal-general-information-main,
cgdis-portal-preference {
  owl-carousel {
    owl-carousel-child {
      padding: 1rem 4rem !important;
      text-align: center !important;
    }
  }
}

/*Owl Carousel*/
.owl-carousel {
  margin: 0;
  padding: 2rem 0 4rem;
}
.occupancy__more {
  position: absolute;
  bottom: 0;
  left: 25%;
  z-index: 1;
  width: 50%;
}
.owl-stage {
  .owl-item {
    border-right: 1px solid #e0e0e0;
    &:nth-last-child(1 of .active) {
      border-right: 0;
    }
  }
}

/*Date in pie chart*/
.pie-chart-date {
  display: block;
  font-weight: 300;
  font-size: 90%;
  font-family: $ff-base;
}

.member-summary {
  height: 9em;
  @media (max-width: 992px) {
    height: 11em;
  }
  @media (max-width: 768px) {
    height: 12em;
  }
  @media (max-width: 576px) {
    height: 8em;
    .performance {
      width: 100%;
    }
  }
}

/*.activity-summary {

  @media (max-width: 768px) {
    height: 4.5em;
  }
}*/

.pie-chart-activity {
  height: 13rem;
}

@include media-breakpoint-down(xl) {
  .statistics-value {
    font-size: 2.8rem;
  }
}
@include media-breakpoint-up(xl) {
  .statistics-value {
    font-size: 4.5rem;
  }
}

@-moz-document url-prefix() {
  .member-summary {
    margin-top: -2rem;
    margin-bottom: 1rem;
  }
}

.bottom-none {
  margin-bottom: 0;
  padding-bottom: 0;
}
.top-none {
  margin-top: 0;
  padding-top: 0;
}

cgdis-portal-dashboard-manager-occupation {
  .section {
    padding-top: 0;
    padding-bottom: 0;
  }
}
cgdis-portal-dashboard-manager-carousel {
  .section {
    padding-top: 0;
  }
}

.owl-item {
  .highcharts-legend-item {
    display: none;
  }

  .service-plan {
    margin: 0 auto;
    min-height: 24rem;
  }
}

.owl-stage {
  padding-top: 1rem;
  padding-bottom: 1rem;
  .owl-item:last-child {
    border: none;
  }
}

.legend_md-4 {
  @media (max-width: 1400px) {
    margin-top: 1rem;
  }
}
