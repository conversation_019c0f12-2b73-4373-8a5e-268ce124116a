/*
 *     Default theme - Owl Carousel CSS File
 */

$color-base: #869791 !default;
$color-white: #fff !default;
$color-gray: #d6d6d6 !default;

//nav

$nav-color: $color-white !default;
$nav-color-hover: $color-white !default;
$nav-font-size: 14px !default;
$nav-rounded: 3px !default;
$nav-margin: 5px !default;
$nav-padding: 4px 7px !default;
$nav-background: $color-gray !default;
$nav-background-hover: $color-base !default;
$nav-disabled-opacity: 0.5 !default;

//dots

$dot-width: 10px !default;
$dot-height: 10px !default;
$dot-rounded: 30px !default;
$dot-margin: 5px 7px !default;
$dot-background: $color-gray !default;
$dot-background-active: $color-base !default;

.owl-theme {
  position: relative;

  .owl-nav {
    //position: absolute;
    //left: 0;
    //right: 0;
    //top: 0;
    //bottom: 0;
    //margin-top: 0;
    //z-index: -1;

    [class*='owl-'] {
      display: flex;
      position: absolute;
      top: 0;
      bottom: 0;
      justify-content: center;
      align-items: center;
      z-index: 9;
      transition: background-color 250ms ease-in;
      margin: 0;
      border-radius: 0.4rem;
      background-color: $c-primary !important;
      padding: 0;
      width: 4rem;
      color: #fff !important;

      &:focus,
      &:hover {
        background-color: rgba($c-primary, 0.8);
      }

      &.disabled {
        display: none;
      }

      svg {
        width: 1.6rem;
        height: 1.6rem;
      }
    }

    .owl-prev {
      left: 0;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .owl-next {
      right: 0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
