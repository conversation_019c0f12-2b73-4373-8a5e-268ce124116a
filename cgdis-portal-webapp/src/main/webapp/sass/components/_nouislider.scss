cgdis-portal-slider {
  .slider-wrapper {
    display: flex;

    .slider-buttons {
      display: flex;
      gap: 5px;
    }

    .prev-buttons {
      margin-right: 20px;
    }

    .next-buttons {
      margin-left: 20px;
    }

    .slider-label-first,
    .slider-label-last {
      font-weight: 300;
      font-size: 10px;
    }

    #sliderContainer {
      width: 100%;
    }
  }
}

cgdis-portal-slider-field {
  .noUi-handle.noUi-handle-upper,
  .noUi-handle.noUi-handle-lower {
    pointer-events: none;
  }

  .ng2-nouislider {
    margin-top: 45px !important;
    margin-bottom: 45px !important;
  }

  .noUi-connect {
    background: $c-primary;
  }

  .noUi-tooltip {
    display: none;
  }

  .noUi-value {
    margin-top: 0.5rem;
    font-size: 1rem;
  }

  .noUi-handle {
    &:after,
    &:before {
      background: none;
    }
  }

  .noUi-pips-horizontal {
    height: unset;
  }

  .noUi-marker {
    background: #aaa;
  }
}
