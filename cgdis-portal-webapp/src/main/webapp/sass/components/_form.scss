@use 'sass:math';

.form {
  display: block;
  padding: math.div($grid-gutter-width, 2) 0;

  .form-item {
    position: relative;

    input:not(type:checkbox),
    select {
      &:not(.-inline) {
        width: 100%;
      }
    }
  }

  .form-actions {
    margin-top: 1em;
  }

  cgdis-portal-icon {
    svg {
      margin-bottom: 4px;
    }
  }
}

.field-required {
  &:after {
    content: ' *';
    color: $c-primary-red;
  }
}

.likeSimpleTableHeaderFormField {
  .field-required {
    &:after {
      content: ' *';
      color: white;
    }
  }
}

.likeSimpleTableHeaderFormFieldRequired {
  label {
    &:after {
      content: ' *';
      color: white;
    }
  }
}

.functions-select {
  label {
    display: none;
  }

  .ngx-select_multiple {
    margin: 0 1rem;
    width: 95%;
  }
}

cgdis-portal-multiple-select-field,
cgdis-portal-datatable-select-filter,
.cgdis-portal-datatable-select-filter,
cgdis-portal-autocomplete-field  {
  ngx-select {
    &.ngx-select-multiple {
      .ngx-select {
        min-height: 35px;

        &::after {
          content: '▼';
          position: absolute;
          top: 8px;
          right: 12px;
        }

        &.open {
          &::after {
            content: '▲';
          }

          .ngx-select__search {
            height: 25px !important;
          }
        }
      }
    }

    &:not(.ngx-select-multiple) {
      .ngx-select {
        height: initial !important;
        max-height: initial !important;

        div {
          max-height: initial !important;
        }
      }
    }
  }


  .ngx-select__search {
    font-size: 14px !important;
  }

  .ngx-select__selected-plural {
    background: $c-gray-clear;
    font-weight: 300;
    font-size: 14px;
    color: $c-txt;
    padding: 6px 18px;
    border: 1px solid $c-gray-lightest;

    &:hover {
      background: $c-primary;

      .ngx-select__clear {
        color: $c-gray-clear !important;
      }
    }
  }

  .multiselect {
    list-style-type: none;
    margin: 0;
    //padding: 0 0 0 0.3em;
    padding-left: 15px;

    list-style-type: none;

    li {
      display: inline-flex;
      font-weight: 300;
      margin-right: 0.8em;
      background: $c-gray-clear;
      font-size: 12px;
      padding: 6px 18px;
      border: 1px solid $c-gray-late-light;




      p {
        margin-bottom: 0;
      }
    }
  }
}

//cgdis-portal-multiple-select-field,
//cgdis-portal-datatable-select-filter,
//cgdis-portal-autocomplete-field {
//  ngx-select {
//    height: 30px !important;
//    max-height: initial !important;
//
//    div {
//      max-height: initial !important;
//    }
//  }
//
//  .ngx-select__selected {
//    span {
//      overflow: hidden;
//      white-space: normal;
//      text-overflow: ellipsis;
//    }
//  }
//
//  .multiselect {
//    list-style-type: none;
//    margin: 0;
//    padding: 0 0 0 0.3em;
//
//    li {
//      float: left;
//      display: inline-flex;
//      -webkit-box-align: center;
//      font-weight: 300;
//      margin-right: 1em;
//    }
//  }
//
//  .ngx-select__selected-plural.btn.btn-default.btn-secondary.btn-xs {
//    background-color: transparent;
//    color: black;
//    font-family:
//      'Roboto',
//      -apple-system,
//      system-ui,
//      BlinkMacSystemFont,
//      'Segoe UI',
//      'Helvetica Neue',
//      Arial,
//      sans-serif;
//    font-weight: 300;
//    font-size: 1.5em;
//    padding: 0.5em;
//  }
//
//  .ngx-select__search.form-control.ng-star-inserted {
//    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUwIDUwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+IDxwYXRoIGZpbGw9IndoaXRlIiBkPSJtMjU2IDIxNGwxNzQgMTY3YzQgNCAxMSA0IDE2IDBsMzAtMzBjNS00IDUtMTEgMS0xNmwtMjEzLTIwNGMtMi0yLTUtMy04LTMtMyAwLTYgMS04IDNsLTIxMyAyMDRjLTQgNS00IDEyIDAgMTZsMzEgMzBjNCA0IDExIDQgMTYgMHoiLz48L2c+PC9zdmc+) !important;
//    background-size: 1.4rem;
//    padding-right: 1.5rem;
//  }
//}

cgdis-portal-checkbox-field {
  // horizontally align all checkbox in group
  .horizontal {
    div {
      display: inline-block;
    }

    .checkboxLabel {
      float: left;
      margin-left: 0.5em;
    }
  }
}

cgdis-portal-datatable-checkbox-group-filter {
  .checkBoxGroup {
    display: grid;
    //grid-template-columns: repeat(auto-fill, minmax(100px, 50%));
    grid-template-columns: repeat(minmax(100px, 50%));
    justify-content: space-between;
  }

  label {
    display: inline-block;
    margin-bottom: 0.5em;
  }

  .checkBoxGroupElement {
    display: inline-block;
    margin-right: 2em;
    width: max-content;
  }
}

cgdis-portal-availability-full-calendar-popup {
  .dates-header {
    ngb-timepicker {
      .ngb-tp-hour {
        input {
          @media (max-width: 768px) {
            width: 100% !important;
          }
        }
      }
    }

    .ngb-tp-spacer,
    .ngb-tp-minute {
      display: none;
    }
  }
}

ngb-timepicker {
  font-size: inherit !important;

  .timepicker-smallhour & {
    .ngb-tp-hour {
      width: 2em;
    }
  }

  font-size: inherit !important;

  .ngb-tp-hour {
    input {
      background-color: transparent !important;
      font-size: 1em;
      @media (max-width: 768px) {
        width: 30px !important;
      }
    }
  }

  .ngb-tp-minute {
    input {
      background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSI1MHB4IiBoZWlnaHQ9IjUwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiNDMTAwMkU7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik0yNSwwLjRDMTEuNCwwLjQsMC40LDExLjQsMC40LDI1czExLDI0LjYsMjQuNiwyNC42czI0LjYtMTEsMjQuNi0yNC42bDAsMEM0OS42LDExLjQsMzguNiwwLjQsMjUsMC40eg0KCSBNMjYuOCw0Ni4xdi00LjNoLTMuNXY0LjNDMTMsNDUuMiw0LjgsMzcsMy45LDI2LjhoNC4zdi0zLjVIMy45QzQuOCwxMywxMyw0LjgsMjMuMiwzLjl2NC4zaDMuNVYzLjlDMzcsNC44LDQ1LjIsMTMsNDYuMSwyMy4yaC00LjMNCgl2My41aDQuM0M0NS4yLDM3LDM3LDQ1LjIsMjYuOCw0Ni4xeiIvPg0KPHBvbHlnb24gY2xhc3M9InN0MCIgcG9pbnRzPSIyNSwyNC43IDE4LjIsMTIuNiAxNS4yLDE0LjMgMjIuOSwyOC4yIDM1LjYsMjguMSAzNS42LDI0LjYgIi8+DQo8L3N2Zz4NCg==');
      background-position: right 1rem top 50%;
      background-size: 1.4rem;
      background-repeat: no-repeat;
      background-color: transparent !important;
      padding-right: 3.5rem;
      font-size: 1em;
      @media (max-width: 768px) {
        background-size: 1rem;
        padding-right: 2.5rem;
        width: 50px !important;
      }
    }

    &.minutes-disabled {
      border: 0;
      pointer-events: none;
    }
  }
}

.defaultCursor {
  cursor: default !important;
}

cgdis-portal-datatable-person-filter {
  .ngx-select {
    .ngx-select__selected {
      .ngx-select__toggle {
        flex-direction: row;
        align-items: baseline;
        border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        border-radius: 0;
        background: transparent;
        padding: 0.5rem;
        height: 2em;
        color: white;
        font-weight: 400;
        font-size: 1.05em;
        line-height: normal;

        .ngx-select__clear {
          color: #ffffff;
        }

        .dropdown-toggle {
          background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBzdHlsZT0iZmlsbDp3aGl0ZTsiIGQ9Im0gMjU1Ljk0MjcyLDI5OC4wNTcyOCAtMTc0LC0xNjcgYyAtNCwtNCAtMTEsLTQgLTE2LDAgbCAtMzAsMzAgYyAtNSw0IC01LDExIC0xLDE2IGwgMjEzLDIwNCBjIDIsMiA1LDMgOCwzIDMsMCA2LC0xIDgsLTMgbCAyMTMsLTIwNCBjIDQsLTUgNCwtMTIgMCwtMTYgbCAtMzEsLTMwIGMgLTQsLTQgLTExLC00IC0xNiwwIHoiLz48L3N2Zz4=')
            no-repeat right 0.5rem top 50% !important;
          padding-right: 1.5rem;
          margin-top: 5px;
        }
      }
    }
  }
}
