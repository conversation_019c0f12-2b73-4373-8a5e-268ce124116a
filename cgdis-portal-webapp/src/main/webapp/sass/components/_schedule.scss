.expanded {
  height: auto;
  li {
    max-height: none;
  }
}

.expand-action {
  &:hover {
    color: $c-primary;
    text-decoration: underline $c-primary;
  }
}

.prestation-remove {
  position: absolute;
  @media (max-width: 768px) {
    right: 5px;
    min-width: 25px;
    min-height: 25px;
  }
  top: 50%;
  right: 3rem;
  transform: translateY(-50%);
}

.prestation-deletion {
  font-weight: 400 !important;
  font-size: 16px;
}

cgdis-portal-schedule-result-person.ng-star-inserted {
  width: 100%;

  .icon-pro {
    display: unset !important;
    margin: 0 !important;
    width: 1.5em !important;
    height: 1.5em !important;
  }
}

.servicePlanScheduleIcons {
  float: right;
  margin-top: 0.5em;
  margin-right: 0.5em;

  @media (max-width: 600px) {
    position: absolute;
    float: unset;
    margin-top: unset;
    margin-right: unset;
    margin-left: 1%;
  }
  .dropdown__trigger {
    svg {
      fill: currentColor;
    }
  }
}

.availabilityScheduleIcons {
  margin: 0.5rem;

  @include media-breakpoint-up('md') {
    position: absolute;
    top: 1.5rem;
    right: 1.1rem;
    margin: 0;
  }
}

#schedule-export-pdf-button {
  //.dropdown__trigger:hover svg {
  //  fill: $c-primary-red;
  //}

  .ng-star-inserted.icon-chevron {
    display: none;
  }

  ul {
    top: 3em;
    right: 0.5em;
    left: auto;
    width: 15em;
    list-style-type: none;
  }

  li:hover {
    cursor: pointer;
    a {
      color: $c-primary-red;
    }
  }
}

.dropdown-hidden {
  display: none;
}

.dropdown-in-list {
  top: 3em;
  left: auto;
  width: 8.1em;
  list-style-type: none;
  li {
    width: 10em;
    font-size: 12px;
  }
  li:hover {
    cursor: pointer;
    a {
      color: $c-primary-red;
    }
  }
}

.last-dropdown {
  top: -1.5em;
}

.hide-scroll {
  .scrollable-content {
    overflow: hidden !important;
  }
}

#schedule-export-pdf-switcher {
  top: 5.5rem;
  right: 6rem;
  width: 8em !important;
  min-width: unset;

  li {
    width: 10em;
    font-size: 12px;
  }
}

.number-person-available {
  position: absolute;
  color: $c-primary-red;
  @media (max-width: 768px) {
    right: 4.2rem;
  }
  top: 34%;
  right: 6.1rem;
  width: 2rem;
  height: 2rem;
  text-overflow: ellipsis;
}

cgdis-portal-service-plan-dropdown,
cgdis-portal-service-plan-list {
  .icon-barracked.-job {
    padding-bottom: 0.3rem;
    width: 2rem;
    height: 2.5rem;
    color: $c-primary-red;
  }

  .ngx-select__toggle.btn.form-control {
    background: none;
  }

  .ngx-select.dropdown {
    border: none;
  }
  ngx-select {
    background: transparent;
    font-weight: 300;

    .ngx-select__clear {
      margin-right: 0 !important;
      margin-left: 0.75em;
    }
    .ngx-select a.ngx-select__clear {
      margin-top: 0;
    }
    .ngx-select__clear-icon {
      width: 0.75em !important;
      color: white !important;
    }

    .text-muted {
      color: black !important;
    }

    .dropdown-header {
      display: block;
      margin-bottom: 0;
      padding: 0.5rem 1.5rem;
      color: black;
      font-weight: 400;
      font-size: 1.3rem;
      white-space: nowrap;
    }
  }

  .ngx-select__choices {
    max-width: 60em !important;
    max-height: 40em !important;
  }

  .ngx-select__item {
    color: gray;
    font-weight: 300;
    font-size: 1.3rem;
  }

  .ngx-select__item_disabled {
    color: $c-primary-red !important;
    font-weight: 500 !important;
    font-size: 1.3rem !important;
  }

  span.ngx-select__placeholder.text-muted.ng-star-inserted,
  span.ngx-select__selected-single {
    font-weight: 300;
    line-height: 2.4em !important;
  }

  .ngx-select__toggle.btn.form-control {
    @media (min-width: 768px) {
      padding: 0;
      //height: 2.4em;
      max-height: 2.4em;
      line-height: 0.25em;
      span {
        line-height: normal;
      }
    }
  }
}

.disabled-select {
  .ngx-select__disabled {
    cursor: unset !important;
    background: transparent !important;
  }

  .ngx-select__toggle-buttons {
    display: none !important;
  }
}

.last-team {
  ul {
    top: -1.5em;
  }
}
