.form-item {
  .ngx-select__selected {
    padding-bottom: $input-border-bottom-size;

    .ngx-select__toggle {
      padding: $input-padding;
      line-height: $input-line-height;
    }
  }
}

.ngx-select {
  border: 0.1rem solid rgba(117, 127, 141, 0.2);

  a {
    &.ngx-select__clear {
      color: $c-primary;
    }
  }

  .ngx-select__item-group {
    .ngx-select__item {
      font-weight: $inputFontWeight;
      white-space: normal;
    }
  }

  .select-noborder & {
    border: 0;
  }

  .ngx-select__toggle {
    .dropdown-toggle {
      background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgNTEyIDUxMiIKICAgaWQ9Imljb24tY2hldnJvbiI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE4NyI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxODUiIC8+CiAgPHBhdGgKICAgICBzdHlsZT0iZmlsbDojYzEwMDJlIgogICAgIGQ9Im0gMjU1Ljk0MjcyLDI5OC4wNTcyOCAtMTc0LC0xNjcgYyAtNCwtNCAtMTEsLTQgLTE2LDAgbCAtMzAsMzAgYyAtNSw0IC01LDExIC0xLDE2IGwgMjEzLDIwNCBjIDIsMiA1LDMgOCwzIDMsMCA2LC0xIDgsLTMgbCAyMTMsLTIwNCBjIDQsLTUgNCwtMTIgMCwtMTYgbCAtMzEsLTMwIGMgLTQsLTQgLTExLC00IC0xNiwwIHoiCiAgICAgaWQ9InBhdGg0MTgxIiAvPgo8L3N2Zz4K') no-repeat right 0.5rem top 50%;
      background-size: 1.4rem;
      padding-right: 1.5rem;

      &::after {
        visibility: hidden;
      }
    }
  }
}

ngx-select {
  &.small-select {
    .ngx-select {
      padding: 0;
    }

    .ngx-select__toggle.btn.form-control {
      //@media (min-width: 768px) {
      //  height: 2.4em;
      //  max-height: 2.4em;
      //  line-height: 0.25em;
      padding: 0;
      //span {
      //  line-height: normal;
      //}
      //}
    }
  }
}

input.ngx-select__search.form-control.ng-star-inserted {
  //height: 56px;
  //font-size: 1.05em;
  padding: 0.5rem;
  //padding: 0.19rem 0 0.19rem 0 !important;
  font-size: 1em;
  line-height: normal;
}

/*li.ngx-select__item-group.ng-star-inserted {
  width: 100%;
  display: flex;
}*/
