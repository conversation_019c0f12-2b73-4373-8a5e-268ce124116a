.service-list {
  &__heading {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: $c-primary;
    padding: 1.5rem;
    color: #fff;
    font-size: 1.3rem;
    font-family: $ff-heading;
    text-transform: uppercase;

    .tabs__content & {
      padding-right: 0;
      padding-left: 0;
    }

    svg {
      margin-right: 0.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  [role='tab'],
  [role\.tab='tab'] {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
  }

  @include media-breakpoint-up(sm) {
    + .legend-box {
      text-align: right;
    }
  }

  @include media-breakpoint-down(md) {
    /*@media (min-width: 600px) {
        padding: 2rem 1.5rem;
      }*/

    &__heading {
      display: none;
    }

    [role='tablist'] {
      display: none;
    }
  }

  @include media-breakpoint-down(sm) {
    //@media (min-width: 600px) {
    padding: 1rem 0;
    //}
  }

  @include media-breakpoint-up(lg) {
    .select2 {
      display: none;
    }

    [role='tablist'] {
      min-width: 26rem;
    }

    [role='tab'],
    [role\.tab='tab'] {
      + [role='tab'],
      + [role\.tab='tab'] {
        border-top: 0.1rem solid $c-gray-lightest;
      }
    }

    [role='tabpanel'] {
      display: flex;
      flex-direction: row;
      justify-content: left;
      align-items: center;
      color: $c-secondary-lighter;
      font-size: 1.2rem;

      + [role='tabpanel'] {
        border-top: 0.1rem solid $c-gray-lightest;
      }

      &[aria-hidden='true'] {
        display: flex;
      }

      .planner__people {
        max-height: none;
      }
    }
  }
}

.service {
  &__status {
    flex-grow: 0;
    min-width: 3rem;
    text-align: center;
  }

  &__details {
    display: flex;
    flex-grow: 1;
    flex-direction: row;
    justify-content: left;
    align-items: center;
    color: $c-secondary-lighter;
    font-size: 1.2rem;
    font-family: $ff-base;

    svg {
      margin-right: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  &__name {
    font-weight: 500;
  }

  &__type {
    display: block;
    font-weight: 300;
  }

  @include media-breakpoint-down(md) {
    &__schedule {
      display: flex;
      align-items: center;
      padding: 3rem 0;
      color: $c-secondary-lighter;
      font-weight: 500;

      svg {
        margin-right: 3rem;
        width: 3.5rem;
        height: 3.5rem;
      }
    }
  }

  @include media-breakpoint-up(lg) {
    &__autofill {
      flex: 0 0 7.5rem;
      text-align: center;
    }

    &__schedule {
      flex: 0 0 20%;
      text-align: center;

      &.-automatic {
        display: flex;
        justify-content: center;
        align-items: center;

        svg {
          margin-right: 1rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }

      &:not(.-automatic) {
        svg {
          display: none;
        }
      }

      .service-list__heading & {
        svg {
          display: inline-block;
        }
      }
    }

    &__resources {
      flex: 0 0 80%;
      overflow-x: auto;
    }
  }
}
