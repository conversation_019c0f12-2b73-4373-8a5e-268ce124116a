.service-plan-tile {
  padding: 0;
  text-align: left;
  &:hover {
    cursor: pointer;
  }

  .tile-content {
    padding: 2.5em;
    width: 100%;
    height: 100%;
  }
}

.service-plan {
  width: 26rem;
  height: 26rem;

  &-list-empty {
    @include listEmpty();
  }
}

.bar-chart-style {
  margin-top: -8px;
  margin-left: -10px;
  width: 128%;
  &-nodata {
    margin-top: -8px;
    margin-left: -10px;
    width: 128%;
    .highcharts-color-0 {
      fill: $c-gray-lighter;
      stroke: #fff;
    }
  }
}

.tile-wrapper {
  margin: 0.5em;
}

.dropdown-container {
  min-width: 40em !important;
  height: 43rem;
  max-height: 50em;
  overflow: auto;
  overscroll-behavior: none;
}

ul.dropdown-list > li:hover {
  cursor: pointer;
  color: $c-primary;
}

#admin-service-plan-list-table-Id,
#admin-service-plan-model-version-list-table-Id,
#admin-service-plan-version-list-table-Id {
  .datatable-header-cell.sortable {
    .datatable-header-cell-wrapper {
      max-width: 80%;
    }
  }
}

#admin-service-plan-model-list-table-Id,
#admin-service-plan-list-table-Id {
  .datatable-body-row {
    height: 70px;
  }
}

#admin-service-plan-model-version-list-table-Id,
#admin-service-plan-version-list-table-Id {
  // "Action" column
  .datatable-header-cell:last-of-type {
    text-align: center;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    @media (min-width: 768px) {
      width: 100px !important;
    }
    .datatable-header-cell-wrapper {
      width: 100%;
    }
  }

  .datatable-body-cell:last-of-type {
    -ms-flex-negative: 0;

    position: relative;
    flex-shrink: 0;
    margin-right: auto;
    margin-left: auto;
    width: 100px !important;
  }
}

/*input[name='totalSlots']{
  width: 35% !important;
}*/

/*#admin-service-plan-version-create-id {
  cgdis-portal-slider-field {
    cgdis-portal-number-field {
        .arrow-down {
          right: unset !important;
        }
        .arrow-up {
          right: unset !important;
        }
    }
  }
}*/

cgdis-portal-service-plan-details {
  cgdis-portal-page-header {
    cgdis-portal-service-plan-dropdown {
      cgdis-portal-up-or-down-button {
        button {
          span {
            display: inline-block;
            vertical-align: bottom;
            max-width: 27vw;
            height: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  #tabsListDaySelector {
    cgdis-portal-tabs-list-item {
      &[role='tab'],
      &[role\.tab='tab'] {
        padding-bottom: 0.8rem;
      }
      .day-selector__number {
        margin-bottom: 1.2rem;
      }
    }
  }

  cgdis-portal-vertical-tabs {
    .ngx-select__selected-single {
      align-items: flex-end !important;
    }
  }

  cgdis-portal-link-with-icon {
    a.btn {
      svg {
        fill: currentColor;
      }
    }
  }

  .button-group {
    display: inline-flex;
    align-items: baseline;

    > * {
      padding: 0.5rem;
    }
  }
}

/*
@media (min-width: 768px) {
  cgdis-portal-service-plan-filter {
    .close-icon {
      right: 6rem;
      bottom: 0.5rem;
    }
  }
}
*/

.search-filter {
  justify-content: center;
  margin: 0.5em 0 1em;
  background: $c-primary;

  padding: 0.5em 2em;

  .col-md-2 {
    padding-bottom: 15px;
  }

  @media (max-width: 400px) {
    padding: 1rem 0.5rem;
  }

  label {
    color: white;
  }

  @media (max-width: 768px) {
    div:not(.align-self-stretch) {
      display: flex;
      flex-direction: column;

      cgdis-portal-datatable-text-filter,
      cgdis-portal-datatable-number-filter {
        width: 100%;

        .datatable-input {
          width: 100%;
        }
      }
    }
  }

  .service-plan-filter__intervention-type {
    @media (min-width: 992px) {
      flex-grow: 1;
      max-width: inherit;
    }
  }

  .service-plan-filter__toggle-filter__row {
    @media (min-width: 768px) {
      width: inherit;
      flex: inherit;
    }
    @media (max-width: 768px) {
      display: flex;
      //flex-direction: row;
    }

    .service-plan-filter__toggle-filter__content {
      @media (max-width: 768px) {
        display: inline-flex;
      }

      .checkboxLabel {
        @media (min-width: 768px) {
          margin-top: 1.4rem;
        }

        margin-bottom: 0;
        @media (max-width: 400px) {
          display: flex;
          justify-content: end;
        }
        @media (max-width: 768px) {
          display: flex;
          justify-content: end;
        }
      }
    }
  }

}

.service-plan-list-filter {
  display: flex;
  align-items: center;
  gap: 5rem;
  justify-content: space-between;
}

.service-plan-list-footer {
  .total-item {
    float: left;
    margin: 1rem 0;
    font-size: 1.4rem;
    text-align: right;

    @media (min-width: 768px) {
      padding: 2rem 2rem 2rem 2em;
    }
  }
}
