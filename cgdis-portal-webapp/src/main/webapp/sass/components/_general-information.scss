cgdis-portal-availability-planning-log-as {
  label {
    font-weight: 400;
  }

  .ml-4 {
    display: inline-flex !important;
  }

  .small-icon {
    width: 2rem;
    height: 2rem;
    line-height: 1.5rem;
  }

  .input-group {
    display: inline-block;
    align-items: center;
    width: auto;
  }

  .log-as-list-mobile {
    display: flex !important;
    width: 100%;

    span {
      white-space: initial !important;
    }
  }

  .select-log-as-list-mobile {
    //width: 280px;
    max-width: 90%;
  }
}

.alert-mail-profil {
  border-bottom-color: #f00;
  background-color: #fdd;
  padding: 1rem;
  padding-left: 0.5rem;
  margin-left: 1rem;
  font-weight: bold;
}

cgdis-portal-admin-people-management-list {
  .people-management-list-header-icons {
    display: flex;
    column-gap: 1rem;
  }

  .person-detail-row {
    .section {
      margin: unset;

      tr {
        background: unset !important;
      }

      td {
        @media (min-width: 768px) {
          height: unset !important;
        }
      }
    }
  }
}

.galon_grade {
  width: 4rem;
  height: 4rem;
}

cgdis-portal-admin-people-management-functions {
  ngx-select {
    display: inline-block;
  }
}

cgdis-portal-current-situation,
cgdis-portal-admin-people-management-list,
cgdis-portal-allowance-scheduler-management,
cgdis-portal-optional-backup-group-management-list,
cgdis-portal-admin-people-management-functions,
cgdis-portal-person-function-operational,
cgdis-portal-entity-filter,
cgdis-portal-allowance,
cgdis-portal-allowance-search,
cgdis-portal-availability-planning-log-as {
  cgdis-portal-field.ng-star-inserted input {
    background: transparent;
  }

  .general-information-header {
    border: unset;
    background: unset;
  }

  .separator {
    border-bottom: 1px solid #666666;
  }

  .status-filter {
    margin-bottom: 2rem;

    label {
      color: #3d3b54;
      font-weight: 500;
      font-size: 16px;
    }

    .ngx-select_multiple {
      border: 0.1rem solid rgba(117, 127, 141, 0.2) !important;

      .ngx-select__search.form-control.ng-star-inserted {
        //background: rgba(0, 0, 0, 0) url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgNTEyIDUxMiIKICAgaWQ9Imljb24tY2hldnJvbiI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE4NyI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxODUiIC8+CiAgPHBhdGgKICAgICBzdHlsZT0iZmlsbDojYzEwMDJlIgogICAgIGQ9Im0gMjU1Ljk0MjcyLDI5OC4wNTcyOCAtMTc0LC0xNjcgYyAtNCwtNCAtMTEsLTQgLTE2LDAgbCAtMzAsMzAgYyAtNSw0IC01LDExIC0xLDE2IGwgMjEzLDIwNCBjIDIsMiA1LDMgOCwzIDMsMCA2LC0xIDgsLTMgbCAyMTMsLTIwNCBjIDQsLTUgNCwtMTIgMCwtMTYgbCAtMzEsLTMwIGMgLTQsLTQgLTExLC00IC0xNiwwIHoiCiAgICAgaWQ9InBhdGg0MTgxIiAvPgo8L3N2Zz4K") no-repeat right 0.5rem top 50%;
        background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUwIDUwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+IDxwYXRoIGZpbGw9IndoaXRlIiBkPSJtMjU2IDIxNGwxNzQgMTY3YzQgNCAxMSA0IDE2IDBsMzAtMzBjNS00IDUtMTEgMS0xNmwtMjEzLTIwNGMtMi0yLTUtMy04LTMtMyAwLTYgMS04IDNsLTIxMyAyMDRjLTQgNS00IDEyIDAgMTZsMzEgMzBjNCA0IDExIDQgMTYgMHoiLz48L2c+PC9zdmc+) !important;
        background-size: 1.4rem;
        padding-right: 1.5rem;
      }
    }
  }

  .information-rating {
    color: $c-primary;

    svg {
      &:hover {
        cursor: pointer;
        color: $c-primary-lighter;
      }
    }
  }

  cgdis-portal-form {
    form {
      max-width: 100%;
    }
  }

  .ngx-select__toggle.btn.form-control {
    justify-content: unset;
    background: none;
  }

  @include ngx-select();

  .datatable-row-center {
    .datatable-header-cell:first-child {
      //display: none;
    }
  }
}

cgdis-portal-admin-people-medical-information-list {
  cgdis-portal-datatable-datepicker-filter {
    background-position: right 1rem top;
    background-size: 1.6rem;

    background-repeat: no-repeat;

    input.-with-datepicker,
    input[type='date'] {
      filter: brightness(0) saturate(100%) invert(19%) sepia(7%) saturate(2612%)
        hue-rotate(205deg) brightness(97%) contrast(82%);
      border-bottom: 0.1rem solid #2c3e7b;
      background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUwIDUwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1MCA1MDsiIHhtbDpzcGFjZT0icHJlc2VydmUiPiA8cGF0aCBmaWxsPSJ3aGl0ZSIgZD0iTTQ2LjEsNC44aC02Yy0wLjcsMC0xLjMsMC42LTEuMywxLjNjMCwwLjcsMC42LDEuMywxLjMsMS4zaDZjMC43LDAsMS4zLDAuNiwxLjMsMS4zdjguMUgzVjguN2MwLTAuNywwLjYtMS4zLDEuMy0xLjNoNi44IGMwLjcsMCwxLjMtMC42LDEuMy0xLjNjMC0wLjctMC42LTEuMy0xLjMtMS4zSDQuM2MtMi4xLDAtMy45LDEuNy0zLjksMy45djM1LjFjMCwyLjEsMS43LDMuOSwzLjksMy45aDQxLjhjMi4xLDAsMy45LTEuNywzLjktMy45IFY4LjdDNTAsNi41LDQ4LjIsNC44LDQ2LjEsNC44eiBNNDcuNCw0My44YzAsMC43LTAuNiwxLjMtMS4zLDEuM0g0LjNjLTAuNywwLTEuMy0wLjYtMS4zLTEuM1YxOS4zaDQ0LjRMNDcuNCw0My44TDQ3LjQsNDMuOHogTTE5LjksNy40aDEwLjRjMC43LDAsMS4zLTAuNiwxLjMtMS4zYzAtMC43LTAuNi0xLjMtMS4zLTEuM0gxOS45Yy0wLjcsMC0xLjMsMC42LTEuMywxLjNDMTguNiw2LjgsMTkuMiw3LjQsMTkuOSw3LjR6IE0xNS4yLDE0LjQgYzEuNSwwLDIuNy0xLjIsMi43LTIuN2MwLTEtMC42LTEuOS0xLjQtMi4zVjEuNmMwLTAuNy0wLjYtMS4zLTEuMy0xLjNjLTAuNywwLTEuMywwLjYtMS4zLDEuM3Y3LjdjLTAuOCwwLjUtMS40LDEuMy0xLjQsMi4zIEMxMi41LDEzLjIsMTMuOCwxNC40LDE1LjIsMTQuNHogTTM1LjEsMTQuNGMxLjUsMCwyLjctMS4yLDIuNy0yLjdjMC0xLTAuNi0xLjktMS40LTIuM1YxLjZjMC0wLjctMC42LTEuMy0xLjMtMS4zIGMtMC43LDAtMS4zLDAuNi0xLjMsMS4zdjcuN2MtMC44LDAuNS0xLjQsMS4zLTEuNCwyLjNDMzIuNCwxMy4yLDMzLjYsMTQuNCwzNS4xLDE0LjR6Ii8+IDxwYXRoIGZpbGw9IndoaXRlIiBkPSJNNDIuNSw0Mi41aC01LjRjLTEuMSwwLTItMC45LTItMnYtNS40YzAtMS4xLDAuOS0yLDItMmg1LjRjMS4xLDAsMiwwLjksMiwydjUuNEM0NC41LDQxLjYsNDMuNiw0Mi41LDQyLjUsNDIuNXoiLz48L3N2Zz4=);
      padding-top: 0;
      padding-bottom: 0;
    }

    .close-icon {
      right: 4rem;
      bottom: 1.2rem;
    }

    @media (max-width: 1200px) {
      input.datatable-input {
        width: 100% !important;
      }
    }
  }

  .filter-bar {
    border-top: 0.1rem solid rgba(117, 127, 141, 0.2);
  }

  .filter-bar-header {
    label {
      color: #3d3b54;
      font-weight: 500;
      font-size: 16px;
    }
  }

  .filter-bar-fields {
    label {
      color: #3d3b54;
    }

    .ngx-select_multiple {
      border: 0.1rem solid rgba(117, 127, 141, 0.2) !important;

      .ngx-select__search.form-control.ng-star-inserted {
        background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhbHF1ZV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDUwIDUwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+IDxwYXRoIGZpbGw9IndoaXRlIiBkPSJtMjU2IDIxNGwxNzQgMTY3YzQgNCAxMSA0IDE2IDBsMzAtMzBjNS00IDUtMTEgMS0xNmwtMjEzLTIwNGMtMi0yLTUtMy04LTMtMyAwLTYgMS04IDNsLTIxMyAyMDRjLTQgNS00IDEyIDAgMTZsMzEgMzBjNCA0IDExIDQgMTYgMHoiLz48L2c+PC9zdmc+) !important;
        background-size: 1.4rem;
        padding-right: 1.5rem;
      }
    }

    input {
      display: block;
    }

    input.datatable-input {
      //height: 35px !important;
      color: #3e3c54 !important;
    }
  }

  .mat-expansion-panel {
    box-sizing: border-box;
  }
  /*TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
  .mat-checkbox-checked .mat-checkbox-background {
    background-color: #2c3e7b !important;
  }

  .mat-ripple-element {
    background-color: rgba(120, 135, 185, 0.25) !important;
  }

  .ngx-select__toggle.btn.form-control {
    background: none;
  }

  @include ngx-select();

  cgdis-portal-datatable-select-filter,
  .cgdis-portal-datatable-select-filter {
    div.ngx-select__toggle.btn.form-control {
      border-bottom: 0.1rem solid #3e3c54;

      span {
        color: #3e3c54;
      }

      i {
        filter: brightness(0) saturate(100%) invert(19%) sepia(7%)
          saturate(2612%) hue-rotate(205deg) brightness(97%) contrast(82%);
      }
    }

    @media (max-width: 1200px) {
      div.dropdown {
        padding-right: 0;
      }
    }
  }

  cgdis-portal-datatable-text-filter {
    input.datatable-input {
      border-bottom: 0.1rem solid #3e3c54 !important;
      color: #ffffff !important;
    }

    @media (max-width: 1200px) {
      input.datatable-input {
        width: 100%;
      }
    }
  }

  @media (max-width: 768px) {
    .entity-list {
      margin-top: 2rem;
    }
  }
}

@media (max-width: 768px) {
  cgdis-portal-datatable-toggle-filter {
    label {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
  }
}

.general-information-tab-inline {
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-size: 20px;
  list-style-type: none;

  li {
    float: left;

    button {
      border: none;
      background: none;
      padding-right: 1.5rem;
      padding-left: 1.5rem;

      &.person-selector {
        background: white;
      }
    }
  }

  .selected {
    //font-weight: bold;
    color: $c-primary;
  }

  .slider {
    display: flex;
    padding: 1rem 0;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
  }

  .slide {
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    scroll-snap-align: start;
    text-align: center;
  }
}

.mat-expansion-panel-body {
  //@media (max-width: 768px) {
  padding: 0 10px 16px !important;
  //}
}

.export-activity {
  margin-top: -4%;
  margin-right: 3%;
}

.export-penalty {
  margin-top: -4%;
  margin-right: 7%;
}

.general-information-tab {
  margin: 0;
  padding: 0;
  overflow: hidden;
  list-style-type: none;

  li {
    float: left;

    button {
      border: none;
      background: none;
      padding-right: 1.5rem;
      padding-left: 1.5rem;
      color: #000000;
    }

    &.separator {
      border-right: 1px solid rgba(0, 0, 0, 0.1);

      &:last-child {
        border-right: none;
      }

    }

  }

  .selected {
    color: $c-primary;
    font-weight: bold;
  }

  .seperator {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.general-information-label {
  display: block;
  padding: 0 0.5rem;
  color: #3d3b54;
  font-weight: normal;
  font-size: inherit;
  font-family: 'Archivo Narrow', Georgia, 'Times New Roman', Times, serif;
}

.accordion__group {
  margin-bottom: 1.5rem !important;
}

.mat-expansion-indicator {
  color: $c-primary;
}

.operational-function-rating {
  padding: 0 16px 0 16px;
}

cgdis-portal-operational-functions {
  ul {
    list-style-type: none;
  }

  .rating {
    svg {
      color: $c-primary;
    }
  }

  datatable-row-wrapper {
    &:nth-child(odd) {
      .person-detail-row {
        background: transparent;
      }
    }

    &:nth-child(even) {
      .person-detail-row {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.width-80 {
  button {
    width: 89%;
    max-width: 89%;
  }
}

#member-list {
  .datatable-body-row {
    min-height: 70px;
  }
}

@media (max-width: 800px) {
  cgdis-portal-totals-information {
    datatable-body-row {
      .datatable-row-center {
        datatable-body-cell:not(:first-child) {
          .datatable-body-cell-label {
            display: flex;
            text-align: center;
          }
        }
      }
    }
  }
}

#member-medical-information-list,
cgdis-portal-admin-people-medical-information-list {
  dl.legend-box.-inline {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: 1rem 0;
  }

  .datatable-body-row {
    cursor: default;
  }

  .people-aptitude {
    padding-bottom: 0.5rem;
  }

  .ngx-datatable .datatable-body-cell {
    overflow-x: visible !important;
  }

  .datatable-body-cell-label {
    display: inline;
    width: 180px !important;
    overflow: visible;
  }

  .aptitude-category {
    padding: 0 0.5em;
    white-space: normal;
  }

  .aptitude-expired {
    color: $c-primary-red;
    font-weight: 600;
  }

  .people-aptitude-badge {
    //padding-right: .8em;
    //padding-left: .8em;
    border-radius: 10rem;
    height: fit-content;
    color: #fff;
    text-align: center;
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  @media (min-width: 769px) {
    .people-apt {
      background-color: #28a745;
    }

    .people-apt-with-restriction {
      background-color: #b8d38f;
    }

    .people-inapt {
      background-color: #dc3545;
    }

    .people-temporary-inapt {
      background-color: #ff6500;
    }
  }

  @media (max-width: 768px) {
    .people-apt {
      color: #28a745;
    }

    .people-apt-with-restriction {
      color: #b8d38f;
    }

    .people-inapt {
      color: #dc3545;
    }

    .people-temporary-inapt {
      color: #ff6500;
    }
  }

  .people-restriction {
    padding-left: 0;
    white-space: normal;
  }
}

cgdis-portal-restrictions-information,
cgdis-portal-aptitudes-information {
  datatable-row-wrapper {
    .col-2 {
      font-weight: 500;
    }

    .col-5 {
      font-weight: 500;
    }
  }

  .aptitude-expired {
    color: $c-primary-red;
    font-weight: 600;
  }
}

.block-intervention-types {
  display: block;
  margin-right: auto;
  margin-left: auto;
  list-style: none;
}

cgdis-portal-general-information-activity-tab {
  .home-nav {
    @media (max-width: 768px) {
      padding: 1rem 0;
    }
  }

  .week-selector {
    margin-top: 1rem !important;

    &__one {
      @media (max-width: 768px) {
        margin-top: 1rem !important;
        margin-bottom: 0;
        padding: 0 !important;
      }

      &__day__name {
        @media (max-width: 768px) {
          font-size: 2.1rem;
        }
      }
    }

    &__title {
      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    &_one {
      @media (max-width: 768px) {
        margin-top: 1rem;
      }
    }
  }
}

cgdis-portal-custom-range {
  .col-4 {
    padding: 0 !important;
  }
}

.operational-contact-mobile {
  margin-right: 1.5rem;
  margin-left: 1.5rem;
  @media (max-width: 900px) {
    margin-right: 3rem !important;
    margin-bottom: 1rem;
  }
}

.general-contact-mobile-padding {
  display: flex;
  justify-content: center;
  padding: 0 1.5rem;
  @media (min-width: 768px) {
    margin-bottom: -15px;
  }
}

.medico_table table,
.medico_table th,
.medico_table td {
  border: 1px solid #b1b1b1;
}

.medico_table {
  width: 100%;
}

.medico_table th {
  color: $c-primary;
}

.medico_table th,
.medico_table td {
  padding: 5px;
}

cgdis-portal-medical-report-information-detail {
  h3 {
    color: $c-primary;
    font-weight: 700;
    font-size: 18px;
    font-family: $ff-base;
  }
}

cgdis-portal-admin-people-management-functions,
cgdis-portal-person-function-operational {
  .ngx-select__toggle.btn.form-control {
    background: none;
  }

  .ngx-select.dropdown {
    border: none;
  }

  ngx-select {
    border: none;
    background: transparent;

    .ngx-select__clear {
      margin-right: 0 !important;
      margin-left: 0.75em;
    }

    .ngx-select a.ngx-select__clear {
      margin-top: 0;
    }

    .ngx-select__clear-icon {
      width: 0.75em !important;
    }
  }

  .ngx-select__choices {
    width: unset !important;
    min-width: 100% !important;
  }

  span.ngx-select__placeholder.text-muted.ng-star-inserted {
    line-height: 2.4em !important;
  }

  .ngx-select__toggle.btn.form-control {
    @media (min-width: 800px) {
      padding: 0;
      height: 2.4em;
      max-height: 2.4em;
      line-height: 0.25em;
      span {
        line-height: normal;
      }
    }
  }

  input.ngx-select__search.form-control.ng-star-inserted {
    height: 2em;
  }

  .planner {
    &__people {
      align-items: flex-start;
      padding: 1.5rem;
    }

    &__header-jobs {
      height: 11rem;
      max-height: 11rem;
      .planner__people-list {
        display: flex;
        flex-direction: column;
        transform: rotate(-90deg);
        max-width: 11rem;
        height: 11rem;
        max-height: 10rem;
      }
    }
  }

  .schedule {
    @include media-breakpoint-up(lg) {
      .tabs &__selector {
        .schedule,
        > [role='tab'],
        > [role\.tab='tab'] {
          &.-actions {
            height: 11rem;
            max-height: 11rem;
          }
        }
      }
    }
  }

  .position-scrollbar {
    max-height: 11rem;
  }
}
