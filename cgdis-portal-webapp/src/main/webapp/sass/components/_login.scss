.login-title {
  display: block;
  padding-bottom: 40px;
  width: 100%;
  color: $c-gray-darker;
  font-size: 24px;
  line-height: 1.2;
  text-align: center;
}

.text-center {
  padding-top: 22px;
  text-align: center;
  a {
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    margin: 0;
    color: $c-gray-mid-dark;
    font-size: 13px;
    line-height: 1.7;
    text-decoration: underline;

    &:focus {
      outline: none !important;
    }

    &:hover {
      color: $c-primary;
    }
  }
}

.txt1 {
  color: $c-gray-mid-dark;
  font-size: 13px;
  line-height: 1.5;
}

.txt2 {
  color: $c-gray-mid-dark;
  font-size: 13px;
  line-height: 1.5;
}

.limiter {
  margin: 0 auto;
  width: 100%;
}

.container-login {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  background-color: $c-body;
  padding: 15px;
  width: 100%;
  min-height: 85vh;
  font-family: $ff-heading;
}

.wrap-login {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  box-shadow: 0 0.3rem 1.2rem 0.3rem rgba(162, 195, 229, 0.3);
  border-radius: 4px;
  background: $input-bg;
  padding: 95px 130px 95px 130px;
  width: 500px;
  overflow: hidden;

  cgdis-portal-form {
    width: 100%;
  }
}

.login-pic {
  display: block;
  margin: auto;
  width: 316px;
  height: 180px;
  img {
    max-width: 100%;
  }
}

.login-form {
  width: 290px;
}

.wrap-input {
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
  width: 100%;

  input {
    outline: none;
    border: none;
  }

  input:focus::-webkit-input-placeholder {
    color: transparent;
  }
  input:focus:-moz-placeholder {
    color: transparent;
  }
  input:focus::-moz-placeholder {
    color: transparent;
  }
  input:focus:-ms-input-placeholder {
    color: transparent;
  }
  input::-webkit-input-placeholder {
    color: #999999;
  }
  input:-moz-placeholder {
    color: #999999;
  }
  input::-moz-placeholder {
    color: #999999;
  }
  input:-ms-input-placeholder {
    color: #999999;
  }

  .input {
    display: block;
    border-radius: 4px;
    background: $c-gray-mid-light;
    padding: 0 30px 0 68px;
    width: 100%;
    height: 50px;
    color: $c-gray-mid-dark;
    font-size: 15px;
    line-height: 1.5;
  }

  .symbol-input {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    align-items: center;

    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
    border-radius: 25px;
    padding-left: 35px;
    width: 100%;
    height: 100%;
    pointer-events: none;
    color: #666666;
    font-size: 15px;
  }

  .input:focus + .focus-input + .symbol-input {
    color: $c-primary;
  }
}

.container-login-form-btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding-top: 20px;
  width: 100%;
  button {
    outline: none !important;
    border: none;
    background: $c-primary;
    &:hover {
      cursor: pointer;
    }
  }
}

.login-form-btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;

  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  transition: all 0.4s;
  border-radius: 4px;
  background: $c-primary;
  padding: 0 25px;

  width: 16em;
  height: 50px;
  color: $input-bg;
  font-size: 15px;
  line-height: 1.5;
  text-transform: uppercase;

  &:hover {
    background: #333333;
    color: white;
    text-decoration: none;
  }
}

.icon-logo-login {
  margin-bottom: 25px;
  width: 100%;
  height: 5rem;
  color: #ff0000 !important;

  svg {
    display: block;
    margin: 0 auto;
    width: auto !important; /* overrides inline */
    height: auto !important; /* overrides inline */
    max-height: 5rem;
  }
}

@media (max-width: 992px) {
  .wrap-login {
    padding: 95px 90px 33px 85px;
  }
  .login-pic {
    width: 35%;
  }
  .login-form {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .wrap-login {
    padding: 100px 80px 33px 80px;
  }
  .login-pic {
    display: none;
  }
  .login-form {
    width: 100%;
  }
}
@media (max-width: 576px) {
  .wrap-login {
    padding: 100px 15px 33px 15px;
  }
}

.language-align-right {
  position: absolute;
  top: 3px;
  right: 0;
}

.token-login-container {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  p {
    width: 100%;
    text-align: center;
  }
  button {
    background-color: #2c3e7b;
    height: 5rem;
    color: #ffffff;
  }
}
