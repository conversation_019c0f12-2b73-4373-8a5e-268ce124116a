.news-list {
  @extend %unstyled-list;
  @include make-row;
  margin-right: 0;
  margin-left: 0;
  min-height: 200px;

  li:not(.news-list__empty) {
    @include make-col-ready();
    @include media-breakpoint-up(sm) {
      @include make-col(6);
    }
  }

  .news {
    @extend .clearfix;
    margin-right: 15px;
    margin-left: 15px;
    border-bottom: 0.1rem solid $c-gray-lightest;
    padding: 2rem 0;

    &:hover {
      cursor: pointer;
    }

    &__date {
      display: block;
      color: rgba($c-gray, 0.5);
      font-size: 1.6rem;
      font-family: $ff-heading;
    }

    &__summary {
      color: rgba($c-txt, 0.8);
      font-weight: 300;
      font-size: 1.4rem;
      font-family: $ff-base;
    }

    figure {
      margin: 0 0;
    }
  }

  &__pagination {
    display: block;
    margin: 0 auto;
    padding: 1.5em;
    font-size: 1.2rem;
    text-align: center;
    @extend .clearfix;
    @extend %unstyled-list;

    button {
      border: none;
      background: none;
      &:disabled {
        a {
          cursor: none;
          border-color: $c-gray-light;
          pointer-events: none;
          color: $c-gray-light;
        }
      }
    }

    .page-item {
      display: inline-block;
      padding: 0.2rem;

      a {
        display: inline-block;
        transition:
          color 250ms ease-in-out,
          background-color 250ms ease-in-out;
        border: 0.1rem solid $c-primary-red;
        border-radius: 0.2rem;
        background-color: #fff;
        width: 4rem;
        height: 4rem;
        overflow: hidden;
        color: $c-primary-red;
        line-height: 4rem;
        text-align: center;
        white-space: nowrap;
        &:hover {
          color: $c-primary-red;
        }
      }

      &.active {
        a {
          background-color: $c-primary-red;
          color: #fff;
        }
      }
    }
  }

  &__empty {
    display: flex;
    width: 100%;
    text-align: center;
  }
}

.ellipsis {
  position: relative;
  margin-bottom: 1em;
  max-width: 500px;
  max-height: 110px;
  overflow: hidden;
  text-align: justify;
  &::after {
    display: block;
    position: relative;
    float: right;
    z-index: 3;
    margin-top: -16px;
    background: #fff;
    width: 3em;
    height: 22px;
    content: '';
  }
  &::before {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 2;
    margin-left: -2em;
    background-size: 100% 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0),
      white 50%,
      white
    );
    padding-right: 5px;
    width: 2em;
    height: 22px;
    content: '\02026';
    text-align: right;
  }
}

.ellipsis-dashboard {
  position: relative;
  margin-bottom: 1em;
  max-width: 500px;
  max-height: 8.5rem;
  overflow: hidden;
  text-align: justify;
  &::after {
    display: block;
    position: relative;
    float: right;
    z-index: 3;
    margin-top: 0;
    background: $c-primary;
    width: 3em;
    height: 22px;
    content: '';
  }
  &::before {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 2;
    margin-left: -2em;
    background-size: 100% 100%;
    background: linear-gradient(
      to right,
      rgba(44, 62, 123, 0),
      $c-primary 50%,
      $c-primary
    );
    padding-right: 5px;
    width: 2em;
    height: 22px;
    content: '\02026';
    color: rgba(161, 172, 188, 0.8);
    text-align: right;
  }
}
