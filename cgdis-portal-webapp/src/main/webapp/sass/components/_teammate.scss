.teammate {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: left;
  align-items: center;
  border: 0.1rem solid #e4edf7;
  border-right: 0;
  padding: 1.6rem;
  height: 7rem;
  max-height: 7rem;
  overflow: hidden;
  color: $c-secondary-lighter;
  font-size: 1.8rem;
  line-height: 1.2;
  font-family: $ff-base;

  & + & {
    border-top: 0;
  }

  &__list {
    @extend %unstyled-list;
    flex: 1;
  }

  &__team {
    display: inline-block;
    flex: 2rem 0 0;
    width: 2rem;
    text-align: center;
  }

  &__firstname {
    flex: auto 0 1;
  }

  &__lastname {
    flex: auto 0 1;
    margin-left: 0.2em;
  }

  &__matricule {
    display: block;
    flex: 100% 1 0;
    margin-left: 2rem;
    color: $c-gray-light;
  }

  @include media-breakpoint-up(lg) {
    padding: 0.5rem;
    height: 5rem;
    max-height: 5rem;
    font-size: 1.4rem;

    &__list {
      flex: 1 0 16.5rem;
      width: 16.5rem;
      max-width: 16.5rem;
    }
  }
}
