.dropdown {
  position: relative;
  //margin-right: 1rem;

  &-item {
    font-size: $fs_base;
  }

  &__trigger {
    @extend %unstyled-button;
    font-weight: 300;
    font-size: 1.8rem;
    font-family: $ff-base;

    svg {
      fill: $c-primary-red;
      transition: transform ease-in 250ms;
      width: 1.3rem;
      height: 1.3rem;
    }

    &[aria-expanded='true'] {
      svg {
        transform: rotate(0deg);
      }
    }
  }

  &__menu {
    @extend .-shadow;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 19;
    background-color: #fff;
    padding: 1.5rem;

    &[aria-hidden='true'] {
      display: none;
    }
  }

  &-list {
    a {
      font-size: 20px;
    }
  }
}

.expand-positions {
  position: absolute;
  top: 0.5rem;
  right: 1.5rem;

  @extend %unstyled-button;
  font-weight: 300;
  font-size: 1.8rem;
  font-family: $ff-base;

  svg {
    fill: white;
    transition: transform ease-in 250ms;
    width: 1.3rem;
    height: 1.3rem;
  }

  &[aria-expanded='true'] {
    svg {
      transform: rotate(0deg);
    }
  }
}
