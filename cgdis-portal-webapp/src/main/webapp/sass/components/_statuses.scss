.status {
  &.-complete {
    color: rgba($c-tertiary, 0.8);

    &.fill {
      fill: rgba($c-tertiary, 0.8);
      background-color: rgba($c-tertiary, 0.8);
      color: #000000;
    }
  }

  &.-degraded {
    color: rgba($c-quaternary, 0.8);

    &.fill {
      fill: rgba($c-quaternary, 0.8);
      background-color: rgba($c-quaternary, 0.8);
      color: #ffffff;
    }
  }

  &.-incomplete {
    color: rgba($c-primary-lighter, 0.8);

    &.fill {
      fill: rgba($c-primary-lighter, 0.8);
      background-color: rgba($c-primary-lighter, 0.8);
      color: #ffffff;
    }
  }

  &.-empty {
    color: rgba($c-professional, 0.8);

    &.fill {
      fill: rgba($c-professional, 0.8);
      background-color: rgba($c-professional, 0.8);
      color: #ffffff;
    }
  }

  &.-ideal {
    color: rgba(0, 119, 0, 0.3);
  }

  &.-optimal {
    color: rgba(255, 115, 0, 0.3);
  }

  &.-critical {
    color: rgba(255, 115, 0, 0.3);
  }

  &.-unacceptable {
    color: rgba(255, 0, 0, 0.3);
  }

  &.-planning {
    color: #79ca59;
  }

  &.-availability {
    color: #b1d1f4;
  }

  &.-professional {
    color: $c-professional;
  }

  &.-partial {
    color: #009fe3;

    &.fill {
      fill: #009fe3;
      background-color: #009fe3;
      color: #ffffff;
    }
  }

  &.-holiday {
    color: $c-quaternary;
  }

  &.-sick {
    color: $c-primary-lighter;
  }
}
