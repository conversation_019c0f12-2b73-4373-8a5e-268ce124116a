// Datepicker .less buildfile.  Includes select mixins/variables from bootstrap
// and imports the included datepicker.less to output a minimal datepicker.css
//
// Usage:
//     lessc build3.less datepicker.css
//
// Variables and mixins copied from Bootstrap 3.3.5

@use 'sass:math';

// Variables
$gray: lighten(#000, 33.5%); // #555
$gray-light: lighten(#000, 46.7%); // #777
$gray-lighter: lighten(#000, 93.5%); // #eee

$brand-primary: $c-primary; //darken(#428bca, 6.5%); // #337ab7

$btn-primary-color: #fff;
$btn-primary-bg: $brand-primary;
$btn-primary-border: darken($btn-primary-bg, 5%);

$btn-link-disabled-color: $gray-light;

$state-info-bg: #d9edf7;

$line-height-base: 1.428571429; // 20/14
$border-radius-base: 4px;

$dropdown-bg: #fff;
$dropdown-border: rgba(0, 0, 0, 0.15);

// Mixins

// Button variants
@mixin button-variant($color, $background, $border) {
  border-color: $border;
  background-color: $background;
  color: $color;

  &:focus,
  &.focus {
    border-color: darken($border, 25%);
    background-color: darken($background, 10%);
    color: $color;
  }
  &:hover {
    border-color: darken($border, 12%);
    background-color: darken($background, 10%);
    color: $color;
  }
  &:active,
  &.active {
    border-color: darken($border, 12%);
    background-color: $background;
    color: $color;

    &:hover,
    &:focus,
    &.focus {
      border-color: darken($border, 25%);
      background-color: darken($background, 17%);
      color: $color;
    }
  }
  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    &:hover,
    &:focus,
    &.focus {
      border-color: $border;
      background-color: $background;
    }
  }
}

.datepicker {
  z-index: 1000 !important;
  margin-bottom: math.div($grid-gutter-width, 2);
  border-radius: $border-radius-base;
  direction: ltr;

  &.datepicker-inline {
    @extend .-shadow;

    .datepicker-switch {
      height: 10rem;
      color: #fff;
      font-size: 3rem;
      line-height: 1.6;
      text-transform: capitalize;

      @media (max-width: 400px) {
        height: 5rem;
      }
    }

    table {
      thead {
        tr {
          td,
          th {
            background-clip: border-box;
            background-color: $c-secondary;
          }

          th.dow {
            background-clip: border-box;
            background-color: #fff;
            padding-top: 2rem;
            font-weight: normal;
            text-transform: uppercase;
          }
        }
      }
    }

    .datepicker-switch,
    .prev,
    .next,
    tfoot tr th {
      cursor: pointer;
      &:hover {
        box-shadow: none;
        background-color: rgba($c-secondary, 0.8);
      }
    }

    .prev,
    .next {
      color: #fff;
    }
  }

  &-rtl {
    direction: rtl;
    &.dropdown-menu {
      left: auto;
    }
    table tr td span {
      float: right;
    }
  }

  &-dropdown {
    @extend .-shadow;
    position: absolute;
    top: 0;
    left: 0;
    border: $dropdown-border;
    background-color: $dropdown-bg;
    padding: 4px;
    width: 30rem;

    &:before {
      display: inline-block;
      position: absolute;
      border-top: 0;
      border-right: 7px solid transparent;
      border-bottom: 7px solid $dropdown-border;
      border-left: 7px solid transparent;
      border-bottom-color: rgba(0, 0, 0, 0.2);
      content: '';
    }
    &:after {
      display: inline-block;
      position: absolute;
      border-top: 0;
      border-right: 6px solid transparent;
      border-bottom: 6px solid $dropdown-bg;
      border-left: 6px solid transparent;
      content: '';
    }
    &.datepicker-orient-left:before {
      left: 6px;
    }
    &.datepicker-orient-left:after {
      left: 7px;
    }
    &.datepicker-orient-right:before {
      right: 6px;
    }
    &.datepicker-orient-right:after {
      right: 7px;
    }
    &.datepicker-orient-bottom:before {
      top: -7px;
    }
    &.datepicker-orient-bottom:after {
      top: -6px;
    }
    &.datepicker-orient-top:before {
      bottom: -7px;
      border-top: 7px solid $dropdown-border;
      border-bottom: 0;
    }
    &.datepicker-orient-top:after {
      bottom: -6px;
      border-top: 6px solid $dropdown-bg;
      border-bottom: 0;
    }
  }

  table {
    margin: 0;
    -webkit-touch-callout: none;
    width: 100%;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    tr {
      td,
      th {
        vertical-align: middle;
        border: 0.25rem solid transparent !important;
        border-radius: 0;
        background-clip: padding-box;
        width: 14.28%;
        height: 3rem;
        font-size: 1rem;
        line-height: 1.8rem;
        text-align: center;
      }
    }
  }
  // Inline display inside a table presents some problems with
  // border and background colors.
  .table-striped & table tr {
    td,
    th {
      background-color: transparent;
    }
  }
  table tr td {
    &.old,
    &.new {
      //color: $btn-link-disabled-color;
      color: #ddd;
    }
    &.active.day {
      // default: no style
    }
    &.day {
      border-radius: 0.45rem;

      &.-now {
        &:not(.-professional) {
          color: black !important;
        }
      }
      &:not(.old):not(.new) {
        &.-complete {
          background-color: rgba($c-tertiary, 0.8) !important;
          color: #fff;
        }
        &.-degraded {
          background-color: rgba($c-quaternary, 0.8) !important;
          color: #fff;
        }
        &.-partial {
          background-color: #009fe3 !important;
          color: #fff;
        }
        &.-incomplete {
          background-color: rgba($c-primary-lighter, 0.8) !important;
          color: #fff;
        }
        &.-empty {
          background-color: rgba($c-professional, 0.8) !important;
          color: #fff;
        }
        &.-planning {
          background-color: #79ca59 !important;
          color: #fff;
        }
        &.-availability {
          background-color: #b1d1f4 !important;
          color: #fff;
        }
        &.-professional {
          background-color: $c-professional !important;
          color: #fff;
        }
        &.-public-holidays {
          background-color: #fff9e7 !important;
          color: black !important;
        }
        &.-noData {
          background-color: $c-gray-mid-dark !important;
          color: black !important;
        }
      }
    }
    &.disabled,
    &.disabled:hover {
      cursor: default;
      background-color: #dddddd;
      color: $btn-link-disabled-color;
    }
    &.highlighted {
      $highlighted-bg: $state-info-bg;
      @include button-variant(
        #000,
        $highlighted-bg,
        darken($highlighted-bg, 20%)
      );
      border-radius: 0;

      &.focused {
        background: darken($highlighted-bg, 10%);
      }

      &.disabled,
      &.disabled:active {
        background: $highlighted-bg;
        color: $btn-link-disabled-color;
      }
    }
    &.today {
      $today-bg: lighten(orange, 30%);
      @include button-variant(#000, $today-bg, darken($today-bg, 20%));

      &.focused {
        background: darken($today-bg, 10%);
      }

      &.disabled,
      &.disabled:active {
        background: $today-bg;
        color: $btn-link-disabled-color;
      }
    }
    &.range {
      $range-bg: $gray-lighter;
      @include button-variant(#000, $range-bg, darken($range-bg, 20%));
      border-radius: 0;

      &.focused {
        background: darken($range-bg, 10%);
      }

      &.disabled,
      &.disabled:active {
        background: $range-bg;
        color: $btn-link-disabled-color;
      }
    }
    &.range.highlighted {
      $range-highlighted-bg: mix($state-info-bg, $gray-lighter, 50%);
      @include button-variant(
        #000,
        $range-highlighted-bg,
        darken($range-highlighted-bg, 20%)
      );

      &.focused {
        background: darken($range-highlighted-bg, 10%);
      }

      &.disabled,
      &.disabled:active {
        background: $range-highlighted-bg;
        color: $btn-link-disabled-color;
      }
    }
    &.range.today {
      $range-today-bg: mix(orange, $gray-lighter, 50%);
      @include button-variant(
        #000,
        $range-today-bg,
        darken($range-today-bg, 20%)
      );

      &.disabled,
      &.disabled:active {
        background: $range-today-bg;
        color: $btn-link-disabled-color;
      }
    }
    &.selected,
    &.selected.highlighted {
      @include button-variant(#fff, $gray-light, $gray);
      text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    }
    &.active,
    &.active.highlighted {
      @include button-variant(
        $btn-primary-color,
        $btn-primary-bg,
        $btn-primary-border
      );
    }
    span {
      display: block;
      float: left;
      cursor: pointer;
      margin: 1%;
      border-radius: 4px;
      width: 23%;
      height: 54px;
      line-height: 54px;
      &:hover,
      &.focused {
        background: $gray-lighter;
      }
      &.disabled,
      &.disabled:hover {
        cursor: default;
        background: none;
        color: $btn-link-disabled-color;
      }
      &.active,
      &.active:hover,
      &.active.disabled,
      &.active.disabled:hover {
        @include button-variant(
          $btn-primary-color,
          $btn-primary-bg,
          $btn-primary-border
        );
      }
      &.old,
      &.new {
        color: $btn-link-disabled-color;
      }
    }
  }

  .datepicker-switch {
    width: 50%;
    height: 5rem;
    font-weight: normal;
    font-size: 1.8rem;
    font-family: $ff-heading;
  }

  .prev,
  .next {
    color: #fff;
  }
  .prev {
    border-top-left-radius: 0.4rem;
  }
  .next {
    border-top-right-radius: 0.4rem;
  }

  .datepicker-switch {
    cursor: pointer;
    &:hover {
      background: $gray-lighter;
    }
  }

  tfoot tr th,
  &.datepicker-inline tfoot tr th {
    cursor: pointer;
    &:hover {
      border: 1px solid $c-primary-red;
      border-radius: $border-radius-base;
      background: white;
      color: $c-primary-red;
    }
  }

  .prev,
  .next {
    cursor: pointer;
    color: $c-primary;

    &.disabled {
      visibility: hidden;
    }
  }

  // Basic styling for calendar-week cells
  .cw {
    vertical-align: middle;
    padding: 0 2px 0 5px;
    width: 12px;
    font-size: 10px;
  }
}
.input-group.date .input-group-addon {
  cursor: pointer;
}
.input-daterange {
  width: 100%;
  input {
    text-align: center;
  }
  input:first-child {
    border-radius: 3px 0 0 3px;
  }
  input:last-child {
    border-radius: 0 3px 3px 0;
  }
  .input-group-addon {
    margin-right: -5px;
    margin-left: -5px;
    border-width: 1px 0;
    padding: 4px 5px;
    width: auto;
    min-width: 16px;
    line-height: $line-height-base;
  }
}

.datepicker-input {
}

// apply over only on desktop
@media (hover: hover) {
  .datepicker {
    .day:hover {
      cursor: pointer;
      border-color: transparent !important;
      background-color: white !important;
      color: $c-primary-red !important;
    }
  }
}
