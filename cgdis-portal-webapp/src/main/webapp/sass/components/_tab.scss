cgdis-portal-service-plan-member {
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  cgdis-portal-service-plan-add-people-selector {
    mat-tab-header.mat-mdc-tab-header {
      background: white;
    }
  }
  mat-tab-header.mat-mdc-tab-header {
    border: none;
    background: $c-secondary;
    padding-right: 3em;
    padding-left: 3em;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mat-mdc-tab {
    opacity: 1 !important;
    color: $c-gray;

    letter-spacing: normal;
    &,
    &:not(.mdc-tab--stacked) {
      height: 3.5rem;
    }
  }
  .mat-mdc-tab .mdc-tab__text-label {
    color: $c-gray;
  }
  .mat-mdc-tab:hover .mdc-tab__text-label {
    color: $c-gray;
  }

  .mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label,
  .mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label {
    color: inherit;
  }

  .mdc-tab-indicator__content--underline {
    border: 0px;
  }

  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mdc-tab--active,
  .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    border-top-right-radius: 0.2rem;
    border-top-left-radius: 0.2rem;
    background: white;
    color: $c-secondary;
  }

  .mat-ripple-element {
    display: none;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mat-tab-body-wrapper {
    display: block;
  }

  .tabs [role='tabpanel'] {
    position: absolute;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mat-mdc-tab-group.mat-primary .mat-mdc-tab:not(.mat-mdc-tab-disabled):focus {
    background-color: white !important;
  }

  mat-ink-bar {
    display: none;
  }

  owl-carousel {
    owl-carousel-child {
      padding: 1rem 4rem !important;
      text-align: center !important;
    }
  }
}

span.accordion__title {
  font-weight: 500;
}

cgdis-portal-service-plan-copy-popup {
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  mat-tab-header.mat-mdc-tab-header {
    border: none;
    background: $c-secondary;
    padding-right: 3em;
    padding-left: 3em;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mat-mdc-tab {
    opacity: 1 !important;
    color: $c-gray;
    &,
    &:not(.mdc-tab--stacked) {
      height: 3.5rem;
    }
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mdc-tab--active,
  .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    border-top-right-radius: 0.2rem;
    border-top-left-radius: 0.2rem;
    background: white;
    color: $c-secondary;
  }

  .mat-ripple-element {
    display: none;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mat-tab-body-wrapper {
    display: block;
  }

  .tabs [role='tabpanel'] {
    position: absolute;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  .mat-mdc-tab-group.mat-primary .mat-mdc-tab:not(.mat-mdc-tab-disabled):focus {
    background-color: white !important;
  }

  mat-ink-bar {
    display: none;
  }

  .themed-popup__content {
    padding: 0;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version. */
  mat-tab-body {
    padding: 2em;
  }
}
