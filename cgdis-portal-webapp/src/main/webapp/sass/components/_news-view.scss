.news-view {
  color: rgba($c-txt, 0.8);
  font-weight: 300;
  font-size: 1.4rem;
  line-height: 1.6;
  //padding: $grid-gutter-width * 1;
  font-family: $ff-base;
  text-align: justify;

  tr:nth-child(odd) {
    background-color: rgba(0, 0, 0, 0.03);
  }
  table {
    box-shadow: 0 0.3rem 1.2rem 0.3rem rgba(162, 195, 229, 0.3);
    border: none;
    min-width: 100%;
  }
  td {
    border: none;
    padding: 0.5em 1em;
    min-width: 50px;
    max-width: 100%;
  }
  .media-element {
    height: auto;
  }
  .element-invisible {
    display: none;
  }
  .file-icon {
    display: none;
  }
  a {
    color: #2c3e7b;
  }
  thead {
    box-shadow: 0 0rem 0rem 0.1rem #2c3e7b;
    border: none;
    border-top-right-radius: 0.4rem;
    border-top-left-radius: 0.4rem;
    background-color: #2c3e7b;
    color: #fff;
    font-weight: normal;
    font-size: 1.3rem;
    font-family: 'Archivo Narrow', Georgia, 'Times New Roman', Times, serif;
    text-transform: uppercase;
    white-space: nowrap;
  }
  th {
    border: none;
    padding: 0.5em 1em;
    min-width: 50px;
    max-width: 100%;
  }

  &__thumb {
    margin: 0;
    padding: 2rem;

    figcaption {
      margin: 2rem auto;

      strong {
        font-weight: normal;
      }
    }
  }

  &__date {
    display: block;
    color: rgba($c-gray, 0.5);
    font-size: 1.6rem;
    font-family: $ff-heading;
  }

  &__content-mobile {
    padding-right: 2em;
    padding-left: 2em;
  }

  &__content {
    padding-top: 2em;
  }

  &__title {
    color: $c-secondary-lighter;
    font-size: 2.8rem;
    font-family: $ff-heading;
  }

  &__summary {
    margin: $grid-gutter-width * 1 auto;
  }

  &__text {
    padding-right: 3rem;

    ul {
      padding-left: 0;
      list-style: none;

      li {
        &::before {
          display: inline-block;
          vertical-align: middle;
          margin-right: 0.5em;
          content: '•';
          color: $c-primary-red;
          font-size: 2em;
          line-height: 0.5;
        }
      }
    }

    ol {
      counter-reset: li;
      padding-left: 0;
      list-style: none;

      li {
        counter-increment: li;

        &::before {
          display: inline-block;
          margin-right: 1em;
          content: counter(li) '.';
          color: $c-primary-red;
        }
      }
    }

    a {
      color: #2c3e7b;
    }
  }

  &__img {
    float: left;
    margin: $grid-gutter-width * 1 $grid-gutter-width * 1 $grid-gutter-width * 1
      0;
  }

  figure {
    img {
      box-shadow: 0 0.3rem 1.5rem 0.3rem rgba(162, 195, 229, 0.3);
      border-radius: 0.2rem;
    }
  }
}
