/* Magnific Popup CSS */

@import 'settings';

////////////////////////
//
// Contents:
//
// 1. General styles
//    - Transluscent overlay
//    - Containers, wrappers
//    - Cursors
//    - Helper classes
// 2. Appearance
//    - Preloader & text that displays error messages
//    - CSS reset for buttons
//    - Close icon
//    - "1 of X" counter
//    - Navigation (left/right) arrows
//    - Iframe content type styles
//    - Image content type styles
//    - Media query where size of arrows is reduced
//    - IE7 support
//
////////////////////////

////////////////////////
// 1. General styles
////////////////////////

// Transluscent overlay
.mfp-bg {
  position: fixed;
  top: 0;
  left: 0;
  opacity: $mfp-overlay-opacity;
  z-index: $mfp-z-index-base + 2;

  background: $mfp-overlay-color;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// Wrapper for popup
.mfp-wrap {
  position: fixed;
  top: 0;
  left: 0;
  z-index: $mfp-z-index-base + 3;
  -webkit-backface-visibility: hidden; // fixes webkit bug that can cause "false" scrollbar
  outline: none !important;
  width: 100%;
  height: 100%;
}

// Root container
.mfp-container {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  padding: 0 $mfp-popup-padding-left;
  width: 100%;
  height: 100%;
  text-align: center;
}

// Vertical centerer helper
.mfp-container {
  &:before {
    display: inline-block;
    vertical-align: middle;
    height: 100%;
    content: '';
  }
}

// Remove vertical centering when popup has class `mfp-align-top`
.mfp-align-top {
  .mfp-container {
    &:before {
      display: none;
    }
  }
}

// Popup content holder
.mfp-content {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  z-index: $mfp-z-index-base + 5;
  margin: 0 auto;
  text-align: left;
}
.mfp-inline-holder,
.mfp-ajax-holder {
  .mfp-content {
    cursor: auto;
    width: 100%;
  }
}

// Cursors
.mfp-ajax-cur {
  cursor: progress;
}
.mfp-zoom-out-cur {
  &,
  .mfp-image-holder .mfp-close {
    cursor: -moz-zoom-out;
    cursor: -webkit-zoom-out;
    cursor: zoom-out;
  }
}
.mfp-zoom {
  cursor: pointer;
  cursor: -webkit-zoom-in;
  cursor: -moz-zoom-in;
  cursor: zoom-in;
}
.mfp-auto-cursor {
  .mfp-content {
    cursor: auto;
  }
}

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

// Hide the image during the loading
.mfp-loading {
  &.mfp-figure {
    display: none;
  }
}

// Helper class that hides stuff
@if $mfp-use-visuallyhidden {
  // From HTML5 Boilerplate https://github.com/h5bp/html5-boilerplate/blob/v4.2.0/doc/css.md#visuallyhidden
  .mfp-hide {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    position: absolute !important;
    margin: -1px !important;
    padding: 0 !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
  }
} @else {
  .mfp-hide {
    display: none !important;
  }
}

////////////////////////
// 2. Appearance
////////////////////////

// Preloader and text that displays error messages
.mfp-preloader {
  position: absolute;
  top: 50%;
  right: 8px;
  left: 8px;
  z-index: $mfp-z-index-base + 4;
  margin-top: -0.8em;
  width: auto;
  color: $mfp-controls-text-color;
  text-align: center;
  a {
    color: $mfp-controls-text-color;
    &:hover {
      color: $mfp-controls-text-color-hover;
    }
  }
}

// Hide preloader when content successfully loaded
.mfp-s-ready {
  .mfp-preloader {
    display: none;
  }
}

// Hide content when it was not loaded
.mfp-s-error {
  .mfp-content {
    display: none;
  }
}

// CSS-reset for buttons
button {
  &.mfp-close,
  &.mfp-arrow {
    display: block;
    -webkit-appearance: none;
    z-index: $mfp-z-index-base + 6;
    cursor: pointer;
    outline: none;
    box-shadow: none;
    border: 0;
    background: transparent;
    padding: 0;
    overflow: visible;
    touch-action: manipulation;
  }
  &::-moz-focus-inner {
    border: 0;
    padding: 0;
  }
}

// Close icon
.mfp-close {
  position: absolute;
  top: 0;
  right: 0;
  opacity: $mfp-controls-opacity;
  padding: 0 0 18px 10px;
  width: 44px;
  height: 44px;
  color: $mfp-controls-color;

  font-style: normal;
  font-size: 28px;
  line-height: 44px;
  font-family: Arial, Baskerville, monospace;
  text-align: center;
  text-decoration: none;

  &:hover,
  &:focus {
    opacity: 1;
  }

  &:active {
    top: 1px;
  }
}
.mfp-close-btn-in {
  .mfp-close {
    color: $mfp-inner-close-icon-color;
  }
}
.mfp-image-holder,
.mfp-iframe-holder {
  .mfp-close {
    right: -6px;
    padding-right: 6px;
    width: 100%;
    color: $mfp-controls-color;
    text-align: right;
  }
}

// "1 of X" counter
.mfp-counter {
  position: absolute;
  top: 0;
  right: 0;
  color: $mfp-controls-text-color;
  font-size: 12px;
  line-height: 18px;
  white-space: nowrap;
}

// Navigation arrows
@if $mfp-include-arrows {
  .mfp-arrow {
    position: absolute;
    top: 50%;
    opacity: $mfp-controls-opacity;
    margin: 0;
    margin-top: -55px;
    padding: 0;
    width: 90px;
    height: 110px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    &:active {
      margin-top: -54px;
    }
    &:hover,
    &:focus {
      opacity: 1;
    }
    &:before,
    &:after {
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      margin-top: 35px;
      margin-left: 35px;
      border: medium inset transparent;
      width: 0;
      height: 0;
      content: '';
    }

    &:after {
      top: 8px;

      border-top-width: 13px;
      border-bottom-width: 13px;
    }

    &:before {
      opacity: 0.7;
      border-top-width: 21px;
      border-bottom-width: 21px;
    }
  }

  .mfp-arrow-left {
    left: 0;
    &:after {
      margin-left: 31px;
      border-right: 17px solid $mfp-controls-color;
    }
    &:before {
      margin-left: 25px;
      border-right: 27px solid $mfp-controls-border-color;
    }
  }

  .mfp-arrow-right {
    right: 0;
    &:after {
      margin-left: 39px;
      border-left: 17px solid $mfp-controls-color;
    }
    &:before {
      border-left: 27px solid $mfp-controls-border-color;
    }
  }
}

// Iframe content type
@if $mfp-include-iframe-type {
  .mfp-iframe-holder {
    padding-top: $mfp-iframe-padding-top;
    padding-bottom: $mfp-iframe-padding-top;
    .mfp-content {
      width: 100%;
      max-width: $mfp-iframe-max-width;
      line-height: 0;
    }
    .mfp-close {
      top: -40px;
    }
  }
  .mfp-iframe-scaler {
    padding-top: $mfp-iframe-ratio * 100%;
    width: 100%;
    height: 0;
    overflow: hidden;
    iframe {
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      box-shadow: $mfp-shadow;
      background: $mfp-iframe-background;
      width: 100%;
      height: 100%;
    }
  }
}

// Image content type
@if $mfp-include-image-type {
  /* Main image in popup */
  img {
    &.mfp-img {
      display: block;
      box-sizing: border-box;
      margin: 0 auto;
      padding: $mfp-image-padding-top 0 $mfp-image-padding-bottom;
      width: auto;
      max-width: 100%;
      height: auto;
      line-height: 0;
    }
  }

  /* The shadow behind the image */
  .mfp-figure {
    line-height: 0;
    &:after {
      display: block;
      position: absolute;
      top: $mfp-image-padding-top;
      right: 0;
      bottom: $mfp-image-padding-bottom;
      left: 0;
      z-index: -1;
      box-shadow: $mfp-shadow;
      background: $mfp-image-background;
      width: auto;
      height: auto;
      content: '';
    }
    small {
      display: block;
      color: $mfp-caption-subtitle-color;
      font-size: 12px;
      line-height: 14px;
    }
    figure {
      margin: 0;
    }
  }
  .mfp-bottom-bar {
    position: absolute;
    top: 100%;
    left: 0;
    cursor: auto;
    margin-top: -$mfp-image-padding-bottom + 4;
    width: 100%;
  }
  .mfp-title {
    color: $mfp-caption-title-color;
    line-height: 18px;
    text-align: left;
    word-wrap: break-word;
    padding-right: 36px; // leave some space for counter at right side
  }

  .mfp-image-holder {
    .mfp-content {
      max-width: 100%;
    }
  }

  .mfp-gallery {
    .mfp-image-holder {
      .mfp-figure {
        cursor: pointer;
      }
    }
  }

  @if $mfp-include-mobile-layout-for-image {
    @media screen and (max-width: 768px) and (orientation: landscape),
      screen and (max-height: 300px) {
      /**
       * Remove all paddings around the image on small screen
       */
      .mfp-img-mobile {
        .mfp-image-holder {
          padding-right: 0;
          padding-left: 0;
        }
        img {
          &.mfp-img {
            padding: 0;
          }
        }
        .mfp-figure {
          // The shadow behind the image
          &:after {
            top: 0;
            bottom: 0;
          }
          small {
            display: inline;
            margin-left: 5px;
          }
        }
        .mfp-bottom-bar {
          position: fixed;
          top: auto;
          bottom: 0;
          box-sizing: border-box;
          margin: 0;
          background: rgba(0, 0, 0, 0.6);
          padding: 3px 5px;
          &:empty {
            padding: 0;
          }
        }
        .mfp-counter {
          top: 3px;
          right: 5px;
        }
        .mfp-close {
          position: fixed;
          top: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.6);
          padding: 0;
          width: 35px;
          height: 35px;
          line-height: 35px;
          text-align: center;
        }
      }
    }
  }
}

// Scale navigation arrows and reduce padding from sides
@media all and (max-width: 900px) {
  .mfp-arrow {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
  }
  .mfp-arrow-left {
    -webkit-transform-origin: 0;
    transform-origin: 0;
  }
  .mfp-arrow-right {
    -webkit-transform-origin: 100%;
    transform-origin: 100%;
  }
  .mfp-container {
    padding-right: $mfp-popup-padding-left-mobile;
    padding-left: $mfp-popup-padding-left-mobile;
  }
}

.themed-popup {
  position: relative;
  margin: 0 auto;
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.16),
    0 3px 6px rgba(0, 0, 0, 0.23);
  background-color: $c-secondary;
  max-width: #{map_get($container-max-widths, sm)};
  min-height: 4.4rem; // close button

  .mfp-close-btn-in & .mfp-close {
    color: #fff;
  }

  &__header {
    position: relative;
    background-color: $c-secondary;
    padding: 1em 2rem 1rem 2.5em;
    color: #fff;
  }

  &__title {
    color: inherit;
    font-size: 2.3rem;
    font-family: $ff-base;
  }

  &__content {
    position: relative;
    background-color: #fff;
    padding: 4rem;
  }
}
