.localSpinner {
  position: relative;
}

.entity-list {
  @media (max-width: 768px) {
    margin-top: 5rem;
  }
}

cgdis-portal-spinner {
  ngx-loading {
    .backdrop {
      background-color: rgba(150, 150, 150, 0.3) !important;
    }

    @media (max-width: 400px) {
      @media (inverted-colors: inverted) {
        .spinner-three-bounce > div {
          background-color: white !important;
        }
      }
    }
  }

  .long-loading-note {
    position: fixed;
    top: 54%;
    left: 50%;
    font-size: 2rem;
    transform: translate(-50%, -50%);
    z-index: 20;
    width: 470px;
    text-align: center;
  }
}
