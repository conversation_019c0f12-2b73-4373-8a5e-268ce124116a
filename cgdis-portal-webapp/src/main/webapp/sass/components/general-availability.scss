.general-availability {
  border-radius: 0.4rem;
  background-color: #fff;
  height: auto;

  font-family: $ff-heading;
  text-transform: uppercase;

  &-header {
    position: relative;
    background-color: $c-primary;
    color: #fff;

    &__title {
      margin-left: 1rem;
      font-weight: bold;
      font-size: 20px;
    }

    &__label {
      position: absolute;
      top: 0;
      right: 0;
      margin-right: 1rem;
      font-weight: bold;
      font-size: 20px;
    }

    &__persons_list {
      display: inline;
      margin-bottom: 0;
      margin-left: 10rem;
    }

    &__person {
      display: inline-block;
      width: 85px !important;
      font-size: 1.3rem;
      text-align: center;

      &__content {
        display: block;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &__number {
          font-size: 1.1rem;
        }
      }
    }
  }

  &-oneday {
    display: block;
    float: left;
    clear: left;
    margin-bottom: 5px;

    &-date {
      color: #2c3e7b;
      font-weight: bold;
      font-size: 17px;

      &.first {
        margin-left: 1rem;
      }
    }
  }

  &-onerow {
    position: relative;
    align-content: center;
    list-style-type: none;

    &__start,
    &__end {
      display: inline;
      float: left;
      color: $c-gray;
      font-family: $ff-base;
    }
    &__end {
      padding-right: 1rem;
    }
    &__start {
      &::after {
        content: '-';
      }
    }
    &-nodata {
      text-transform: none;
    }

    &-cellule {
      display: block;
      vertical-align: top;
      float: left;
      border: 0.1rem solid #e9eef5;
      background-color: white;
      width: 85px;
      height: 3rem !important;
      text-align: left;

      &.PROFESSIONAL {
        border: none;
        border-left: 3px solid $c-professional;
        background-color: rgba($c-professional, 0.05);
        padding: 0.25rem;

        &.barracked {
          background-color: rgba($c-professional, 0.2);
        }
      }

      &.VOLUNTEER {
        border: none;
        border-left: 3px solid #b1d1f4;
        background-color: rgba(#b1d1f4, 0.1);
        padding: 0.25rem;

        &.barracked {
          background-color: rgba(#b1d1f4, 0.3);
        }
      }

      &-icon {
        margin-bottom: 3px;
        margin-left: 6px;
        max-width: 15px !important;
        height: 15px !important;
      }
    }
  }
}

.scrollable-content_availability {
  padding-top: 1px;

  .ng-scrollbar-view {
    /*@media (max-width: 768px) {
      max-height: 100%;
    }*/
    padding-right: 0;
    max-height: calc(16vh);
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.scrollable-content_it {
  padding-top: 1px;

  .ng-scrollbar-view {
    //max-height: calc(12vh);
    /*@media (max-width: 800px) {
      max-height: 100%;
    }*/
    padding-right: 0;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.scrollable-content_ga {
  padding-top: 0px;

  .ng-scrollbar-view {
    padding-right: 0;
    max-height: calc(84vh - 250px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.ga_week-selector {
  position: relative;
  bottom: 5px;
}
