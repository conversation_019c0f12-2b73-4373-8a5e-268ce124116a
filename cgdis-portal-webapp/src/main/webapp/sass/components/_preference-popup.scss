cgdis-portal-preference {
  li {
    margin: 0.2rem;
    border: 1px solid $c-gray-darker;
    width: 3em;
    height: 3em;
    line-height: 3em;
    list-style-type: none;
    text-align: center;

    :hover {
      cursor: pointer;
    }
  }

  li.selected {
    background-color: $c-primary;
    color: white;
    :hover {
      cursor: default;
    }
  }

  ul {
    padding: 0;

    > li {
      display: inline-block;
      zoom: 1;
      *display: inline;
    }
  }

  label {
    font-weight: 400 !important;
  }
}

.preferences-export-persons {
  .dropdown__trigger {
    svg {
      fill: currentColor;
    }
  }
}

.preferences-export-prestations {
  .dropdown__trigger {
    svg {
      fill: currentColor;
    }
  }
}

#preferences-export-prestations-switcher,
#preferences-export-persons-switcher {
  top: 5.5rem;
  right: 6rem;
  width: 8em !important;
  min-width: unset;

  li {
    border: none !important;
    width: 10em !important;
    font-size: 12px !important;
  }
}

#preferences-export-prestations-button,
#preferences-export-download-button {
  .ng-star-inserted.icon-chevron {
    display: none;
  }

  ul {
    top: 3em;
    left: -13rem;
    @include media-breakpoint-between(sm, xl) {
      left: calc(100% - 20em);
    }
    @include media-breakpoint-down(xs) {
      left: -7em;
    }
    width: 20em;
    list-style-type: none;
  }

  li {
    display: list-item !important;
    border: none !important;
    width: 25em !important;
    height: unset !important;
    font-size: 12px !important;
    line-height: unset !important;
    text-align: left !important;
  }

  li:hover {
    cursor: pointer;
    a {
      color: $c-primary-red;
    }
  }
}
