@mixin ngx-select() {
  .ngx-select.dropdown {
    border: none;
  }

  ngx-select {
    border: none;
    background: transparent;

    .ngx-select__clear {
      margin-right: 0 !important;
      margin-left: 0.75em;
    }

    .ngx-select a.ngx-select__clear {
      margin-top: 0;
    }

    .ngx-select__clear-icon {
      width: 0.75em !important;
    }
  }

  .ngx-select__choices {
    width: unset !important;
    min-width: 100% !important;
  }
  span.ngx-select__placeholder {
    flex-grow: 1;
  }
  span.ngx-select__placeholder.text-muted.ng-star-inserted,
  span.ngx-select__selected-single {
    line-height: 2.4em !important;
  }

  .ngx-select__toggle.btn.form-control {
    @media (min-width: 768px) {
      padding: 0;
      //height: 2.4em;
      max-height: 2.4em;
      line-height: 0.25em;
      span {
        line-height: normal;
      }
    }
  }

  input.ngx-select__search.form-control.ng-star-inserted {
    height: 2em;
  }
}
