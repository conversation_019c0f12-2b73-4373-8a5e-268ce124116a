{"admin": {"allowance": {"back": "(de)Back", "configuration": {"actions": "", "barracked_amount": "(de)Barracked amount", "barracked_amount_mobile": "", "configVersion": "(de)Version", "creation": {"error": "(de)Failed to create allowance configuration '{{label}}', Error code : {{code}}", "success": "(de)Allowance configuration successfully created", "title": "(de)Allowance configuration creation"}, "delete": {"error": "(de)Failed to suppress the configuration", "success": "(de)Configuration successfully deleted"}, "endDate": "(de)End date", "label": "(de)Label", "label_mobile": "", "new": {"fields": {"barrackedAmount": "(de)Barracked amount", "barrackedAmount_mobile": "(de)<PERSON>acked", "endDate": "(de)End date", "label": "(de)Label", "notBarrackedAmount": "(de)Not Barracked amount", "notBarrackedAmount_mobile": "(de)Not Barracked", "startDate": "(de)Start date", "version": "(de)Version", "versionName": "(de)Version"}, "title": "(de)New configuration"}, "not_barracked_amount": "(de)Not barracked amount", "not_barracked_amount_mobile": "", "popup": {"delete": {"message": "(de)Do you really want to delete the configuration <b>{{name}}</b>?", "subtitle": "(de)of a configuration", "title": "(de)Deletion"}}, "startDate": "(de)Start date", "title": "(de)Allowance configurations", "update": {"success": "(de)Allowance successfully updated", "title": "(de)Edit configuration", "title_view": "(de)View configuration"}, "version": ""}, "create": "(de)Create", "list": {"allowanceAmount": "(de)Allowance amount", "allowanceAmount_mobile": "", "allowanceDate": "(de)Allowance date", "duration": "(DE) Duration"}, "search": {"detail": {"title": "(de)Search for allowance"}, "title": "(de)Search for allowance"}, "title": "(de)Allowance"}, "boxes": {"form": {"globalId": "", "isMaster": "", "name": ""}, "list": {"header": {"entityName": "", "globalId": "", "name": ""}, "title": "", "titlemobile": ""}, "title": "", "view": {"title": "", "titlemobile": ""}}, "entities": {"form": {"armingPriority": "(de)Armament priority", "armingPriority_mobile": "", "category": "(de)Category", "entity": "(de)Entity", "main": "", "name": "(de)Name", "open": "(de)Open", "openParent": "(de)Open upper level", "parent": "(de)Upper level", "type": "(de)Type", "update": {"success": "(de)Entity updated with success"}}, "list": {"header": {"actions": "(de)Actions", "armingPriority": "(de)Armament priority", "armingPriority_mobile": "", "category": "(de)Category", "mainEntity": "", "name": "(de)Name", "parentEntity": "", "servicePlanType": "(de)Service plan type", "type": "(de)Type"}, "title": "(de)List of entities", "titlemobile": ""}, "title": "(de)Manage entities", "view": {"title": "(de)Entity", "titlemobile": ""}}, "export": {"creation": {"error": "", "success": "", "title": ""}, "delete": {"error": "", "success": ""}, "form": {"add-mail": "", "cron": "", "data-export": "", "email": "", "entity": "", "export-format": {"csv": "", "format": "", "json": ""}, "export-number-days": "", "export-on-server": "", "export-persons": "", "export-service-plans": "", "export-type": "", "export-vehicles": "", "name": "", "remove-mail": ""}, "list": {"header": {"actions": "", "entity": "", "last-execution": "", "last-execution-mobile": "", "name": "", "next-execution": "", "next-execution-mobile": ""}, "title": "", "titlemobile": ""}, "popup": {"delete": {"message": "", "subtitle": "", "title": ""}, "force-execution": {"message": "", "subtitle": "", "title": ""}}, "title": "", "update": {"error": "", "success": "", "title": "", "titlemobile": ""}}, "function_operational": {"close": {"error": ""}, "creation": {"error": "(de)Failed to create operational function '{{label}}', Error code : {{code}}", "success": "(de)Operational function '{{label}}' successfully created", "title": "(de)Operation function creation"}, "delete": {"error": "(de)Failed to delete operational function, Code d'erreur : {{code}}", "success": "(de)Operational function successfully deleted"}, "form": {"id": "(de)Id", "intervention_type": "(de)Intervention type", "label": "(de)Label", "portalLabel": "(de)Portal Label"}, "list": {"header": {"actions": "(de)Actions", "function": "(de)Function", "id": "(de)ID", "intervention_type": "(de)Intervention type", "label": "(de)Label", "portalLabel": "(de)Portal Label"}, "modelName": "(de)Nom du modèle", "showClosedFunctionOperational": "(de)Show closed Operational functions", "title": "(de)Operational functions list", "titlemobile": "(de)Operational functions list", "totalElements": "(de){{totalElements}} results"}, "popup": {"closure": {"message": "", "subtitle": "", "title": ""}, "delete": {"message": "(de)Do you really want to delete the operational function <b>{{label}}</b> ?", "subtitle": "(de)of an operational function", "title": "(de)Deletion"}, "validate": {"message": "", "subtitle": "", "title": ""}}, "update": {"error": "(de)Failed to update operational function '{{label}}', Error code : {{code}}", "success": "(de)Operational function '{{label}}' suyccessfully updated", "title": "(de)Operational function update", "titlemobile": "(de)Operational function detail"}}, "general-message": {"creation": {"error": "", "success": "", "title": "", "titlemobile": ""}, "delete": {"error": "", "success": ""}, "form": {"endDateTime": "", "message": "", "mobileEndDateTime": "", "mobileStartDateTime": "", "name": "", "startDateTime": ""}, "list": {"header": {"endDateTime": "", "message": "", "mobileEndDateTime": "", "mobileStartDateTime": "", "name": "", "startDateTime": ""}, "title": "", "titlemobile": ""}, "title": "", "update": {"error": "", "success": "", "title": "", "titlemobile": ""}}, "position_template": {"closure": {"error": "", "success": ""}, "copy": {"error": "", "new_name": "", "success": "", "title": ""}, "creation": {"error": "", "success": "", "title": ""}, "delete": {"error": "", "success": ""}, "form": {"buttons": {"addversion": ""}, "id": "", "intervention_type": "", "label": "", "portalLabel": ""}, "list": {"header": {"actions": "", "entity": "", "id": "", "intervention_type": "", "label": "", "portalLabel": ""}, "title": "", "titlemobile": ""}, "popup": {"closure": {"message": "", "subtitle": "", "title": ""}, "delete": {"message": "", "subtitle": "", "title": ""}}, "title": "", "update": {"error": "", "success": "", "title": "", "titlemobile": ""}, "version": {"closure": {"error": "", "success": ""}, "copy": {"label": "", "message": "", "target": "", "title": ""}, "create": {"success": "", "title": ""}, "delete": {"success": ""}, "edit": {"information_message": "", "success": "", "title": "", "titlemobile": ""}, "form": {"button": {"addversion": ""}, "enddate": "", "enddate_mobile": "", "function": {"empty": "", "type": {"ideal": "", "partial": ""}}, "functions": {"empty": "", "type": {"ideal": "", "partial": ""}}, "label": "", "startdate": "", "startdate_mobile": "", "version": ""}, "function": {"empty": "", "type": {"ideal": "", "partial": ""}}, "functions": {"empty": "", "type": {"ideal": "", "partial": ""}}, "list": {"header": {"actions": "", "enddate": "", "enddate_mobile": "", "label": "", "startdate": "", "startdate_mobile": ""}}, "popup": {"closure": {"message": "", "subtitle": "", "title": ""}, "delete": {"message": "", "subtitle": "", "title": ""}}}}, "public-holiday": {"creation": {"error": "", "success": "", "title": "", "titlemobile": ""}, "delete": {"error": "", "success": ""}, "form": {"date": "", "endDateTime": "", "message": "", "mobileEndDateTime": "", "mobileStartDateTime": "", "name": "", "startDateTime": ""}, "list": {"header": {"date": "", "name": "", "year": ""}, "title": "", "titlemobile": ""}, "title": "", "update": {"error": "", "success": "", "title": "", "titlemobile": ""}}, "service_plan": {"closure": {"error": "(de)Failed to close the service plan, Error code : {{code}}", "success": "(de)Service plan successfully closured"}, "creation": {"error": "(de)Failed to create service plan '{{label}}', Error code : {{code}}", "success": "(de)Service model '{{label}}' successfully created", "title": "(de)Service plan creation"}, "delete": {"error": "(de)Failed to suppress the service plan, Error code : {{code}}", "success": "(de)Service plan successfully deleted"}, "form": {"armingDelay": "(de)Armament delay", "armingPriority": "(de)Armament priority", "backupGroup": "(de)Backup group", "box": "", "elsBoxManagement": "", "elsStatus": "", "elsStatusManagement": "", "enabled": "(de)Enabled", "entity": "(de)Entity", "error": {"novehicles": "(de)There is no available vehicles for entity {{entityName}}"}, "firstDay": "(de) First day of the week", "id": "(de)Id", "isusedasoptionalbackupgroup": "", "label": "(de)Name", "model": "(de)Model", "optionalGroup": "(de)Optional group", "optionalGroupUnavailable": "", "popup": {"warning": {"message": "", "title": ""}}, "portalLabel": "", "vehicle": "(de)Vehicle"}, "list": {"entityName": "", "header": {"actions": "(de)Actions", "armingDelay": "(de)Armament delay", "armingPriority": "(de)Armament prio.", "closed": "", "closed-no": "", "closed-yes": "", "entity": "", "id": "(de)ID", "name": "(de)Name", "servicePlanType": "(de)Service plan type", "servicePlanType_mobile": "(de)Type", "vehicle": "(de)Vehicle"}, "modelName": "", "title": "(de)List of service plans", "titlemobile": "", "totalElements": ""}, "popup": {"closure": {"message": "(de)Do you really want to close the service plan <b>{{name}}</b>?", "subtitle": "(de)of a service plan", "title": "(de)Closure"}, "delete": {"message": "(de)Do you really want to delete the service plan <b>{{name}}</b>?", "subtitle": "(de)of a service plan", "title": "(de)Deletion"}}, "teams": {"create": {"success": "", "title": ""}, "delete": {"error": "", "success": ""}, "form": {"addperson": "", "button": {"addteam": ""}, "label": "", "popup": {"title": ""}, "shortlabel": ""}, "list": {"header": {"actions": "", "label": "", "shortlabel": ""}, "popup": {"delete": {"message": "", "subtitle": "", "title": ""}}}, "update": {"success": "", "title": "", "titlemobile": ""}}, "update": {"error": "(de)Failed to change the service plan '{{label}}', Error code : {{code}}", "information_message": "", "success": "(de)Service plan '{{label}}' successfully changed", "title": "(de)Editing a service plan", "titlemobile": ""}, "version": {"closure": {"error": "(de)Failed to close service plan version, error code : {{code}}", "success": "(de)Service plan version closed successfully"}, "create": {"error": "(de)Failed to create the version, Error code : {{code}}", "success": "(de)Version '{{label}}' successfully changed", "title": "(de)Creation of a time slots version", "titlemobile": ""}, "delete": {"error": "(de)Failed to close service plan version, error code :  {{code}}", "success": "(de)Service plan version deleted successfully"}, "details": {"from": "(de)From", "subtitle": "(de)", "title": "(de)Time slots details", "to": "(de)to"}, "edit": {"error": "(de)Failed to change the version, Error code : {{code}}", "information_message": "", "success": "(de)The version has been updated", "title": "(de)Time slots version", "titlemobile": ""}, "form": {"button": {"addversion": "(de)Add a version"}, "enddate": "(de)End date", "errors": {"modelVersionNotCreated": "(de)The version of the model is not yet created", "total_slots": {"max": "(de)Number of time slots should be 24 max", "min": "(de)Number of time slots should be at least 1", "no_more_place": {"none": "", "plural": "", "singular": ""}, "not_enough_place": ""}}, "filling": {"automatic": "(de)Automatic", "manual": "(de)Manual", "title": "(de)Filling mode: "}, "label": "(de)Name", "servicePlanType": "(de)Service plan type", "startTime": "(de)Start time", "startdate": "(de)Start date", "totalSlots": "(de)Slot", "version": "(de)Version"}, "list": {"header": {"actions": "(de)Actions", "enddate": "(de)End date", "label": "(de)Name", "split": "", "startdate": "(de)Start date", "startdate_mobile": "", "type": "", "type_mobile": ""}, "popup": {"closure": {"message": "(de)Do you really want to close the service plan version  <b>{{name}}</b> of {{startDate}} ?", "subtitle": "(de)of a service plan version", "title": "(de)Closure"}, "delete": {"message": "(de)Do you really want to delete the service plan version <b>{{name}}</b> of {{startDate}} ?", "subtitle": "(de)of a service plan version", "title": "(de)Deletion"}}}}}, "service_plan_model": {"closure": {"error": "(de)Failed to close the service plan model, Error code : {{code}}", "success": "(de)Service plan model successfully closured"}, "copy": {"error": "(de)Failed to copy service plan model", "new_name": "(de)Copy - {{name}}", "success": "(de)Service plan model '{{name}}' successfully copied", "title": "(de)Copy an existing service plan model"}, "creation": {"error": "(de)Failed to create service plan '{{name}}', Error code : {{code}}", "success": "(de)Service plan model '{{name}}' successfully created", "title": "(de)Service plan model creation"}, "delete": {"error": "(de)Failed to delete the service plan model, Error code : {{code}}", "success": "(de)Service plan model successfully deleted"}, "form": {"backup_statuses": "", "buttons": {"addversion": "(de)Add a version"}, "entity": "(de)Entity", "exclusive": "(de)Exclusive ?", "function_operational": "", "has_vehicle": "(de)Vehicle ?", "id": "(de)Identifier", "intervention_type": "(de)Intervention type", "name": "(de)Name", "popup": {"warning": {"message": {"entity": "", "vehicle": "", "vehicletypes": "", "vehicletypes_removed": ""}, "title": ""}}, "vehicle_type": "(de)Vehicle type"}, "list": {"header": {"actions": "(de)Actions", "entity": "(de)Entity", "id": "(de)Identifier", "intervention": "(de)Intervention", "name": "(de)Name"}, "title": "(de)List of service plan models", "titlemobile": ""}, "popup": {"closure": {"message": "(de)Do you really want to close the service plan model <b>{{name}}</b> ?", "subtitle": "(de)of a service plan model", "title": "(de)Closure"}, "delete": {"message": "(de)Do you really want to delete the service plan model <b>{{name}}</b> ?", "subtitle": "(de)of a service plan model", "title": "(de)Deletion"}}, "title": "(de)Manage service plan models", "update": {"error": "(de)Failed to change the service plan model '{{name}}', Error code : {{code}}", "information_message": "", "success": "(de)Service plan model '{{name}}' successfully changed", "title": "(de)Editing a service plan model", "titlemobile": ""}, "version": {"closure": {"error": "(de)Failed to close the service plan model", "success": "(de)Service plan model successfully closured"}, "copy": {"label": "", "message": "", "target": "", "title": ""}, "create": {"success": "(de)Version successfully created", "title": "(de)Version creation"}, "delete": {"success": "(de)Version '{{label}}' deleted"}, "edit": {"information_message": "", "position": {"degraded": {"title": "(de)Degraded positions"}}, "success": "(de)The version has been updated", "title": "(de)Update a version of positions", "titlemobile": ""}, "form": {"button": {"addversion": "(de)Add a version of positions"}, "enddate": "(de)End date", "enddate_mobile": "", "label": "(de)Name", "startdate": "(de)Start date", "startdate_mobile": "", "version": "(de)Version"}, "list": {"header": {"actions": "(de)Actions", "enddate": "(de)End date", "enddate_mobile": "(de)End", "label": "(de)Name", "startdate": "(de)Start date", "startdate_mobile": ""}}, "popup": {"closure": {"message": "(de)Do you really want to close the service plan model version <b>{{name}}</b> of {{startDate}} ?", "subtitle": "(de)of a service plan model version", "title": "(de)Closure"}, "delete": {"message": "(de)Do you really want to delete the version <b>{{name}}</b> of {{startDate}} ?", "subtitle": "(de)of a service plan model", "title": "(de)Deletion"}}, "position": {"empty": "Keine Position", "function": {"operational_function": "(de)Operational Function", "operational_function_mobile": "", "partialfullfill": "(de)Degraded positions"}, "type": {"additional": "(de)Additional positions", "complete": "(de)Complete positions", "degraded": "(de)Degraded positions"}}}}, "vehicles": {"els_status": {"1": "(de)Einsatzbereit Funk", "2": "(de)Einsatzbereit Wache", "3": "(de)Einsatz übernommen", "4": "(de)Am Einsatzort", "5": "", "6": "(de)Nicht einsatzbereit", "7": "(de)Patient aufgenommen", "8": "(de)<PERSON>", "1c": "(de)Alarmiert am Einsatzbereit Funk", "2c": "(de)<PERSON><PERSON><PERSON><PERSON>", "3c": "(de)Alarmierung bei Einsatz übernommen", "4c": "(de)Alarmierung am Einsatzort", "7c": "(de)Alarmierung bei Patienten transport", "8c": "(de)Alarmierung am Transportziel"}, "form": {"currentElsStatus": "(de)ELS actual status", "description": "(de)Description", "elsStatusHistoryLablel": "(de)Historique du statut ELS", "entity": "(de)Entity", "id": "(de)Id", "name": "(de)Name", "registration": "(de)Registration", "registrationmobile": "", "type": "(de)Type"}, "list": {"header": {"actions": "(de)Actions", "date": "(de)Date", "description": "(de)Description", "id": "(de)Id", "name": "(de)Name", "registration": "(de)Registration", "registrationmobile": "", "servicePlanName": "(de)Associated Service plan", "status": "(de)Status", "text": "", "type": "(de)Type"}, "title": "(de)List of vehicles", "titlemobile": ""}, "title": "(de)Manage vehicles", "update6vehicle": "", "view": {"title": "(de)Vehicle", "titlemobile": ""}}}, "allowance": {"scheduler": {"form": {"date": {"label": "(DE) Computing date or computing start date"}, "enddate": {"label": "(DE) Computing end date"}, "entity": {"label": "(DE) entities list", "nodata": "(DE) No entity selected"}, "person": {"label": "(DE) persons list", "nodata": "(DE) No person selected"}, "popup": {"confirm": {"message": {"date": "(DE)Computing  by date will be launched", "entity": "(DE) Computing  by date and entities will be launched", "person": "(DE) Computing  by date and persons will be launched"}, "title": "(DE) Computing allowances"}}, "submit": {"success": "(DE)Generation has been taken into account and will start soon"}}}, "search": {"allowance": "", "duration": "", "firstname": "", "lastname": "", "total": {"allowance": "", "duration": ""}}}, "audit": {"actionDateTime": "", "actionType": {"ADD": "", "CLOSURE": "", "COPY": "", "CREATE": "", "DAY": "", "DELETE": "", "END": "", "MERGE": "", "SLOT": "", "SPLIT": "", "START": "", "UPDATE": "", "VIEW": "", "WEEK": "", "title": ""}, "allowance": "", "copy_prestation": {"day": "", "slot": "", "week": ""}, "details": "", "impersonatedUserName": "", "link": "", "logas_title": "", "model_title": "", "person": "", "prestation": {"bypass_function": "", "date": "", "person": "", "position": "", "service_plan": "", "slot": ""}, "prestation_title": "", "service_plan": {"entity": "", "name": "", "type": ""}, "service_plan_model": {"entity": "", "name": "", "type": ""}, "service_plan_model_version": {"endDate": "", "model_name": "", "name": "", "spname": "", "startDate": ""}, "service_plan_version": {"endDate": "", "name": "", "spname": "", "startDate": ""}, "serviceplan_title": "", "slot": {"merge": "", "split": ""}, "type": {"COPY_PRESTATION": "", "LOGAS": "", "MODEL": "", "PDS": "", "PRESTATION": "", "SLOT": "", "VERSION_MODEL": "", "VERSION_PDS": "", "title": ""}, "rici": {"title": "RICI Audit", "alert-group": {"title": "Verwaltung der Alarmgruppen", "headers": {"schema-alias": "Schema<PERSON><PERSON><PERSON>", "range-name": "Bereichsname"}}, "simcard": {"title": "Verwaltung der SIM-Karten", "iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Status", "associatedPager": "Pager TEC ID", "associatedPagerId": "Pager ID", "import": {"title": "CSV-Import-Audits von SIM-Karten", "fileName": "Dateiname", "importStatus": "Importstatus", "totalRecords": "Gesamtanzahl der Einträge", "successfulRecords": "Erfolgreiche Einträge", "validationErrors": "Validierungsfehler", "importErrors": "I<PERSON>rt<PERSON>hler"}}, "pager": {"title": "Verwaltung der Pager", "pagerId": "Pager ID", "serialNumber": "Seriennummer", "associatedSimCardIccid": "SIM – ICCID", "associatedSimCardMsisdn": "SIM – MSISDN", "assignmentType": "Zuweisungstyp", "assignedPersonName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignedEntityName": "Zugewiesene Einheit", "status": "Status", "associatedSim": "SIM ID", "import": {"title": "CSV-Import-Audits für Pager", "fileName": "Dateiname", "importStatus": "Importstatus", "totalRecords": "Gesamtanzahl der Einträge", "successfulRecords": "Erfolgreiche Einträge", "validationErrors": "Validierungsfehler", "importErrors": "I<PERSON>rt<PERSON>hler", "statuses": {"COMPLETED_WITH_ERRORS": "COMPLETED_WITH_ERRORS", "COMPLETED_SUCCESS": "COMPLETED_SUCCESS", "FAILED_FILE_PROCESSING": "FAILED_FILE_PROCESSING", "INITIATED": "INITIATED", "INITIATED_FILE_INFO_UNAVAILABLE": "INITIATED_FILE_INFO_UNAVAILABLE"}}}, "ricrange": {"title": "Verwaltung der RIC-Bereiche", "name": "Name", "type": "<PERSON><PERSON>", "rangeStart": "<PERSON><PERSON><PERSON>", "rangeEnd": "<PERSON><PERSON><PERSON>", "entity": "Einheit"}, "schema": {"title": "Verwaltung der RIC Schemas", "schemaName": "Name", "schemaAlias": "<PERSON><PERSON>", "functionCodeA": "Funktion A", "functionCodeB": "Funktion B", "functionCodeC": "Funktion C", "functionCodeD": "Funktion D", "type": "<PERSON><PERSON>"}, "tabs": {"headers": {"rici": "RICI"}}}}, "authentication": {"error": "(de)You are not authenticated", "logout": {"success": "(de)You have been disconnected"}, "lost": "(de)You are no more authenticated"}, "availability_planning": {"copypopup": {"fromTo": "(de){{start}} to {{end}}\n", "message": "(de)Are you sure to copy availabilities from {{fromDate}} to {{newDate}} ? Existing availabilities of {{ newDate }} will be deleted.<br/>Availabilities to copy are:<br/>{{events}}", "messageNoData": "", "messageWeek": "(de)Are you sure to copy availabilities from {{fromDate}} to {{toDate}} ? Existing availabilities between {{ newDate }} and {{newEndDate}} will be deleted", "success": "(de)Availabilities successfully copied", "title": "(de)Copy availabilities"}, "deletepopup": {"message": "(de)Do you really want to delete the availability?", "success": "(de)Availability deleted successfully", "title": "(de)Delete availability"}, "list-placeholder": "", "list-placeholder-mobile": "", "logas": "", "no_data": "", "popup": {"form": {"display_warning": "", "enddate": "(de)To", "entity": "(de)Entities", "information_message": "", "interventiontype": "(de)Intervention type", "professional": "", "startdate": "(de)From", "success": "(de)Availability successfully added", "type": "", "volunteer": ""}, "title": "(de)I want to be available", "title_non_editable": "(de)My availabilities", "titlemobile": ""}, "prestationpopup": {"closebutton": "(de)Close", "enddate": "(de)To", "entity": "(de)Entity", "interventiontype": "(de)Intervention type", "position": "(de)Position", "serviceplan": "(de)Service plan", "startdate": "(de)From", "title": "(de)Assignment"}, "show_prestations": "(de)Show prestations", "title": "(de)My planning and availabilities"}, "backup-management": {"export": "", "title-activation": "", "title-export": ""}, "chart": {"legend": {"ambulance": "(de)Ambulance", "availability": "(de)Reals Availabilities", "commandment": "(de)Commandment", "complete": "(de)Complete", "degraded": "(de)Degraded", "dms": "", "empty": "(de)Empty", "fire": "(de)Fire", "gis": "", "incomplete": "(de)Incomplete", "intervention": "(de)Interventions", "mobile": {"ambulance": "(de)Amb.", "commandment": "(de)Com.", "complete": "(de)Comp.", "degraded": "(de)Deg.", "dms": "", "empty": "(de)Emp.", "fire": "(de)Fire", "gis": "", "incomplete": "(de)Incomp.", "nodata": "(de)No data", "others": "(de)Others", "partial": "(de)Par.", "samu": ""}, "nodata": "(de)No data", "others": "(de)Others", "partial": "(de)Partial", "prestation": "(de)Prestations", "samu": "", "total": "(de)Total", "totals": ""}}, "current-situation": {"active-backup": "", "add-person": "", "els-status": "", "fromto": "", "legend": {"barracked": "", "pro": ""}, "person-not-found": "", "persons": "", "ressources": "", "schedule": "", "service-plan": "", "status": "", "time-slot": "", "title": ""}, "dashboard": {"default": {"welcome": "Wëllkomm op CGDIS Portal"}, "manager": {"all-service-plan": "(de)My service plans", "new-service-plan": "(de)New service plan", "occupancy-rate": "(de)Occupancy rate", "subtitle": "(de)of all my manual service plans", "title": "(de)Occupancy rate", "today": "(de)Today"}, "members": {"chart": {"ambulance": "(de)Ambulance", "ambulance-mobile": "", "availability": "(de)Availability", "available-hours": "(de)Hours availables", "commandment": "(de)Commandment", "commandment-mobile": "(de)COM", "dms": "(de)DMS", "dms-mobile": "(de)DMS", "exclusive": "", "fire": "(de)Fire", "fire-mobile": "", "gis": "(de)GIS", "gis-mobile": "(de)GIS", "mobile": {"ambulance": "(de)Amb.", "availability": "(de)Dispo.", "commandment": "(de)Com.", "dms": "(de)DMS", "exclusive": "", "fire": "(de)Inc.", "gis": "(de)GIS", "others": "(de)Au<PERSON>", "planning": "(de)Planning", "professional": "(de)Service pro.", "samu": "(de)SAMU", "tooltip": "(de)<PERSON>ures aff."}, "noData": "(de) No data", "other": "(de)Other", "others": "(de)Other", "others-mobile": "(de)Other", "planning": "(de)Planning", "professional": "(de)Professional service", "professional-mobile": "", "samu": "(de)SAMU", "samu-mobile": "(de)SAMU", "tooltip": "(de)Hours worked"}, "summary": {"availability": "(de)Avail:", "month": "(de)This month", "perm": "(de)Perm:", "week": "(de)This week"}, "title": "(de)My planning & availabilities"}, "news": {"button": "(de)See more", "title": "(de)News"}}, "date": {"days": {"friday": "", "monday": "", "saturday": "", "sunday": "", "thursday": "", "tuesday": "", "wednesday": ""}, "duration": {"dashboard": "", "dayshoursminutes": "", "hours": "", "hoursminutes": "", "minutes": "", "monthsdayshoursminutes": "", "yearsmonthsdayshoursminutes": ""}, "months": {"1": {"abr": "", "full": ""}, "2": {"abr": "", "full": ""}, "3": {"abr": "", "full": ""}, "4": {"abr": "", "full": ""}, "5": {"abr": "", "full": ""}, "6": {"abr": "", "full": ""}, "7": {"abr": "", "full": ""}, "8": {"abr": "", "full": ""}, "9": {"abr": "", "full": ""}, "10": {"abr": "", "full": ""}, "11": {"abr": "", "full": ""}, "12": {"abr": "", "full": ""}}, "two-columns-view": {"close": "", "open": ""}}, "default": {"button": {"back": "(de)Back", "cancel": "(de)Can<PERSON>", "delete": "(de)Delete", "logas": "(de)Se connecter au logas", "scan": "(de)Scan QR code", "submit": "(de)Submit", "validate": "(de)Validate"}, "construction": {"message": "", "title": ""}, "day_more": "(de)D+1", "no": "(de)No", "placeholder": {"search": "(de)Search...", "select-time-slot": "(de)Select a time slot"}, "popup": {"fromto": {"from": "", "to": ""}}, "search": "(de)Search", "table": {"noresult": "(de)No data to display", "total": "(de)Total"}, "yes": "(de)Yes"}, "entities": {"empty": "", "type": {"CIS": "(de)EMERGENCY CENTER", "CS": "(de)SUPPORT CENTER", "GROUP": "(de)GROUPS", "GS": "(de)SPECIAL GROUPS", "NATIONAL": "(de)NATIONAL", "SAMU": "(de)SAMU", "UNKNOWN": "", "ZONE": "(de)ZONE"}}, "error": {"badgateway": "(de)Timeout occurred. Please contact your administrator", "entity": {"export": "", "update": ""}, "export": {"default": "", "no_data_to_export": "", "no_export_type": ""}, "form": {"rici-range-form": {"ComparableAfterAnother": "Das Ende des Bereichs muss nach dem Beginn des Bereichs liegen"}, "rici-schema-form": {"Min": "Das RIC Suffix muss zwischen 900 und 999 (e<PERSON><PERSON><PERSON><PERSON><PERSON>) liegen.", "Max": "Das RIC Suffix muss zwischen 900 und 999 (e<PERSON><PERSON><PERSON><PERSON><PERSON>) liegen."}, "RiciRange": "Der Bereich ist bereits belegt.", "RiciRangeName": "Der Name des Bereichs muss einzigartig sein.", "RiciSchemaName": "Der Name des Schemas muss einzigartig sein.", "RiciSchemaAlias": "Der Alias des Schemas muss einzigartig sein.", "RiciSchemaSuffix": "Das Suffix des Schemas muss einzigartig sein.", "OddNumber": "<PERSON> Zahl muss ungerade sein.", "DatetimeAfterAnother": "", "DatetimeNotEquals": "(de)Dates should not be identical", "EmailWithTld": "(de)Wrong email address", "Emails": "", "FutureDate": "", "FutureDatetime": "", "Issi": "(de)Wrong ISSI number", "NotBlank": "(de)Mandatory field", "NotEmpty": "(de)Mandatory field", "NotNull": "(de)Mandatory field", "Pager": "(de)Wrong pager number", "PagerMobile": "(de)Wrong pager mobile number", "PhoneNumber": "(de)Wrong phone number", "RequiredIfBooleanValue": "(de)Mandatory field", "ServicePlanArmingDelayRange": "(de)The value of armament delay must be between 60 and 900 seconds", "ServicePlanArmingDelayRangeBarracked": "(de)The value of armament delay must be between 60 and 900 seconds", "ServicePlanArmingDelayRangeNotBarracked": "(de)The value of armament delay must be between 60 and 1200 secondes", "ServicePlanMinimumArmingDelayRangeBarracked": "(de)The value of armament delay must be between 60 and 900 seconds", "ServicePlanModelFullEditable": "", "ServicePlanModelVersionUniqueLabel": "(de)The name of the model version already exists", "ServicePlanUniqueLabel": "(de)The name of the service plan already exists", "ServicePlanUpdateModel": "", "ServicePlanUpdateModelWithPrestations": "", "ServicePlanVehicleMandatory": "(de)Mandatory field", "ServicePlanVersionUniqueLabel": "(de)The label of the version already exists", "ServicePlanVersionUniqueStartdate": "(de)A version already exists with this start date", "Size": "(de)The size must be between {{args1}} and {{args0}}", "SplitSlotTimeValidator": "", "UpdateServicePlanArmingDelayRangeBarracked ": "", "UpdateServicePlanArmingDelayRangeNotBarracked": "", "VolunteerAvailabilityCheckAssignment": "", "VolunteerAvailabilityUpdateStartDate": "(de)The start date must be in the future or after previous start date", "availability-full-calendar-popup": {"DatetimeAfterAnother": "(de)The end date should be after start date"}, "datetimeDateNotNull": "(de)Date is mandatory", "invalid": "(de)The form is not valid", "max": "(de)The value is too large", "maxlength": "(de)Please enter a maximum of {{requiredLength}} characters", "min": "(de)The value is too small", "minlength": "(de)Please enter at least {{requiredLength}} characters", "required": "(de)Mandatory field", "service-plan": {"split": {"inpast": "", "notinslotrange": ""}, "team": {"label": {"exist": ""}, "members": {"duplicate": ""}, "shortlabel": {"exist": ""}}}}, "person": {"already_logas": "", "export": "", "no_assignments": ""}, "prestation": {"allowance": {"configuration": {"pastorcurrent": "", "samestartdate": ""}}, "copy": "", "exclusive": "", "nothing-copy": ""}, "service_plan": {"assignteam": {"empty": ""}, "box-free": "", "person": {"already_assigned": "(de)This person is already assigned to this time slot"}, "timeslot": {"merge": {"default": "", "service_plan_different": "", "slot_already_started": ""}}, "vehicle-free": "", "version": {"copy": "", "exist_with_start_date": "(de)A version already exists with this start date"}}, "service_plan_model": {"update": {"forbidden": "(de)Update this service plan model is not possible"}, "version": {"copy": "", "dates": "", "exist_with_start_date": "(de)A version already exists with this start date", "notfound": ""}}, "technical_error": "(de)Technical error. contact your administrator", "timeout": "", "userrights": {"delete": "(de)This permission is inalienable to the role. Can not delete it"}, "volunteeravailability": {"delete": {"prestations_already_assigned": ""}, "duplicate": {"error": "", "message_pro": "", "message_vol": "", "title": ""}, "update": {"prestations_already_assigned": ""}}, "rici": {"simcard": {"alreadyassociated": "Die SIM-Karte ist bereits einem Pager zugewiesen. Bitte wählen Si<PERSON> eine andere aus.", "alreadyexists": {"ICCID": "Eine SIM-Karte mit dieser ICCID existiert bereits.", "MSISDN": "Eine SIM-Karte mit dieser MSISDN existiert bereits."}}, "pager": {"alreadyexists": {"SERIAL_NUMBER": "Die Seriennummer ist bereits im System vorhanden. <PERSON>te geben Si<PERSON> eine neue an.", "PAGER_ID": "Die Pager-ID ist bereits im System vorhanden. Bitte geben Si<PERSON> eine neue an."}}, "person": {"already_has_pager_assigned": "Diesem Benutzer ist bereits ein Pager zugewiesen. Bitte wählen Si<PERSON> eine andere Person aus."}, "ric": {"already_used": "Der RIC wird bereits verwendet.", "already_used_by_alert_group": "Der RIC wird bereits von einer Alarmgruppe verwendet.", "is_not_number": "Der RIC-Wert ist keine gültige Zahl.", "is_divisible_by_8": "Der RIC-Wert darf nicht durch 8 teilbar sein.", "outside_of_entity_range": "Der RIC-Wert liegt außerhalb des Entitätsbereichs.", "no_available_ric_in_range": "<PERSON><PERSON> verfügbarer RIC in den Bereichen für diese Entität gefunden.", "no_active_range_for_entity": "Keine aktiven RIC-Bereiche für diese Entität gefunden."}}}, "error-management": {"delete": {"success": ""}, "list": {"header": {"actions": "", "cause": "", "creationdate": "", "message": "", "objectidentifier": "", "status": "", "type": ""}}, "resend": {"error": "", "success": ""}, "status": {"CREATED": "", "DONE": "", "RESENT": ""}, "title": "", "type": {"ASSIGNEMENT_QUEUE_NOTIFICATION": "", "DELETE_ONE_VEHICLE_HISTORIC": "", "ERROR_MESSAGE_TO_IGNORE": "", "INTERNAL_PROCESS": "", "NOTIFIER_EMAIL": "", "NOTIFIER_MANAGER": "", "PROCESS_DELETE_BACKUP_GROUPS": "", "PROCESS_DELETE_PERSON_FROM_MSNAV": "", "PROCESS_DELETE_PERSON_IN_ELS": "", "PROCESS_DELETE_PERSON_IN_LEVESO": "", "PROCESS_EXPORT_BACKUP_112": "", "PROCESS_EXPORT_MAIL": "", "PROCESS_INSERT_APTITUDES_FROM_XLSX": "", "PROCESS_LUXDOK_INSERT_INTERVENTION": "", "PROCESS_READ_APTITUDES_FROM_XLSX": "", "PROCESS_REFRESH_BACKUP_GROUPS": "", "PROCESS_REFRESH_ENTITIES": "", "PROCESS_REFRESH_PERSON_ASSIGNMENTS": "", "PROCESS_REFRESH_VEHICLE": "", "PROCESS_SERVICE_PLAN_EXPORT_CSV": "", "PROCESS_SERVICE_PLAN_EXPORT_ONE_CSV": "", "PROCESS_SERVICE_PLAN_SEND_FILLING_EXPORT": "", "PROCESS_SERVICE_PLAN_UPDATE": "", "PROCESS_SERVICE_PLAN_UPDATE_ALL": "", "PROCESS_UPDATE_APTITUDES_FROM_GEPS": "", "PROCESS_UPDATE_ENTITY_LEVESO": "", "PROCESS_UPDATE_PERSON_ELS": "", "PROCESS_UPDATE_PERSON_FROM_MSNAV": "", "PROCESS_UPDATE_PERSON_GEPS": "", "PROCESS_UPDATE_PERSON_LEVESO": "", "PROCESS_UPDATE_PERSON_LUXDOK": "", "PROCESS_UPDATE_PERSON_MSNAV": "", "PROCESS_UPDATE_PERSON_PAGERMGMT": "", "PROCESS_UPDATE_REMARKS_FROM_GEPS": "", "REFRESH_BACKUP_GROUPS": "", "REFRESH_ENTITIES": "", "REFRESH_ONE_BACKUP_GROUPS": "", "REFRESH_VEHICLES": "", "SEND_ALL_ASSIGNMENT_CLOSURE": "", "SEND_ALL_PRESTATION_QUEUE": "", "SEND_ONE_PRESTATION_QUEUE": "", "SERVICE_PLAN_DELETE": "", "SERVICE_PLAN_OPTIONAL_BACKUP_PUSH": "", "SERVICE_PLAN_PUSH": "", "SMSI_EXPORT": "", "SMSI_SERVICE_PLAN_FILLING": "", "UPDATE_ENTITY_LEVESO": "", "UPDATE_PERSON_ELS": "", "UPDATE_PERSON_GEPS": "", "UPDATE_PERSON_LEVESO": "", "UPDATE_PERSON_LUXDOK": "", "UPDATE_PERSON_MSNAV": "", "UPDATE_PERSON_PAGERMGMT": "", "UPDATE_VEHICLE_TACTICAL_STATE": "", "UPDATE_VEHICLE_TACTICAL_STATE_INPUT": ""}}, "general_availability": {"legend": {"barracked": "", "professional": "", "volunteer": ""}, "no_data": "", "title": ""}, "general_information": {"active_assignment": {"list": {"allClosedAssignment": "(de) Afficher les affectations clôturées", "endDate": "(de)End date", "entityName": "(de)Name", "primaryType": {"list": {"PRIMARY": "(de)Primary", "SECONDARY": "(de)Secondary", "TECHNICAL": "(de)Technical"}, "title": "(de)Primary type"}, "startDate": "(de)Start date", "type": {"list": {"EXT": "EXT", "PRO": "PRO", "VOL": "VOL"}, "title": "(de) Statut"}}, "title": "(de)Active assignments"}, "activity": {"allowance": "", "allowance_mobile": "", "availabilities": {"duration": "", "end": "", "entities_availability": "", "entities_availability_with_barracked": "", "intervention_types": "", "start": "", "start_mobile": "", "title": ""}, "day": "", "detail": {"availabilities": "", "availabilities_mobile": "", "interventions": "", "interventions_mobile": "", "prestations": "", "prestations_mobile": "", "totals": ""}, "hours": "", "interventions": {"alarm": "", "end": "", "end_mobile": "", "function_name": "", "keyword": "", "mission_number": "", "number": "", "number_mobile": "", "radio": "", "start": "", "start_mobile": "", "title": ""}, "month": "", "periode": "", "prestations": {"duration": "", "end": "", "end_mobile": "", "portal_label": "", "position_label": "", "realDuration": "", "realDuration_mobile": "", "start": "", "start_mobile": "", "title": ""}, "range": "", "semester": "", "title": "", "title_logas": "", "totals": {"allowance": "", "allowance_mobile": "", "details": {"barrackedAmount": "", "entity": "", "notBarrackedAmount": "", "proDuration": "", "proDuration_mobile": "", "realDuration": "", "sum": "", "total": "", "volDuration": "", "volDuration_mobile": ""}, "duration": "", "label": "", "nbPerson": "", "title": ""}, "type": "", "week": "", "year": ""}, "address": {"city": "(de)City", "country": "(de)Country", "number": "(de)N°", "street": "(de)Street", "subtitle": "(de)Address {{number}}", "title": "(de)Postal address", "zip_code": "(de)Postal Code"}, "aptitudes": {"list": {"aptitude": "", "description": "", "endDate": "", "isImported": "", "startDate": "", "status": {"title": ""}, "userComment": ""}, "title": ""}, "availability": {"type": {"ambulance": "", "commandment": "", "dms": "", "fire": "", "gis": "", "other": "", "others": "", "samu": ""}}, "bank_account": {"bank_code": "(de)Bank code", "bic": "(de)BIC", "iban": "(de)IBAN", "title": "(de)Bank details"}, "contact_information": {"alert_email": "", "alert_email_notification": "", "alert_mobile": "", "general": "(de)Administrative Contacts", "operational": "(de)Operational Contacts", "private": {"email": "(de)Private email", "mobile": "(de)Private mobile", "phone": "(de)Private phone"}, "professional": {"email": "(de)Professional email", "mobile": "(de)Professional mobile", "phone": "(de)Professional phone"}, "ric": "", "ricMobile": "(de)RIC Mobile", "warning_no_mail": "(de)Warning ! There is no mail ! Please enter one email adress!", "warning_one_mail": "(de)Warning ! There is only one mail which will be used by default !"}, "diplomas": {"list": {"comment": "(de)Comment", "ident": "(de)Identification", "name": "(de)Name", "type": "(de)Type", "validFrom": "(de)<PERSON><PERSON> from", "validUntil": "(de)Valid until"}, "title": "(de)Diplomas"}, "driver_license": {"list": {"category": "(de)Category", "endDate": "(de)End date", "startDate": "(de)Start date"}, "title": "(de)Driving licenses"}, "function_operational": {"title": ""}, "general": "(de)General", "managerial_occupations": {"list": {"header": {"allClosedOccupations": "(de) Afficher les emplois cloturés", "assignment": "", "assignment_description": "", "code": "", "endDate": "", "external": "", "external_desktop_reduced": "", "external_mobile": "", "label": "", "nomination": "", "professional": "", "professional_desktop_reduced": "", "professional_mobile": "", "startDate": "", "status": {"list": {"ONGOING": "", "RESIGNED": "", "REVOKED": "", "SUSPENDED": ""}, "title": ""}, "type": "", "volunteer": "", "volunteer_desktop_reduced": "", "volunteer_mobile": ""}}, "title": ""}, "medical": "", "medical-information": {"legend": {"apt": "Apt", "apt_with_restriction": "Apt with restrictions", "inapt": "Inapt", "temporary_inaptitude": "Temporary inaptitude"}}, "medical-report": {"aptitude": "", "empty": "", "new-aptitudes": "", "new-restrictions": "", "no-aptitude": "", "no-restriction": "", "remark": "", "restriction": "", "start": "", "statut": "", "title": ""}, "operational": "(de)Operational", "operational_contact": {"email": "(de)Email", "issi": "(de)ISSI", "mobile": "(de)Mobile", "pager": "(de)Pager", "pager_mobile": "(de)Pager mobile", "phone": "(de)Phone", "send_alarm_clock": ""}, "operational_dates": {"list": {"header": {"endDate": "", "startDate": "", "type": {"list": {"PROFESSIONAL": "(de)Professional", "VOLUNTEER": "(de)Volunteer"}, "title": ""}}}, "title": ""}, "operational_functions": {"list": {"granted": "(de)This function is not yet granted in any center", "label": "(de)Function"}, "title": "(de)Operational Functions"}, "operational_grades": {"list": {"header": {"allClosedGrade": "Afficher les grades clôturés", "allClosedGrades": "", "code": "", "decreeDate": "", "echelon": "", "endDate": "", "gradeType": {"list": {"DMS": "", "GIS": "", "INCSASAP": ""}, "title": ""}, "professional": "", "professional_desktop_reduced": "", "professional_mobile": "", "psupp": "", "samu": "", "samu_mobile": "", "startDate": "", "title": "", "volunteer": "", "volunteer_desktop_reduced": "", "volunteer_mobile": ""}}, "title": ""}, "operational_occupations": {"list": {"header": {"code": "(de)Code", "endDate": "(de)End date", "entity": "(de)Entity", "external": "", "external_desktop_reduced": "", "external_mobile": "", "label": "(de)Label", "nomination": "(de)Nomination date", "professional": "(de)Professional", "professional_desktop_reduced": "", "professional_mobile": "", "startDate": "(de)Start date", "status": {"list": {"ONGOING": "(de)Ongoing", "RESIGNED": "(de)Resigned", "REVOKED": "(de)Revoked", "SUSPENDED": "(de)Suspended"}, "title": "(de)Status"}, "type": "(de)Type", "volunteer": "(de)Volunteer", "volunteer_desktop_reduced": "", "volunteer_mobile": ""}}, "title": "(de)Operational Occupations"}, "operational_volunteer_internship": {"duration": "", "endDate": "", "startDate": "", "title": ""}, "operational_young_firefighter": {"firstName": "", "first_tutor": "", "lastName": "", "parental_consent": "", "phoneNumber": "", "second_tutor": "", "title": ""}, "person_medical_information": {"remark": "", "title": ""}, "personal_information": {"birthdate": "(de)Birth date", "birthplace": "(de)Place of birth", "both": "(de)All", "cgdisrn": "", "code": "(de)Naming code", "firstname": "(de)First name", "foreign": "(de)Foreign ID number", "gender_f": "(de)Female", "gender_i": "(de)Undefined", "gender_m": "(de)Male", "hiring": "(de)CGDIS start date", "iam": "(de)IAM number", "is_candidate": "(de)Candidat", "is_external": "(de)Extern", "is_intern": "(de)Stagiaire", "is_operational_firefighter": "(de)Operational firefighter", "is_operational_firefighter_short": "", "is_professional": "(de)Professional", "is_professional_adm_tech": "(de)Professionnel administratif et technique", "is_professional_adm_tech_short": "", "is_professional_operational": "(de)Professionnel opérationel", "is_professional_operational_short": "", "is_professional_tech": "(de)Technical professional", "is_retired": "(de)Retired", "is_samu": "(de)<PERSON>u", "is_support_firefighter": "(de)Support firefighter", "is_support_firefighter_short": "", "is_technical": "(de)Technical", "is_veteran": "(de)<PERSON>eteran", "is_volunteer": "(de)<PERSON><PERSON><PERSON>", "is_volunteer_tech": "(de)Technical volunteer", "is_young_firefighter": "(de)Young firefighter", "is_young_firefighter_short": "", "lastname": "(de)Last name", "nationalRegistrationNumber": "", "person_title": "(de)Title", "rnrpp": "(de)RNRPP number", "sex": "(de)Sex", "status": "(de)Status", "tic": "", "title": "(de)Personal information", "vacation": "(de)CGDIS end date"}, "restrictions": {"list": {"description": "", "startDate": "", "startDate_mobile": "", "type": "", "userComment": ""}, "title": ""}, "roles": {"title": ""}, "suspension": {"list": {"endDate": "(de)End date", "startDate": "(de)Start date"}, "title": "(de)Suspensions"}}, "i18n": {"error": {"timeout": "Timeout"}}, "intervention": {"types": {"ambulance": "(de)Ambulance", "ambulance_mobile": "", "commandment": "(de)Commandment", "commandment_mobile": "(de)Commandment", "dms": "(de)DMS", "dms_mobile": "(de)DMS", "fire": "(de)Fire", "fire_mobile": "", "gis": "(de)GIS", "gis_mobile": "(de)GIS", "others": "(de)Others", "others_mobile": "", "samu": "SAMU", "samu_mobile": "SAMU"}}, "layout": {"navigation": {"footer": {"copyright": "(de)2023 CGDIS. All rights reserved.", "copyrightmobile": "(de)2023 CGDIS. All rights reserved.", "link": {"conditions": "(de)Terms of use", "confidential": "(de)Confidentiality"}}, "menu": {"items": {"admin": {"box": "", "boxes": "", "entities": "(de)Entities", "export": "", "function_operational": "", "general-message": "", "position_template": "", "public-holiday": "Public holidays", "service_plan": "(de)Service plans", "service_plan_model": "(de)Service plan models", "vehicles": "(de)Vehicles"}, "administration": "(de)Administrative", "allowance": {"configuration": "(de)Configuration", "search": "(de)Allowances global view", "settings": "(de)Allowance configuration", "title": "(de)Allowance management"}, "audit": {"list": {"empty": "", "title": ""}}, "backup-112-management": "", "current_situation": "(de)Phase 2 - Current situation", "my_planning": "(de)Edit my availabilities", "news": "(de)News", "optional_backup_group_management": {"list": {"empty": "", "title": ""}, "title": ""}, "organizational": "(de)Organizational", "people_management": {"function": "", "list": {"empty": "", "title": ""}, "title": "", "volunteer_availabilities_logas": ""}, "people_medical_information": {"list": {"empty": "(de)No visible member", "title": "(de)Member medical information viewing"}, "title": "(de)Member medical information viewing"}, "performances": "(de)My Planning & availabilities", "plan_travail": "(de)Phase 2 - My Workplans", "service_plan": "(de)My Service plans", "sync": {"audit_management": "", "error_management": "", "logas": "", "scheduler_management": "", "title": ""}, "user_rights": {"permissions": "(de)Permissions", "roles": "(de)Roles", "title": "(de)User rights"}, "vehicules": "(de)My Vehicles", "rici": {"menu": "RICI", "alert-groups": "Verwaltung der Alarmgruppen", "sim": "Verwaltung der SIM-Karten", "ranges": "Verwaltung der RIC-Bereiche", "schemas": "Verwaltung der RIC-Schemas", "pagers": "Verwaltung der RIC-Pager", "pagers-assignments": "Verwaltung der Zuweisung von Pagern und Alarmgruppen"}}, "title": "(de)<PERSON>u"}, "profile": {"myplanning": "(de) My Planning", "mypreferences": {"assignment_notification": "", "assignment_notification_mobile": "", "availability_notification": "", "availability_notification_mobile": "", "calendar_days": "(de)Number of days by week:", "closed_calendar": "", "error_notification": "", "error_notification_mobile": "", "export-person-full": "", "export-person-notechnical": "", "export-person-technical": "", "export-prestations": "", "export-prestations-first-semester": "", "export-prestations-range": "", "export-prestations-second-semester": "", "first-day": {"1": "Mon", "2": "Die", "3": "<PERSON><PERSON>", "4": "Don", "5": "Fre", "6": "Sam", "7": "Son"}, "first_day_of_week": "", "fisrt_day_of_week": "(de)Frist day of the week:", "full_availability": "", "full_availability_mobile": "", "input_geps_error_notification": "", "input_geps_error_notification_mobile": "", "input_geps_notification": "", "input_geps_notification_mobile": "", "language": "(de)Language:", "notification": "", "other_prestations": "", "other_prestations_mobile": "", "partial_availability": "", "partial_availability_mobile": "", "planning": "", "prestation_notification": "", "prestation_notification_mobile": "", "profil": "", "serviceplan": "", "show_prestation": "", "show_prestation-mobile": "", "title": "(de)My Preferences", "update-error": "(de)Error during the update of your preference", "update-success": "(de)Preference updated with success", "update-vehicle": "", "update-vehicle-6": "", "update-vehicle-6_mobile": "", "update-vehicle-6c": "", "update-vehicle-6c_mobile": ""}, "myprofile": "(de)My Profile"}, "title": "(de)<strong>CGDIS</strong> Portal", "titleTest": " (de)<strong>CGDIS</strong> Portal Test"}}, "logas": {"connected": "You're connected as {{userName}}", "info": "To connect in logas, please select one person below :", "logout": "Back to initial profil", "successful": "You're connected", "warning": ""}, "login": {"field": {"password": "(de)Password", "username": "(de)IAM number"}, "successful": "(de)You are connected"}, "managerial_occupations": {"list": {"header": {"assignment": "(de)Assignment", "assignment_description": "(de)Assignment description", "code": "(de)Code", "endDate": "(de)End date", "external": "(de)External", "label": "(de)Label", "nomination": "(de)Nomination date", "professional": "(de)Professional", "startDate": "(de)Start date", "status": {"list": {"ONGOING": "(de)Ongoing", "RESIGNED": "(de)Resigned", "REVOKED": "(de)Revoked", "SUSPENDED": "(de)Suspended"}, "title": "(de)Status"}, "type": "(de)Type", "volunteer": "(de)Volunteer"}}, "title": "(de)Managerial Occupations"}, "mobile-enrollment": {"ask": "", "title": ""}, "news": {"detail": {"button": "", "title": ""}, "list": {"empty": "", "title": ""}}, "no_data": "(de)No data available", "operational_dates": {"list": {"header": {"endDate": "(de)End Date", "startDate": "(de)Start Date", "type": {"list": {"PROFESSIONAL": "PROFESSIONAL", "VOLUNTEER": "VOLUNTEER"}, "title": "(de)Type"}}}, "title": "(de)Operationals Dates"}, "operational_functions": {"popup": {"message": "(de)By disabling this operational function, some of the person's prestations may be modified.<br>Continue ?", "subtitle": "(de)Operational function disabling", "title": "(de)Warning"}}, "operational_grades": {"list": {"header": {"code": "(de)Code", "decreeDate": "(de)DecreeDate", "echelon": "(de) Echelon", "endDate": "(de)End date", "gradeType": {"list": {"DMS": "(de)DMS", "GIS": "(de)GIS", "INCSASAP": "(de)INCSA/SAP"}, "title": "(de)Status"}, "professional": "(de)Professional", "psupp": "(de)Support firefighter", "samu": "(de)<PERSON>u", "startDate": "(de)Start date", "title": "(de)Title", "volunteer": "(de)Volunteer"}}, "title": "(de)Operational Grades"}, "operational_volunteer_internship": {"duration": "<PERSON><PERSON><PERSON>", "endDate": "Date de fin", "startDate": "Date de début"}, "operational_young_firefighter": {"firstName": "Prénom", "first_tutor": "Premier tuteur", "lastName": "Nom", "parental_consent": "Accord <PERSON><PERSON><PERSON>", "phoneNumber": "Numéro de téléphone", "second_tutor": "Deuxième tuteur"}, "optional_backup_group_management": {"list": {"allgroups": "", "allgroupsmobile": "", "cgdisregistrationnumber": "", "members": "", "name": "", "select_entity": "", "title": ""}, "title": ""}, "people_management": {"functions": {"allpersons": "(de) Afficher également les functions des entités subordonnées", "allpersonsmobile": "Functions des entités subordonnées", "ambulance": "", "availability": "(de)Availability", "cgdisregistrationnumber": "", "cgdisregistrationnumbermobile": "", "commandment": "", "create": {"success": "(de)The function has been added with success"}, "deletion": "", "deletion_message": "", "details": {"cgdisRegistrationNumber": "(de)CGDIS registration number", "firstName": "(de)First name", "lastName": "(de)Last name"}, "dms": "", "fire": "", "firstname": "(de)Firstname", "function_operational": "(de)Operational functions", "function_operational_mobile": "Operational func.", "gis": "", "granted": "(de)Granted ?", "lastname": "(de)Lastname", "other": "", "person_number_ext": "Nombre d'Externe'", "person_number_ext_mobile": "EXT", "person_number_pro": "Nombre de Professionnels", "person_number_pro_mobile": "PRO", "person_number_vol": "Nombre de Volontaires", "person_number_vol_mobile": "VOL", "samu": "", "select_entity": "(de)Select an entity", "status": "(de)Status", "summary": "", "table": {"filterName": ""}, "technical": "(de)Show technical assignments", "title": "(de)Functions", "type": "", "update": {"information_message": "", "success": "(de)The function has been updated with success"}}, "general_contact": {"update": {"success": ""}}, "operational_contact": {"update": {"success": ""}}, "title": "(de)People Management"}, "people_medical_information": {"functions": {"allpersons": "(de)Also show people in subordinate entities", "allpersonsmobile": "(de)Show subordinate entities", "apt": "(de)Apt", "apt_with_restriction": "", "aptitude": "(de)Aptitude", "aptitudes": "(de)Aptitudes", "aptitudesmobile": "", "cgdisregistrationnumber": "(de)CGDIS registration number", "cgdisregistrationnumbermobile": "", "expired": "", "firstname": "(de)Firstname", "inapt": "(de)Inapt", "lastname": "(de)Lastname", "noSuspensionEndDate": "(de)Indefinitely suspended", "restrictions": "(de)Restrictions", "restrictionsmobile": "", "status": "(de)Statut", "suspensionEndDate": "(de)Suspension until", "suspensionToggle": "", "suspensions": "", "technical": "(de)Show technical assignments", "temporary_inapt": "(de)Temporary inapt", "toggle_filters": "(de)Filters", "undefined": "", "validity_from": "(de)Validity from ", "validity_until": "(de)Expire after", "without_aptitudes": "(de)Without aptitudes"}}, "planning": {"status": {"availability": "(de)Available", "planning": "(de)Planning", "professional": "(de)Professional"}}, "prestation": {"assignteam": {"success": ""}, "copy": {"popup": {"assignment": "", "assignment-message": "", "confirm": {"copy-day-message": "", "copy-week-message": "", "target-date": "", "title": ""}, "day": "", "day-message": "", "function": "", "function-message": "", "message": "", "slotnotexist": "", "slotnotexist-message": "", "title": ""}, "success": ""}}, "scheduler-management": {"description": {"backup_optional_group_push_request": "", "entities_push_request": "", "export_backup_112": "", "person_assignments_push_request": "", "prestation_queue_all": "", "prestationsallowancesall": "(DE) Compute allowances for date (or dates range), entities, persons", "service_plan_csv_export_all": "", "service_plan_update_all_requestall": "", "vehicle_box_push_request": "", "vehicle_push_request": ""}, "error": "", "header": {"action": "", "cron": "", "description": "", "name": ""}, "name": {"backup_optional_group_push_request": "", "entities_push_request": "", "export_backup_112": "", "person_assignments_push_request": "", "prestation_queue_all": "", "prestationsallowancesall": "(DE) Generate allowances", "service_plan_csv_export_all": "", "service_plan_update_all_requestall": "", "vehicle_box_push_request": "", "vehicle_push_request": ""}, "popup": {"message": "", "subtitle": "", "title": ""}, "success": "", "title": ""}, "service-plan": {"popup": {"split": {"success": ""}}}, "service_plan": {"add": {"error": {"nopersonselected": "(de)Select a person"}}, "add_people": "(de)+ Add person", "add_people_mobile": "(de)+ Person", "address": "(de)Address", "armingDelay": "(de)Armament delay", "armingPriority": "(de)Armament priority", "automatic_filling": "(de)Automatic filling", "availability-details-between": "", "availability-details-between-mobile": "", "availability-details-from": "", "availability-details-from-mobile": "", "availability-details-until": "", "availability-details-until-mobile": "", "availability-duration": "", "availability-duration-after": "", "availability-duration-before": "", "backupGroup": "(de)Backup group", "button": {"validate": {"default": "", "professional": "", "volunteer": ""}}, "city": "(de)City", "country": "(de)Country", "day_selector": "{{ date }}", "details": "(de)Service Plan details", "details-link": "(de)+ Details", "entity": "(de)Entity", "exclusive": "", "filter": {"all": "(de)All", "archived": "(de)Archived", "current": "(de)Currents", "future": "(de)Futures"}, "filter-link": "(de)+ Filters", "filter-link-toclose": "(de)- Filtres", "filterTitle": "(de)Filters", "full-availability-long": "", "full-availability-long-mobile": "", "full-availability-short": "", "full-availability-short-mobile": "", "job": {"available": "(de)Available", "available-pro": "", "unaffected": "(de) Unaffected", "unavailable": "(de)Unavailable", "unavailable-prestation": ""}, "legend": {"status": {"barracked": "", "complete": "(de)Complete", "degraded": "(de)Degraded", "empty": "(de)Empty", "incomplete": "(de)Incomplete", "nodata": "(de)No data", "partial": "(de)Partial"}}, "manual_filling": "(de)Manual filling", "manual_synchronization_els": "", "member": {"popup": {"tab": {"header": {"counter": {"tooltip": ""}}}}, "prestations": {"between": "", "from": "", "until": ""}}, "model": "(de)Model", "no_data": "(de)No data available", "no_functions": "", "optionalGroup": "(de)Optional group", "partial_functions": "", "popup": {"add": {"error": {"nopersonselected": "(de)Select a person"}, "professional-prestation": ""}, "delete": {"prestation": {"message": "(de)Do you really want to delete the assignment of <b>{{firstname}} {{lastname}}</b> ?", "subtitle": "(de)of an assignment", "success": "", "title": "(de)Deletion", "with-availability": ""}}, "exclusive": {"message": "", "title": ""}, "export": {"csv": {"message": "", "success": ""}, "pdf": {"message": "", "success": ""}, "send-filling": {"message": "", "success": ""}}, "no_functions": {"message": "", "title": ""}}, "popupTitle": "(de)Service plan", "positions": "(de)Positions in service :", "prestations-availability": "Dispos :", "schedule_selector": {"merge": {"popup": {"message": "", "title": "", "warningmessage": ""}}, "split": {"popup": {"closedVersionName": "The closed version", "message": "", "nextVersionName": "The next version", "splittedVersionName": "The splitted version", "title": ""}}}, "semester_selector": "Semester {{semesterNumber}} {{year}}", "servicePlanType": "(de)Service plan type :", "team": "", "title": "(de)My service Plan", "title_one": "(de)My service Plan", "vehicle": "(de)Vehicle", "versions": {"copy": {"label": "", "message": "", "target": "", "title": ""}, "dates": {"default": "(de)From {{startMonth}}/{{startDay}}/{{startYear}}  to {{endMonth}}/{{endDay}}/{{endYear}}", "from": "(de)From {{startMonth}}/{{startDay}}/{{startYear}}", "label": "(de)Version:", "month": "Mois: {{month}} {{year}}", "year": "Année: {{year}}"}, "empty": "(de)No service plan available", "filling": {"automatic": "(de)Automatic", "manual": "(de)Manual", "title": "(de)Filling mode: "}, "registration": "", "type": {"barracked": "(de)Quartered", "barracked-mobile": "", "not_barracked": "(de)Not quartered", "professional": "(de)Professional", "title": "(de)Type: "}, "vehicle": "(de)Vehicle: "}, "week_selector": "(de)From {{ startDate }} to {{ endDate }}", "zipCode": "(de)Zip code"}, "tooltip": {"add": "", "add-position": "", "audit": "", "availability": "", "closure": "", "copy": "", "delete": "", "edit": "", "export": {"csv": "", "default": "", "pdf-landscape": "", "pdf-new-format": "", "pdf-portrait": "", "prestation": "", "prestations": "", "prestations-detail": "", "prestations-penalty": "", "prestations-penalty-explain": "", "prestations-summary": "", "send-service-plan-filling": ""}, "force-export": "", "info-availabilities-list": "", "logout": "", "management": "", "new": "", "next": "", "previous": "", "refresh": "", "reload": "", "service-plan": {"export": {"prestationspdf": ""}, "merge": "", "more": "", "split": ""}, "validate": ""}, "user-rights": {"permission": {"ROLE_PERMISSION_112_BACKUP": {"description": "", "name": ""}, "ROLE_PERMISSION_112_BACKUP_ACTIVATE": {"description": "", "name": ""}, "ROLE_PERMISSION_112_BACKUP_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_112_BACKUP_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_ALLOWANCE_SETTINGS": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_AUDIT": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_BOX": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_BOX_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_BOX_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_ENTITIES": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_ENTITIES_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_CLOSURE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_CREATE": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_TEAM": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VEHICLE_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PDS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CLOSURE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_OPY": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE_BACKUP": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_COPY": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_SCHEDULER": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_VEHICLES": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_VEHICLES_ID_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_VEHICLES_STATUS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_VEHICLES_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ADMIN_VEHICLES_VIEW_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_AUDIT": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_SEARCH": {"description": "", "name": ""}, "ROLE_PERMISSION_ALLOWANCE_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_CURRENT_SITUATION": {"description": "", "name": ""}, "ROLE_PERMISSION_DASHBOARD_MANAGER": {"description": "", "name": ""}, "ROLE_PERMISSION_DASHBOARD_MEMBER": {"description": "", "name": ""}, "ROLE_PERMISSION_DASHBOARD_MEMBER_WORKING_PLAN": {"description": "", "name": ""}, "ROLE_PERMISSION_GENERATE_API_DESCRIPTION": {"description": "", "name": ""}, "ROLE_PERMISSION_GENERATE_PRESTATIONS_ALLOWANCES": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS": {"description": "", "name": ""}, "ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS": {"description": "", "name": ""}, "ROLE_PERMISSION_IMPERSONATED": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE": {"description": "Autoriser la génération de QR code", "name": "Permission de générer un QR code"}, "ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_PREFERENCES_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_PREFERENCES_VIEW": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_VIEW": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL": {"description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PAGER_UPDATE": {"ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS": {"description": "", "name": ""}, "ROLE_PERMISSION_IMPERSONATED": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE": {"description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_TEAM": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT_PDF": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SEND_FILLING": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ERROR_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW": {"description": "", "name": ""}, "column": "", "dependency": "", "description": "", "entity": "", "name": "", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "ROLE_PERMISSION_PAGER_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_TEAM": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT_PDF": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SEND_FILLING": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_STATE_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PREFERENCES_UPDATE_VEHICLE": {"description": "", "name": ""}, "ROLE_PERMISSION_PROFILE_TIC_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PROFILE_TIC_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ERROR_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VEHICLE_UPDATE_STATUS_6_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW": {"description": "", "name": ""}, "column": "", "dependency": "", "entity": "", "no-permissions": "", "no-search-result": "", "role": {"ROLE_ADMIN": {"description": "", "name": ""}, "ROLE_ASTREINTE_AUDIT": {"description": "", "name": ""}, "ROLE_AUDIT": {"description": "", "name": ""}, "ROLE_CENTER_CHIEF": {"description": "", "name": ""}, "ROLE_CHEF_COMPAGNIE": {"description": "", "name": ""}, "ROLE_DCOCGO_ASSISTANT": {"description": "(de) DCO Assistant CGO", "name": "(de)DCO Assistant CGO"}, "ROLE_DCOCGO_COORDINATOR": {"description": "(de) DCO Coordinateur CGO", "name": "(de) DCO Coordinateur CGO"}, "ROLE_DCOCSU_CHEFDESALLE": {"description": "(de) DCO Chef de Salle CSU", "name": "(de)DCO Chef de Salle CSU"}, "ROLE_DCO_DATA_ELS": {"description": "", "name": ""}, "ROLE_DCO_VOLUNTEER": {"description": "", "name": ""}, "ROLE_DIRECTOR": {"description": "", "name": ""}, "ROLE_DMS_COORDINATOR_SAMU": {"description": "", "name": ""}, "ROLE_HELPDESK": {"description": "", "name": ""}, "ROLE_INFS_SECRETARIAT": {"description": "(de) INFS Secretariat", "name": "(de)INFS Secretariat"}, "ROLE_MANAGEMENT_EXPORT": {"description": "", "name": ""}, "ROLE_MEMBER": {"description": "", "name": ""}, "ROLE_MONITEUR_JEUNES": {"description": "", "name": ""}, "ROLE_PAGER_MANAGEMENT": {"description": "(de) DML Gestionnaire des pagers", "name": "(de) DML Gestionnaire des pagers"}, "ROLE_PERMANENCE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_LIGHT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_VEHICLE_AUDIT": {"description": "", "name": ""}, "ROLE_VEHICLE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_ZONE_CHIEF": {"description": "", "name": ""}, "column": "", "dependency": "", "entity": "", "entityinheritance": "", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "search-permission": "", "select-permission": ""}, "role": {"ROLE_ADMIN": {"description": "", "name": ""}, "ROLE_ASTREINTE_AUDIT": {"description": "", "name": ""}, "ROLE_AUDIT": {"description": "", "name": ""}, "ROLE_CENTER_CHIEF": {"description": "", "name": ""}, "ROLE_CHEF_COMPAGNIE": {"description": "", "name": ""}, "ROLE_DAF_COMPTABILITE": {"description": "", "name": ""}, "ROLE_DCOCGO_ASSISTANT": {"description": "", "name": ""}, "ROLE_DCODCO_ADMINISTRATIF": {"description": "(DE) DCO DCO Administratif", "name": "(DE) DCO DCO Administratif"}, "ROLE_DCOCGO_COORDINATOR": {"description": "", "name": ""}, "ROLE_DCOCSU_CHEFDESALLE": {"description": "", "name": ""}, "ROLE_DCO_DATA_ELS": {"description": "", "name": ""}, "ROLE_DCO_VOLUNTEER": {"description": "", "name": ""}, "ROLE_DIRECTOR": {"description": "", "name": ""}, "ROLE_DMS_COORDINATOR_SAMU": {"description": "", "name": ""}, "ROLE_FO_GEST": {"description": "", "name": ""}, "ROLE_FO_GEST_LIGHT": {"description": "", "name": ""}, "ROLE_HELPDESK": {"description": "", "name": ""}, "ROLE_INFS_SECRETARIAT": {"description": "", "name": ""}, "ROLE_MANAGEMENT_EXPORT": {"description": "", "name": ""}, "ROLE_MEMBER": {"description": "", "name": ""}, "ROLE_MONITEUR_JEUNES": {"description": "", "name": ""}, "ROLE_PAGER_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_LIGHT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_VEHICLE_AUDIT": {"description": "", "name": ""}, "ROLE_VEHICLE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_ZONE_CHIEF": {"description": "", "name": ""}, "column": "", "dependency": "", "entity": "(de)Entity(ies)", "entityinheritance": "", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "vehicles": {"title": "(de)My Vehicles", "type": {"AMB": "(de)Ambulance", "DL": "(de)DL", "FR": "(de)First Responder", "HLF": "(de)HLF", "LF": "(de)LF", "TMF": "(de)TMF"}}, "warnings": {"message": {"VEHICLE_TYPE_UPDATE": ""}}, "working_plan": {"new": {"title": "(de)Working plan creation"}, "title": "(de)Phase 2 - Working plan"}}, "vehicles": {"title": "", "type": {"AMB": "", "DL": "", "FR": "", "HLF": "", "LF": "", "TMF": ""}}, "warning_allowances": "(de)Warning, the allowances amount is based on the prestations until D-1", "warnings": {"message": {"VEHICLE_TYPE_UPDATE": "", "backup-group-unavailable": "", "backup-mode-activated": "", "group-unavailable-for-plans": "", "optional-group-unavailable": "", "pleaserotatedevice": ""}}, "working_plan": {"new": {"title": ""}, "title": ""}, "rici": {"title": "RICI 2.0", "common": {"validate-cancel-buttons": {"label-validate": "Bestätigen", "label-cancel": "Abbrechen"}}, "alert-groups": {"list": {"title:": "Verwaltung der Alarmgruppen", "add-alert-group": "Alarmgruppe hinzufügen", "headers": {"name": "Name der Alarmgruppe", "ric": "RIC Bereich", "status": "Status", "order": "Reihenfolge", "description": "Beschreibung", "actions": "Aktionen", "deletion": "Löschen"}, "tooltips": {"schema": "<PERSON><PERSON><PERSON> bearbeiten", "range": "<PERSON><PERSON><PERSON> bear<PERSON>", "bell-mute": "<PERSON><PERSON><PERSON> stumm", "bell-unmute": "<PERSON><PERSON><PERSON> laut", "person": "Alarmgruppen zuweisen", "audit": "Audit"}, "popups": {"delete": {"title": "Alarmgruppe löschen?", "text": "Löschung der Alarmgruppe {{name}} bestätigen"}}, "add": {"title": "Erstellung von Alarmgruppen", "schema-alias-header": "<PERSON><PERSON><PERSON>", "add-description-placeholder": "+ Beschreibung", "schema-suffix-header": "Suffix", "range-start-header": "Bereichsbeginn", "tooltip-delete": "Alarmgruppe löschen", "tooltip-delete-impossible": "Alarmgruppe kann nicht gelöscht werden", "success-toast": "Alarmgruppe erfolgreich erstellt!"}, "edit": {"title": "Bearbeitung der Alarmgruppen", "order-toast": "Die Reihenfolge wurde erfolgreich geändert"}}}, "pager-assignment-and-groups": {"list": {"title": "Verwaltung der Zuweisung von Pagern und Alarmgruppen", "headers": {"name": "Name", "first-name": "<PERSON><PERSON><PERSON>", "registration": "CGDIS-Matrikelnummer", "pager-id": "PagerID", "update-status": "Update Status", "G-AG-CIS": "G-AG-CIS", "G-INCSA": "G-INCSA", "G-AGT": "G-AGT", "G-CSec": "G-CSec", "G-AG-INCSA": "G-AG-INCSA", "G-FR": "G-FR", "G-AG-SAP": "G-AG-SAP", "G-CADRE": "G-CADRE", "G-TLFW": "G-TLFW", "actions": "Aktionen"}, "popups": {"assignment": {"add-allocation-link": "<PERSON>uweisen +", "add-allocation": "Einen Pager zu<PERSON>sen?", "add-allocation-success": "Pager wurde er<PERSON>lg<PERSON>ich zugewiesen.", "pagers-list-label": "Liste verfügbarer Pager:", "pagers-list-selector-placeholder": "Pager-<PERSON>", "pagers-list-empty": "<PERSON><PERSON> ve<PERSON>üg<PERSON>."}, "edit": {"title": "Zugewiesenen Pager bearbeiten?", "status-label": "Aktueller Pager-Status", "reason-label": "Grund für die Änderung", "reason-placeholder": "Reparaturanfrage", "pager-list-label": "<PERSON>e verfügbarer Pager", "pager-list-placeholder": "Pager-<PERSON>"}}, "actions": {"edit-tooltip": "Pager bearbeiten", "repair-tooltip": "Pager reparieren", "lost-tooltip": "<PERSON>r verloren", "transmit-tooltip": "<PERSON><PERSON>"}, "add-allocation-link": "<PERSON>uweisen +", "add-allocation": "Einen Pager zu<PERSON>sen?", "pagers-list-label": "Liste verfügbarer Pager:", "pagers-list-selector-placeholder": "Pager-<PERSON>", "pagers-list-empty": "<PERSON><PERSON> ve<PERSON>üg<PERSON>.", "assignment-modification-toast-true": "Alarmgruppe wurde erfolgreich hinzugefügt!", "assignment-modification-toast-false": "Alarmgruppe wurde erfolgreich entfernt!"}}, "pagers": {"list": {"title": "Verwaltung der Pager", "headers": {"inactiv-toggle": "Inaktive Pager anzeigen", "pager-id": "Pager-<PERSON>", "serial-number": "Seriennummer", "pager-status": "Pager-Status", "MSISDN": "MSISDN", "assignment": "<PERSON><PERSON><PERSON><PERSON>g", "update-status": "Update Status", "programming-date": "Programmierdatum", "last-update": "Letzte Aktualisierung", "actions": "Aktionen", "pager-assignment-person": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pager-assignment-entity": "Einheit", "pager-assignment-entity-filter": "Einheit auswählen", "pager-assignment-type": "Zuweisungstyp"}, "delete": {"success": "Der Pager wurde erfolgreich gelöscht.", "error": "Beim Löschen des Pagers ist ein Fehler aufgetreten."}, "popup": {"delete": {"title": "Pager lö<PERSON>?", "message": "<PERSON> <b>{{name}}</b> wird <PERSON>.", "message-no-pager-id": "Der Pager ohne angegebene Pager-ID wird gelöscht."}, "upload": {"title": "Pager per CSV importieren", "note": "Bitte wählen Sie eine CSV-Datei zum Importieren aus.", "note-file-columns": "Erwartete Spalten: SERIAL_NUMBER, PAGER_ID (optional), STATUS, MANUFACTURER, MODEL, DELIVERY_DATE, SIM_ICCID (optional)", "mandatoryPagerFieldsLabel": "Pflichtangaben", "ifSimAssociatedMandatoryFieldsLabel": "Bei zugewiesener SIM: Pflichtangaben", "dateFormat": "JJJJ-MM-TT", "button": {"import": "Importieren"}, "loading": "Importiere Pager...", "error": {"no-file-selected": "Keine Datei ausgewählt. Bitte wählen Sie eine CSV-Datei.", "generic": "Beim Importieren der CSV-Datei ist ein Fehler aufgetreten.", "noReportData": "<PERSON><PERSON>n verfügbar."}, "report": {"dialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CSV) – Pager", "title": "Importdetails", "fileName": "Dateiname", "total": "Gesamtzeilen zu verarbeiten", "successful": "Erfolgreich importiert", "validationErrors": "Validierungsfehler", "importErrors": "I<PERSON>rt<PERSON>hler", "errorsDetails": "Fehlerdetails", "line": "<PERSON><PERSON><PERSON>", "rawData": "<PERSON><PERSON><PERSON><PERSON>", "allSuccess": "Alle {{count}} Einträge wurden erfolgreich importiert.", "noErrorsNoSuccess": "<PERSON><PERSON> er<PERSON>, aber keine Einträge erfolgreich importiert.", "emptyFileAfterHeader": "<PERSON>i ist leer oder enthält nur den Header.", "fileLevelError": "Fehler auf Dateiebene (z.Bsp. ungültiger Header):", "noReportData": "<PERSON><PERSON>n verfügbar."}, "expectedColumnsTitle": "Erwartete Spalten in der CSV-Datei", "column": {"serial_number": {"name": "Seriennummer", "description": "Seriennummer des Pagers"}, "pager_id": {"name": "Pager-<PERSON>", "description": "Eindeutige Kennung des Pagers"}, "manufacturer": {"name": "<PERSON><PERSON><PERSON>", "description": "Hersteller des Pagers"}, "model": {"name": "<PERSON><PERSON>", "description": "Modellbezeichnung des Pagers"}, "delivery_date": {"name": "Lieferdatum", "description": "Lieferdatum des Pagers (Format JJJJ-MM-TT)"}, "sim_iccid": {"name": "SIM-Karten-ID (ICCID)", "description": "ICCID der SIM-Karte, die diesem Pager zugewiesen werden soll"}, "sim_msisdn": {"name": "SIM-MSISDN", "description": "MSISDN der SIM-Karte"}, "sim_pin": {"name": "SIM-PIN", "description": "PIN der SIM-Karte"}, "optional": "optional"}, "downloadExample": "Beispielda<PERSON><PERSON> (.csv)"}}, "actions": {"addPagerTooltip": "Neuen Pager hinzufügen", "downloadTooltip": "Pagerliste als CSV exportieren", "downloadTooltipOne": "Diesen Pager als CSV exportieren", "auditTooltip": "<PERSON><PERSON><PERSON><PERSON>", "deleteTooltip": "<PERSON><PERSON>", "deleteWasAssignedTooltip": "Löschen nicht möglich – Zuweisung vorhanden", "deleteDisabledTooltip": "Löschen nicht möglich – Zuweisung vorhanden", "repairTooltip": "Defekten/verlorenen Pager melden", "uploadTooltip": "Pagerliste per CSV importieren"}}, "resetPassword": {"success": "Wartungspasswort erfolgreich zurückgesetzt.", "error": "Fehler beim Zurücksetzen des Wartungspassworts."}, "creation": {"title": "Pager<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "Pager er<PERSON><PERSON><PERSON><PERSON><PERSON> erste<PERSON>."}, "update": {"title": "Pager-<PERSON><PERSON><PERSON><PERSON>", "success": "Pager er<PERSON><PERSON><PERSON><PERSON><PERSON> aktualisiert."}, "details": "Details zum Pager", "auditTooltipDisabled": "Audit ist erst nach Erstellung des Pagers verfügbar.", "pagerId": "Pager-<PERSON>", "pagerIdTooltip": "Die Pager-ID ist bei der Erstellung optional. Sie muss eindeutig, alphanumerisch und 10 Zeichen lang sein. Sie ist er<PERSON><PERSON><PERSON>, um einen Pager zu aktivieren.", "serialNumber": "Seriennummer", "serialNumberTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 14 <PERSON>eichen lang.", "manufacturer": "<PERSON><PERSON><PERSON>", "manufacturerTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>; bestimmt verfügbare Modelloptionen.", "model": "<PERSON><PERSON>", "modelTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>; abhängig vom gewählten Hersteller.", "deliveryDate": "Lieferdatum", "deliveryDateTooltip": "Pflichtfeld. Standardmäßig wird das heutige Datum verwendet.", "maintenancePassword": "Wartungspasswort", "maintenancePasswordTooltip": "Wird bei Erstellung oder Bedarf automatisch vom System generiert.", "resetPasswordTooltipDisabled": "Zurücksetzen nicht beim Erstellen verfügbar.", "pagerStatus": "Pager-Status", "pagerStatusTooltip": "Aktueller Pager-Status (automatisch basierend auf Zuweisung).", "simAllocation": "SIM-Zuweisung – ICCID", "simAllocationTooltip": "Optional. Auswahl nach ICCID oder MSISDN. Nur aktive/verfügbare Karten werden angezeigt. Pflicht zur Aktivierung.", "searchSimPlaceholder": "Nach ICCID suchen...", "simAllocationMsisdn": "SIM-Zuweisung – MSISDN", "simAllocationMsisdnTooltip": "Optional. Auswahl nach ICCID oder MSISDN. Nur aktive/verfügbare Karten werden angezeigt. Pflicht zur Aktivierung.", "searchMsisdnPlaceholder": "Nach MSISDN suchen...", "assignment": "<PERSON><PERSON><PERSON><PERSON>g", "assignmentTooltip": "Standardzuweisung: <PERSON><PERSON>. <PERSON><PERSON><PERSON> (Person, Einheit, Lager, verloren) und Status (deklassiert) des Pagers.", "pagerOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pagerOwnerLink": "+<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simCardLink": "+S<PERSON>-<PERSON><PERSON>", "pagerOwnerTooltip": "<PERSON><PERSON><PERSON>, dem der Pager zugewiesen ist", "searchUserPlaceholder": "Nach Name suchen...", "matricule": "CGDIS-Matrikelnummer", "individualRic": "Individueller RIC", "entity": "Einheit", "change-person-reason": "Grund für die Änderung", "entityTooltip": "<PERSON><PERSON><PERSON>, der der Pager zugewiesen wurde", "searchEntityPlaceholder": "Nach Einheitsnamen suchen...", "auditComingSoon": "Audit-Funk<PERSON> bald verfügbar.", "resetPasswordComingSoon": "Zurücksetzungsfunktion bald verfügbar.", "form": {"programming": {"status": "Pager-<PERSON><PERSON><PERSON>", "status-tooltip": "Pager-Programmierung TT", "programming-date": "Programmierdatum", "last-update": "Letzte Aktualisierung"}, "update": {"toast": "Der Pager wurde erfolgreich aktualisiert."}, "actions": {"transmit": "Übertragen"}}, "status": {"ACTIVE": "Aktiv", "INACTIVE": "Inaktiv", "DEFECTIVE": "Defekt", "LOST": "Unbekannt", "DECOMMISSIONED": "Ausgemustert", "DESTROYED": "Zerstört", "updated": "<PERSON>ktual<PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON><PERSON>", "overdue": "Überfällig"}, "assignmentType": {"PERSON": "<PERSON><PERSON><PERSON>", "STOCK": "Lager", "POOL_CIS": "CGDIS-Pool", "LOST": "Unbekannt", "DESTROYED": "Zerstört", "DECLASSED": "Deklassiert"}, "update-status": {"UP_TO_DATE": "AKTUELL", "PENDING_UPDATE": "AKTUALISIERUNG AUSSTEHEND", "UPDATE_OVERDUE": "AKTUALISIERUNG ÜBERFÄLLIG", "UPDATE_ERROR": "AKTUALISIERUNGSFEHLER", "DEFAULT": "STANDARD"}, "assignment-types": {"PERSON": "PERSON", "STOCK": "LAGER", "POOL_CIS": "POOL_CIS", "LOST": "VERLOREN", "DECLASSED": "DEKLASSIERT"}}, "sim-list": {"title": "Verwaltung der SIM-Karten", "table": {"filters": {"toggle-deactivated-sims": "Deaktivierte SIM-Karten anzeigen"}, "headers": {"iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Status", "pager-id": "Pager-<PERSON>", "operations": "Aktionen"}}, "actions": {"addSimTooltip": "Neue SIM-Karte hinzufügen", "downloadSimTooltip": "SIM-<PERSON><PERSON><PERSON><PERSON>", "uploadSimTooltip": "Liste der SIM-Karten importieren", "deactivate-tooltip": "SIM-Karte deaktivieren", "deactivate-disabled-tooltip": "Deaktivierung nicht möglich.", "delete-tooltip": "SIM-Karte löschen", "delete-disabled-tooltip": "Löschen nicht möglich. Es besteht eine aktuelle oder vergangene Zuweisung.", "pager-details-tooltip": "Pager-Details anzeigen", "history-tooltip": "<PERSON><PERSON><PERSON><PERSON>", "reactivate-tooltip": "SIM-Karte reaktivieren", "reactivate-disabled-tooltip": "Reaktivierung nicht möglich", "last-pager-tooltip": "<PERSON><PERSON><PERSON>wi<PERSON>ne Pager-ID", "active-pager-tooltip": "Aktuell zugewiesene Pager-ID"}, "popup": {"delete": {"title": "Löschen – SIM-Karte", "note": "Bitte bestätigen Sie das Löschen der SIM-Karte mit ICCID: {{iccid}}", "number": "<PERSON><PERSON><PERSON>", "error": {"error": "Fehler beim Erstellen der SIM-Karte.", "no-id": "Löschen nicht möglich, <PERSON><PERSON> nicht gefunden."}, "success": "Die SIM-Karte wurde erfolgreich gelöscht."}, "deactivate": {"title": "Löschen nicht möglich – SIM-Karte deaktivieren?", "confirmation": "Bitte bestätigen Sie die Deaktivierung der SIM-Karte mit ICCID: {{iccid}}", "active-pager-id": "Aktuell zugewiesene Pager-ID", "last-pager-id": "<PERSON><PERSON><PERSON>wi<PERSON>ne Pager-ID", "no-pager-history": "<PERSON><PERSON><PERSON> Pager-ID: <PERSON><PERSON>", "error": {"error": "Fehler bei der Deaktivierung der SIM-Karte.", "no-id": "Deaktivierung nicht möglich, Karte nicht gefunden."}, "success": "Die SIM-Karte wurde erfolgreich deaktiviert."}, "reactivate": {"title": "SIM-Karte reaktivieren?", "confirmation": "Bitte bestätigen Sie die Reaktivierung der SIM-Karte mit ICCID: {{iccid}}", "last-pager-id": "<PERSON><PERSON><PERSON>wi<PERSON>ne Pager-ID", "no-pager-history": "<PERSON><PERSON><PERSON> Pager-ID: <PERSON><PERSON>", "error": {"error": "Fehler bei der Reaktivierung der SIM-Karte.", "no-id": "Reaktivierung nicht möglich, Karte nicht gefunden."}, "success": "Die SIM-Karte wurde erfolgreich reaktiviert."}, "upload": {"title": "CSV-Import – SIM-Karten", "note": "Bitte wählen Sie eine CSV-Datei zum Importieren aus.", "note-file-columns": "Die Datei muss folgende Spalten enthalten: ICCID, MSISDN, PIN, SIM_STATUS und optional PAGER_ID, LINK_STATUS, LINK_START_DATETIME, LINK_END_DATETIME.", "loading": "Import der SIM-Karten läuft...", "noSimsImported": "<PERSON><PERSON>-Karte wurde importiert.", "mandatoryFieldsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "optionalFieldsLabel": "Bei zugewiesenem Pager: erforder<PERSON> Daten", "expectedColumnsTitle": "Erwartete Spalten in der CSV-Datei", "column": {"iccid": {"name": "SIM-<PERSON><PERSON><PERSON> (ICCID)", "description": "Identifikationsnummer der SIM-Karte (ICCID)"}, "msisdn": {"name": "MSISDN-<PERSON><PERSON>mer", "description": "MSISDN-Nummer der SIM-Karte"}, "pin": {"name": "PIN-Code", "description": "PIN-Code der SIM-Karte"}, "pager_id": {"name": "Pager-<PERSON>", "description": "ID des Pagers, dem diese SIM-Karte zugewiesen werden soll"}, "pager_serial_number": {"name": "Seriennummer des Pagers", "description": "Seriennummer des Pagers, dem diese SIM-Karte zugewiesen werden soll"}, "optional": "optional"}, "downloadExample": "Beispielda<PERSON><PERSON> (.csv)", "error": {"no-report-data": "<PERSON><PERSON> verfügbar.", "server-processing": "Fehler bei der Verarbeitung der Datei auf dem Server. Bitte Bericht prüfen.", "no-file-selected": "Bitte eine Datei auswählen.", "generic": "Fehler beim Import der CSV-Datei mit SIM-Karten.", "noReportData": "<PERSON><PERSON> verfügbar."}, "button": {"import": "Importieren"}, "success": {"generic": "Import erfolgreich abgeschlossen.", "partial": "{{success}} von {{total}} SIM-Karte(n) erfolgreich importiert. Einige Zeilen enthalten Fehler."}, "report": {"dialogTitle": "CSV-Importbericht – SIM-Karten", "title": "Importdetails", "fileName": "Dateiname", "total": "Gesamtzeilen zu verarbeiten", "successful": "Erfolgreich", "validationErrors": "Validierungsfehler", "importErrors": "I<PERSON>rt<PERSON>hler", "errorsDetails": "Fehlerdetails", "line": "<PERSON><PERSON><PERSON>", "rawData": "<PERSON><PERSON><PERSON><PERSON>", "allSuccess": "{{count}} SIM-Karte(n) erfolgreich importiert.", "noErrorsNoSuccess": "<PERSON><PERSON> er<PERSON>, aber auch keine SIM-Karten erfolgreich importiert.", "emptyFileAfterHeader": "Datei ist leer nach dem Header. <PERSON>ine Datenzeilen gefunden.", "fileLevelError": "<PERSON>hler auf Dateiebene:", "containsErrors": "Der Import enthält Fehler. Bitte prüfen Sie den Bericht."}}, "download": {"title": "CSV-Export – SIM-Karten", "note": "Bitte den zu exportierenden Dateinamen bestätigen"}}}, "schemas": {"list": {"title": "Verwaltung der RIC-Schemata", "headers": {"name": "Name des RIC-Schemas", "alias": "<PERSON><PERSON>", "description": "Beschreibung", "a-fct": "A-Funktion", "b-fct": "B-Funktion", "c-fct": "C-Funktion", "d-fct": "D-Funktion", "actions": "Aktionen"}, "delete": {"tooltip": "Löschen des RIC-Schemas nicht möglich. Das Schema wird von folgenden Entitäten verwendet: ...", "popup": {"title": "RIC-<PERSON><PERSON><PERSON>?", "message": "<PERSON> RIC-Schema <b>{{name}}</b> wird <PERSON>."}, "toast": "Das RIC-Schema wurde erfolgreich gelöscht."}}, "form": {"add": {"title": "RIC-<PERSON><PERSON><PERSON>", "toast": "Das RIC-<PERSON><PERSON><PERSON> wurde erfolgreich erstellt."}, "edit": {"title": "RIC-<PERSON><PERSON><PERSON> bear<PERSON>", "toast": "Das RIC-Schema wurde erfolgreich aktualisiert."}, "schema": {"title": "Details des RIC-Schemas", "audit": "+<PERSON><PERSON>", "name": "Name des RIC-Schemas", "name-tooltip": "Name des RIC-Schemas", "alias": "<PERSON><PERSON>", "alias-tooltip": "<PERSON><PERSON>", "suffix": "RIC-Suffix", "suffix-tooltip": "RIC-Suffix", "description": "Beschreibung", "description-tooltip": "Beschreibung"}, "functions": {"title": "Funktionsdetails des RIC-Schemas", "title-tooltip": "Funktionsdetails des RIC-Schemas", "a-fct": "A-Funktion", "b-fct": "B-Funktion", "c-fct": "C-Funktion", "d-fct": "D-Funktion", "suffix": "RIC-Suffix", "description": "Beschreibung", "suffix-placeholder": "Suffix", "tone-placeholder": "Ton", "model-placeholder": "<PERSON><PERSON>", "color-placeholder": "Farbe", "codes": {"tones": {"TONE_1": "1", "TONE_2": "2", "TONE_3": "3", "TONE_4": "4", "TONE_5": "5"}, "models": {"MODEL_1": "1", "MODEL_2": "2", "MODEL_3": "3", "MODEL_4": "4", "MODEL_5": "5"}, "colors": {"RED": "rot", "WHITE": "weiß", "YELLOW": "gelb", "GREEN": "gr<PERSON>n"}}}}}, "ranges": {"popup": {"delete": {"title": "RIC-Bereich löschen?", "message": "Der RIC-Bereich <b>{{name}}</b> wird <PERSON>."}}, "list": {"title": "Verwaltung der RIC-Bereiche", "tooltip": {"delete": "Löschen nicht möglich. RIC-Bereich zugewiesen an "}, "headers": {"name": "Name des RIC-Bereichs", "type": "Bereichstyp", "start": "Bereichsbeginn", "end": "Bereichsende", "allocation": "<PERSON><PERSON><PERSON><PERSON>g", "rate": "Auslastungsrate", "actions": "Aktionen"}, "filters": {"allocated": "Nur zugewiesene Bereiche anzeigen"}}, "add": {"title": "Erstellung eines RIC-Bereichs"}, "edit": {"title": "Bearbeitung eines RIC-Bereichs"}, "single": {"title": "RIC-Bereich"}, "types": {"special": "Spezial", "standard": "Standard", "system": "System"}, "forms": {"delete": {"toast": "Der RIC-Bereich wurde erfolgreich gelöscht."}, "update-form": {"name": "Name", "tooltip-name": "Wird noch definiert", "type": "<PERSON><PERSON>", "tooltip-type": "Wird noch definiert", "allocation": "<PERSON><PERSON><PERSON><PERSON>g", "tooltip-allocation": "Wird noch definiert", "rangeStart": "Bereichsbeginn", "tooltip-rangeStart": "Wird noch definiert", "rangeEnd": "Bereichsende", "tooltip-rangeEnd": "Wird noch definiert", "toast": "Der RIC-Bereich wurde erfolgreich aktualisiert."}, "create-form": {"name": "Name", "tooltip-name": "Wird noch definiert", "type": "<PERSON><PERSON>", "tooltip-type": "Wird noch definiert", "allocation": "<PERSON><PERSON><PERSON><PERSON>g", "tooltip-allocation": "Wird noch definiert", "rangeStart": "Bereichsbeginn", "tooltip-rangeStart": "Wird noch definiert", "rangeEnd": "Bereichsende", "tooltip-rangeEnd": "Wird noch definiert", "toast": "Der RIC-Bereich wurde erfolgreich erstellt."}}}, "sim": {"title": "Erstellung einer SIM-Karte", "details": "Details zur SIM-Karte", "associated-pager-info-subtitle": "<PERSON>ugewi<PERSON>ner Pager", "associated-pager-info-no-pager": "Derzeit ist kein Pager zugewiesen.", "iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Status", "pagerId": "PagerID", "serialNumber": "Seriennummer", "pager-owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "matricule-cgdis": "CGDIS-Matrikelnummer", "iccidTooltip": "ICCID = Integrated Circuit Card Identifier. Er ist verpflichtend, eindeutig, numerisch und zwischen 16 und 20 Zeichen lang.", "msisdnTooltip": "MSISDN = Mobile Subscriber ISDN Number. Er ist verpflichtend, eindeutig, numerisch und maximal 15 Zeichen lang.", "pinTooltip": "PIN = Persönliche Identifikationsnummer. Sie ist verpflichtend, numerisch und 4 bis 8 Zeichen lang.", "statusTooltip": "STATUS = Status der SIM-Karte. Er ist verpflichtend und unterliegt fachlichen Regeln.", "auditLink": "+<PERSON><PERSON>", "auditComingSoon": "Audit – demnächst verfügbar", "historiqueStatutLink": "+<PERSON><PERSON><PERSON><PERSON>", "pager-details": "+Details", "add-pager": "+Pager", "status-history": {"title": "<PERSON><PERSON><PERSON><PERSON>", "no-data": "<PERSON><PERSON> Daten gefunden", "date": "Datum der Änderung", "status": "Status"}, "statuses": {"disponible": "Verfügbar", "associee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inactive": "Inaktiv"}, "pager-details-coming-soon": "Pager-Details – demnächst verfügbar", "historiqueStatutComingSoon": "Statusverlauf – demnächst verfügbar", "createNewSimCard": "Erstellung einer neuen SIM-Karte", "creation": {"success": "Die SIM-Karte wurde erfolgreich erstellt.", "error": "<PERSON><PERSON>n der SIM-Karte ist ein Fehler aufgetreten."}, "edit": {"title": "Bearbeitung einer SIM-Karte", "success": "Die SIM-Karte wurde erfolgreich bearbeitet.", "error": "<PERSON>im <PERSON> der SIM-Karte ist ein Fehler aufgetreten.", "no-pager-associated": "<PERSON><PERSON>."}}, "pagerAssociation": {"title": "Neue SIM-Karte mit einem Pager verknüpfen?", "subtitle": "Bitte wählen Sie den Pager aus", "success": "Die SIM-Karte wurde erfolgreich mit dem Pager verknüpft.", "error": "<PERSON>im Verknüpfen der SIM-Karte mit dem Pager ist ein Fehler aufgetreten.", "noPagersAvailable": "<PERSON><PERSON> für die Verknüpfung verfügbar."}}}