import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { environment } from '@env/environment';

@Component({
  selector: 'cgdis-portal-app-switch',
  templateUrl: './app-switch.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppSwitchComponent implements OnInit {
  mobile = environment.application === 'mobile';

  constructor() {}

  ngOnInit() {}
}
