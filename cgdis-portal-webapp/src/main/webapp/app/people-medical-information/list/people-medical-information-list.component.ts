import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { PeopleMedicalInformationListService } from '@app/people-medical-information/list/people-medical-information-list.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { Entity } from '@app/model/entity.model';
import { UntypedFormControl } from '@angular/forms';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { EntityService } from '@app/common/shared/services/entity.service';
import { DateModel, DateService } from '@eportal/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { AptitudesInformationFilterService } from '@app/general-information/aptitudes-information/aptitudes-information-filter.service';
import { RestrictionInformationFilterService } from '@app/general-information/restrictions-information/restriction-information-filter.service';
import { PersonMedicalInformation } from '@app/model/person/person-medical-information.model';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { Subscription } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { LegendItem } from '@app/common/modules/legend/legend-item';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

import { AptitudeStatutFilter } from '@app/model/person/aptitude-statut.filter';
import { PersonAptitude } from '@app/model/person/person-aptitude.model';
import { LandscapeBlockerModes } from '@app/model/landscape-blocker-modes.model';

@Component({
  selector: 'cgdis-portal-admin-people-medical-information-list',
  templateUrl: './people-medical-information-list.component.html',
  styleUrls: ['../people-medical-information.component.scss'],
  providers: [PeopleMedicalInformationListService],
})
export class PeopleMedicalInformationListComponent
  implements OnInit, OnDestroy
{
  landscapeBlockerModes = LandscapeBlockerModes;

  /**
   * The selected entity
   */
  selectedEntity: Entity = null;

  formControl = new UntypedFormControl();

  formControlAptitudeStatuts = new UntypedFormControl();

  formControlAptitudes = new UntypedFormControl();

  formControlRestrictions = new UntypedFormControl();

  formControlStatus = new UntypedFormControl();

  formControlToggle = new UntypedFormControl();

  formControlTechnicalToggle = new UntypedFormControl();

  suspensionControlToggle = new UntypedFormControl();

  endDateFormControl: UntypedFormControl = new UntypedFormControl();

  filterConfigEntityEqual = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  filterConfigLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });

  filterConfigAllPersons = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });

  startDateFilterConfig = new FilterConfig({ operator: SearchOperator.ge });

  endDateFilterConfig = new FilterConfig({ operator: SearchOperator.le });

  aptitudes: FieldGroupOption<string, string>[] = [];
  restrictions: FieldGroupOption<string, string>[] = [];
  aptitudeStatuts: FieldGroupOption<string, string>[] = [];
  statuses: FieldGroupOption<string, string>[] = [];
  statusesTranslations: any;
  startDateFormat = 'DD/MM/YYYY';
  endDateFormat = 'MM/YYYY';

  public onlyYoungFirefighter = false;
  public hasTechnicalAssignment = false;

  peopleMedicalInformationList: PersonMedicalInformation[];
  peopleMedicalInformationListLength: number;

  mobileLoading: boolean = true;

  legendItems: LegendItem[];

  isMobile: boolean = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private entitiesService: EntityService,
    private dateService: DateService,
    private aptitudesInformationFilterService: AptitudesInformationFilterService,
    private restrictionInformationFilterService: RestrictionInformationFilterService,
    private connectedUserService: ConnectedUserService,
    private translateService: TranslateService,
    public peopleMedicalInformationListService: PeopleMedicalInformationListService,
    private assgnmentService: AssignmentService,
    private cd: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 768px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.startDateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
          this.endDateFormat = this.isMobile ? 'MM/YY' : 'MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'general_information.medical-information.legend.apt',
        classes: ['people-aptitude-badge people-apt'],
      }),
      new LegendItem({
        id: '2',
        labelKey:
          'general_information.medical-information.legend.apt_with_restriction',
        classes: ['people-aptitude-badge people-apt-with-restriction'],
      }),
      new LegendItem({
        id: '3',
        labelKey:
          'general_information.medical-information.legend.temporary_inaptitude',
        classes: ['people-aptitude-badge people-temporary-inapt'],
      }),
      new LegendItem({
        id: '4',
        labelKey: 'general_information.medical-information.legend.inapt',
        classes: ['people-aptitude-badge people-inapt'],
      }),
    ];

    this.subscriptions.push(
      this.peopleMedicalInformationListService
        .getResults()
        .subscribe((result: any) => {
          this.peopleMedicalInformationList = result.content;
          this.peopleMedicalInformationListLength = result.totalElements;
          this.mobileLoading = false;
          this.cd.markForCheck();
        }),
    );

    this.onlyYoungFirefighter =
      this.connectedUserService.hasAnyRoles([
        'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER',
      ]) &&
      !this.connectedUserService.hasAnyRoles([
        'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL',
      ]);

    this.statuses = [
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_retired',
        ),
        value: 'retired',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_young_firefighter',
        ),
        value: 'youngFirefighter',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_veteran',
        ),
        value: 'veteran',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_professional_operational',
        ),
        value: 'professionalOperational',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_professional_adm_tech',
        ),
        value: 'professionalAdmTech',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_intern',
        ),
        value: 'intern',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_candidate',
        ),
        value: 'candidate',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_operational_firefighter',
        ),
        value: 'operationalFirefighter',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_support_firefighter',
        ),
        value: 'supportFirefighter',
      }),
      new FieldOption({
        label: this.translateService.instant(
          'general_information.personal_information.is_samu',
        ),
        value: 'samu',
      }),
    ];
    this.statusesTranslations = {
      retired: 'general_information.personal_information.is_retired',
      youngFirefighter:
        'general_information.personal_information.is_young_firefighter_short',
      veteran: 'general_information.personal_information.is_veteran',
      professionalOperational:
        'general_information.personal_information.is_professional_operational_short',
      professionalAdmTech:
        'general_information.personal_information.is_professional_adm_tech_short',
      intern: 'general_information.personal_information.is_intern',
      candidate: 'general_information.personal_information.is_candidate',
      operationalFirefighter:
        'general_information.personal_information.is_operational_firefighter_short',
      supportFirefighter:
        'general_information.personal_information.is_support_firefighter_short',
      samu: 'general_information.personal_information.is_samu',
    };
    this.statuses.sort((a, b) => a.label.localeCompare(b.label));
    this.formControlStatus.setValue([
      'professionalOperational',
      'operationalFirefighter',
    ]);
    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  setFilter(event: any): void {
    this.formControlToggle.setValue(event.allUnderEntity, { emitEvent: false });
    this.setEntity(event.entity);
  }

  /**
   * Set selected entity into form control
   * @param entity: the selected entity
   */
  setEntity(entity: Entity): void {
    if (entity == null) {
      this.formControl.setValue(null);
    } else {
      this.formControl.setValue(entity.tecid);
    }

    this.selectedEntity = entity;
    this.updateFilter(true);
    this.cd.markForCheck();
  }

  public updateFilter(changeEntity: boolean): void {
    /*this.formControlAptitudes.setValue(null);
    this.formControlRestrictions.setValue(null);
    this.formControlAptitudeStatuts.setValue(null);
    this.formControlStatus.setValue(['professionalOperational','operationalFirefighter']);*/

    let subentities = this.formControlToggle.value === true;
    if (changeEntity) {
      this.formControlTechnicalToggle.setValue(false);
      if (this.selectedEntity.tecid != null) {
        this.assgnmentService
          .existTechnicalAssignments(this.selectedEntity.tecid, subentities)
          .subscribe((exist) => {
            this.hasTechnicalAssignment = exist;
            this.cd.markForCheck();
          });
      }
    }

    let technical = this.formControlTechnicalToggle.value === true;
    // Backend calls to retrieve options of the fields "aptitudes" and "restrictions"
    if (this.selectedEntity.tecid != null) {
      this.assgnmentService
        .getAllStatuesForEntity(
          this.selectedEntity.tecid,
          subentities,
          technical,
        )
        .subscribe((allStatus) => {
          this.statuses = [];

          if (!this.onlyYoungFirefighter && allStatus.candidate) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_candidate',
                ),
                value: 'candidate',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.intern) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_intern',
                ),
                value: 'intern',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.operationalFirefighter) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_operational_firefighter',
                ),
                value: 'operationalFirefighter',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.professionalAdmTech) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_professional_adm_tech',
                ),
                value: 'professionalAdmTech',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.professionalOperational) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_professional_operational',
                ),
                value: 'professionalOperational',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.retired) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_retired',
                ),
                value: 'retired',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.samu) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_samu',
                ),
                value: 'samu',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.supportFirefighter) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_support_firefighter',
                ),
                value: 'supportFirefighter',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.veteran) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_veteran',
                ),
                value: 'veteran',
              }),
            );
          }
          if (allStatus.youngFirefighter) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_young_firefighter',
                ),
                value: 'youngFirefighter',
              }),
            );
          }
          this.formControlStatus.setValue([
            'professionalOperational',
            'operationalFirefighter',
          ]);
          this.cd.markForCheck();
        });
      this.aptitudesInformationFilterService
        .getAllAptitudeDetails()
        .subscribe((types) => {
          this.aptitudes = _.map(types, (element) => {
            if (element.externalId.includes('CONCLU')) {
              return new FieldGroupOption({
                value: element.externalId,
                I18NLabel: element.name,
              });
            } else {
              return null;
            }
          });
          this.aptitudes = this.aptitudes.filter((element) => element !== null);
        });

      this.aptitudes.sort((a, b) => a.I18NLabel.localeCompare(b.I18NLabel));
      this.restrictionInformationFilterService
        .getAllRestrictions()
        .subscribe((types) => {
          this.restrictions = _.map(types, (element) => {
            return new FieldGroupOption({
              value: element.externalId,
              I18NLabel: element.description,
            });
          });
        });
      this.restrictions.sort((a, b) => a.I18NLabel.localeCompare(b.I18NLabel));

      this.aptitudeStatuts = [];
      // Aptitude statut filter options are set manually.
      this.aptitudeStatuts.push(
        new FieldGroupOption({
          I18NLabel: 'people_medical_information.functions.apt',
          value: AptitudeStatutFilter.APTE,
        }),
      );
      this.aptitudeStatuts.push(
        new FieldGroupOption({
          I18NLabel:
            'people_medical_information.functions.apt_with_restriction',
          value: AptitudeStatutFilter.APTE_WITH_RESTRICTION,
        }),
      );
      this.aptitudeStatuts.push(
        new FieldGroupOption({
          I18NLabel: 'people_medical_information.functions.inapt',
          value: AptitudeStatutFilter.INAPTE,
        }),
      );
      this.aptitudeStatuts.push(
        new FieldGroupOption({
          I18NLabel: 'people_medical_information.functions.temporary_inapt',
          value: AptitudeStatutFilter.TEMPORARY_INAPTE,
        }),
      );
      this.aptitudeStatuts.push(
        new FieldGroupOption({
          I18NLabel: 'people_medical_information.functions.expired',
          value: AptitudeStatutFilter.EXPIRED,
        }),
      );
      this.aptitudeStatuts.push(
        new FieldGroupOption({
          I18NLabel: 'people_medical_information.functions.without_aptitudes',
          value: AptitudeStatutFilter.WITHOUT_APTITUDE,
        }),
      );
    } else {
      this.aptitudes = [];
      this.restrictions = [];
      this.aptitudeStatuts = [];
      this.statuses = [];
    }
    this.cd.markForCheck();
  }

  public getFormattedFullDateSpan(aptitude: PersonAptitude): string {
    let startDate = aptitude.startDate;
    let endDate = aptitude.endDate;

    let span;

    if (startDate != null) {
      if (startDate.year <= 1900) {
        return this.translateService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      span = [this.dateService.format(startDate, this.startDateFormat)];
    }

    if (endDate != null) {
      if (endDate.year <= 1900) {
        return this.translateService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      span = span
        .concat(this.dateService.format(endDate, this.endDateFormat))
        .join(' - ');
    }

    return span.toString();
  }

  public getFormattedStartDate(date: DateModel): string {
    if (date != null) {
      if (date.year <= 1900) {
        return this.translateService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.startDateFormat);
    } else {
      return '-';
    }
  }

  public getFormattedEndDate(date: DateModel): string {
    if (date != null) {
      if (date.year >= 2200) {
        return this.translateService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.endDateFormat);
    } else {
      return '-';
    }
  }

  public isExpired(date: DateModel): boolean {
    let firstDayOfCurrentMonth = this.dateService.firstDayOfMonth();
    let firstDayOfMonth = new DateModel({
      year: date.year,
      month: date.month,
      day: 1,
    });
    return this.dateService.isAfter(firstDayOfCurrentMonth, firstDayOfMonth);
  }

  public computeRowClasses(row: PersonMedicalInformation) {
    if (row.warning) {
      return {
        'container-warn': true,
      };
    }
    return;
  }

  public goToPage($event: PageEvent) {
    this.peopleMedicalInformationListService.updateDataSource(
      $event.pageSize,
      $event.pageIndex,
    );
  }
}
