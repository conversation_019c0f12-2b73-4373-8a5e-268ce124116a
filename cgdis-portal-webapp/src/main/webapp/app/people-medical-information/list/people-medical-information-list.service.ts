import { Location } from '@angular/common';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { Injectable } from '@angular/core';
import { PersonMedicalInformation } from '@app/model/person/person-medical-information.model';

@Injectable()
export class PeopleMedicalInformationListService extends CgdisDatatableService<PersonMedicalInformation> {
  constructor(
    public _restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      _restService.all('person', 'assignment', 'logas', 'medical-information'),
    );
  }

  /**
   * Set the data source
   * @param pageSize
   * @param pageNumber
   */
  public updateDataSource(pageSize: number, pageNumber: number): void {
    this.pageSize = pageSize;
    this.currentPage = pageNumber;
    super.setDataResource(
      this._restService.all(
        'person',
        'assignment',
        'logas',
        'medical-information',
      ),
    );
  }
}
