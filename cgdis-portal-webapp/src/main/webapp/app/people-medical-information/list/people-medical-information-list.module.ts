import { PageTemplateModule } from '../../common/template/page-template/page-template.module';
import { SharedModule } from '../../common/shared/shared.module';
import { NgModule } from '@angular/core';
import { SimplePopupModule } from '../../common/modules/popup/simple-popup.module';
import { FormsModule } from '@angular/forms';
import { NgxSelectModule } from 'ngx-select-ex';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { EntityService } from '../../common/shared/services/entity.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PeopleMedicalInformationListComponent } from '@app/people-medical-information/list/people-medical-information-list.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { PeopleMedicalInformationResultMobileModule } from '@app/people-medical-information/people-medical-information-result-mobile/people-medical-information-result-mobile.module';
import { PeopleMedicalInformationSelectorMobileModule } from '@app/people-medical-information/people-medical-information-selector-mobile/people-medical-information-selector-mobile.module';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MobileExpansionPanelModule } from '@app/common/template/mobile-expansion-panel/mobile-expansion-panel.module';
import { LegendModule } from '@app/common/modules/legend/legend.module';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    NgxSelectModule,
    FormsModule,
    DatatableModule,
    SimplePopupModule,
    EpDatatableModule,
    MatTooltipModule,
    MatExpansionModule,
    PeopleMedicalInformationResultMobileModule,
    PeopleMedicalInformationSelectorMobileModule,
    MatPaginatorModule,
    MobileExpansionPanelModule,
    LegendModule,
    FormModule,
    EntityFilterModule,
  ],
  providers: [EntityService],
  declarations: [PeopleMedicalInformationListComponent],
})
export class PeopleMedicalInformationListModule {}
