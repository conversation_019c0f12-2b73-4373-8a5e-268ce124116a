<ng-container class="people-management">
  <cgdis-portal-page-template cgdisPortalLandscapeBlocker [landscapeBlockerMode]="landscapeBlockerModes.BLOCKONTABLETONLY">

    <!-- Title and entity filter   -->
    <cgdis-portal-page-header [titleKey]="'layout.navigation.menu.items.people_medical_information.list.title'"
                              [subtitleAlign]="true">
      <div page-header-subtitle class="flex-row" >
        <!-- Entity filter -->
        <cgdis-portal-entity-filter [entityPermission]="['ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST']" [initPrimaryEntity]="true" (filterSelected)="setFilter($event)"></cgdis-portal-entity-filter>
      </div>
    </cgdis-portal-page-header>

    <div *ngIf="!isMobile && hasTechnicalAssignment"  class="row">
      <div class="col-md-12" >
        <cgdis-portal-datatable-toggle-filter
          [labelKey]="'people_management.functions.technical'"
          [filterName]="'withTechnical'"
          [filterConfig]="filterConfigAllPersons"
          [datatableService]="peopleMedicalInformationListService"
          [customFormControl]="formControlTechnicalToggle"
          (onValueChanged)="updateFilter(false)"

        ></cgdis-portal-datatable-toggle-filter>
      </div>
    </div>

    <!--  Filter bar  -->
    <mat-accordion>
      <mat-expansion-panel class="accordion__group p-0" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'people_medical_information.functions.toggle_filters'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body filter-bar">
            <div class="row">
              <div class="aptitude-filter-bar col-xl-4 col-lg-4 col-md-6 col-12 my-4">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label [translate]="'people_medical_information.functions.aptitude'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-select-filter [possibleValues]="aptitudes"
                                                          [filterName]="'aptitudes'"
                                                          [datatableService]="peopleMedicalInformationListService"
                                                          [allowClear]="true"
                                                          [customFormControl]="formControlAptitudes"
                    ></cgdis-portal-datatable-select-filter>
                  </div>
                </div>
              </div>

              <div class="aptitude-end-filter-bar col-xl-4 col-lg-3 col-md-6 col-12 my-4">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label [translate]="'people_medical_information.functions.validity_until'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-datepicker-filter
                      class="activity-datepicker-filter"
                      [customFormControl]="endDateFormControl"
                      [filterName]="'aptitudeEndDateTime'"
                      [filterConfig]="endDateFilterConfig"
                      [datatableService]="peopleMedicalInformationListService"
                      [placeholder]="''"
                    >
                    </cgdis-portal-datatable-datepicker-filter>
                  </div>
                </div>
              </div>
              <div class="restriction-filter-bar col-xl-4 col-lg-4 col-md-6 col-12 my-4">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label
                      [translate]="isMobile ? 'people_medical_information.functions.restrictionsmobile' : 'people_medical_information.functions.restrictions'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-select-filter [possibleValues]="restrictions"
                                                          [filterName]="'restrictions'"
                                                          [datatableService]="peopleMedicalInformationListService"
                                                          [allowClear]="true"
                                                          [customFormControl]="formControlRestrictions"
                    ></cgdis-portal-datatable-select-filter>
                  </div>
                </div>
              </div>
              <div class="statut-filter-bar col-xl-4 col-lg-3 col-md-6 col-12 my-4">
                <div class="row">
                  <div class="col-sm-12 col-12 filter-bar-header">
                    <label [translate]="'people_medical_information.functions.status'"></label>
                  </div>
                  <div class="col-sm-12 col-12 filter-bar-fields pt-2">
                    <cgdis-portal-datatable-checkbox-group-filter [possibleValues]="aptitudeStatuts"
                                                                  [filterName]="'aptitudeStatut'"
                                                                  [datatableService]="peopleMedicalInformationListService"
                                                                  [customFormControl]="formControlAptitudeStatuts"
                    ></cgdis-portal-datatable-checkbox-group-filter>
                  </div>
                </div>
              </div>
              <div class="restriction-filter-bar col-xl-2 col-lg-3 col-md-6 col-12 my-4" *ngIf="isMobile">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label [translate]="'people_medical_information.functions.firstname'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-text-filter [filterName]="'firstName'"
                                                        [placeholder]="''"
                                                        [allowClear]="true"
                                                        [filterConfig]="filterConfigLike"
                                                        [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-text-filter>
                  </div>
                </div>
              </div>
              <div class="restriction-filter-bar col-xl-2 col-lg-3 col-md-6 col-12 my-4" *ngIf="isMobile">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label [translate]="'people_medical_information.functions.lastname'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-text-filter [filterName]="'lastName'"
                                                        [placeholder]="''"
                                                        [allowClear]="true"
                                                        [filterConfig]="filterConfigLike"
                                                        [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-text-filter>
                  </div>
                </div>
              </div>
              <div class="restriction-filter-bar col-xl-4 col-lg-4 col-md-6 col-12 my-4">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label [translate]="'people_management.functions.status'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields medical-multi-select">
                    <cgdis-portal-datatable-select-filter
                      [possibleValues]="statuses"
                      [filterName]="'status'"
                      [flexGrow]="2"
                      [datatableService]="peopleMedicalInformationListService"
                      [allowClear]="true"
                      [multiple]="true"
                      [customFormControl]="formControlStatus"
                    ></cgdis-portal-datatable-select-filter>
                  </div>
                </div>
              </div>
              <div class="restriction-filter-bar col-xl-4 col-lg-4 col-md-6 col-12 my-4" *ngIf="isMobile">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label
                      [translate]="isMobile ? 'people_medical_information.functions.cgdisregistrationnumbermobile' : 'people_medical_information.functions.cgdisregistrationnumber'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-text-filter [filterName]="'cgdisRegistrationNumber'"
                                                        [placeholder]="''"
                                                        [allowClear]="true"
                                                        [filterConfig]="filterConfigLike"
                                                        [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-text-filter>
                  </div>
                </div>
              </div>
              <div class="restriction-filter-bar col-xl-4 col-lg-5 col-md-7 col-12 my-4">
                <div class="row">
                  <div class="col-12 filter-bar-header">
                    <label
                      [translate]="'people_medical_information.functions.suspensions'"></label>
                  </div>
                  <div class="col-12 filter-bar-fields">
                    <cgdis-portal-datatable-toggle-filter
                      [labelKey]="'people_medical_information.functions.suspensionToggle'"
                      [filterName]="'withoutSuspension'"
                      [filterConfig]="filterConfigAllPersons"
                      [datatableService]="peopleMedicalInformationListService"
                      [customFormControl]="suspensionControlToggle"
                    ></cgdis-portal-datatable-toggle-filter>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>
    </mat-accordion>

    <!--  Persons table  -->
    <cgdis-portal-panel *ngIf="selectedEntity != undefined; else noEntities">

      <div class="panel" *ngIf="isMobile">
        <cgdis-portal-spinner [loading]="mobileLoading"></cgdis-portal-spinner>
        <div *ngIf="!mobileLoading">
          <cgdis-portal-legend [items]="legendItems" [legendClasses]="['-inline']"></cgdis-portal-legend>

          <cgdis-portal-mobile-expansion-panel [data]="peopleMedicalInformationList" [hasPaginator]="true"
                                               [pageLength]="peopleMedicalInformationListLength"
                                               [service]="peopleMedicalInformationListService"
                                               [templateRefHeader]="templateRefHeader"
                                               [templateRefBody]="templateRefBody">
            <ng-template let-oneData="oneData" #templateRefHeader>
              <cgdis-portal-people-medical-information-selector-mobile
                [person]="oneData"></cgdis-portal-people-medical-information-selector-mobile>
            </ng-template>
            <ng-template let-oneData="oneData" #templateRefBody>
              <cgdis-portal-people-medical-information-result-mobile [id]="'member-medical-information-list'"
                                                                     [person]="oneData"></cgdis-portal-people-medical-information-result-mobile>
            </ng-template>
          </cgdis-portal-mobile-expansion-panel>
        </div>


      </div>


      <div [ngStyle]="{'display': isMobile ? 'none' : ''}" class="localSpinner entity-list">
        <ng-container>
          <cgdis-portal-cgdisdatatable
            [datatableService]="peopleMedicalInformationListService"
            [id]="'member-medical-information-list'"
            [sorts]="[{dir:'asc',prop:'lastName'}]"
            [class]="'entity__table'"
          >
            <!-- Hidden filter for entityId -->
            <cgdis-portal-datatable-number-filter [hidden]="true"
                                                  [filterConfig]="filterConfigEntityEqual"
                                                  [filterName]="'entityId'"
                                                  [customFormControl]="formControl"
                                                  [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-number-filter>

            <cgdis-portal-datatable-toggle-filter [hidden]="true"

                                                  [filterName]="'allPersonsUnderEntity'"
                                                  [filterConfig]="filterConfigAllPersons"
                                                  [datatableService]="peopleMedicalInformationListService"
                                                  [customFormControl]="formControlToggle"
            ></cgdis-portal-datatable-toggle-filter>

            <!-- Icon warning when no aptitudes valable-->
            <ep-datatable-column [columnName]="'warning'" [flexGrow]="0.5" [sortable]="false">
              <ng-template epDatatableHeader>

              </ng-template>
              <ng-template epDatatableCell let-context>
                <cgdis-portal-icon *ngIf="context.value"
                                   [icon]="'icon-warning'"></cgdis-portal-icon>

              </ng-template>


            </ep-datatable-column>

            <!-- CGDIS registration number -->
            <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [flexGrow]="1.5">
              <ng-template epDatatableHeader>
                <span
                  [translate]="isMobile ? 'people_medical_information.functions.cgdisregistrationnumbermobile' : 'people_medical_information.functions.cgdisregistrationnumber'"></span>
              </ng-template>

              <ng-template epDatatableCell let-row="row" class="suspension">
                {{row.cgdisRegistrationNumber}}
                <div *ngIf="row?.currentSuspensions?.length>0">
                  <div *ngFor="let currentSuspension of row?.currentSuspensions"
                       style="overflow: visible; width: 500px;">
                    <ng-container
                      *ngIf="currentSuspension.endDate==null; else currentSuspensionEndDate">{{'people_medical_information.functions.noSuspensionEndDate' | translate}}</ng-container>
                    <ng-template
                      #currentSuspensionEndDate>{{'people_medical_information.functions.suspensionEndDate' | translate}} {{getFormattedEndDate(currentSuspension?.endDate)}}</ng-template>
                  </div>
                </div>
              </ng-template>

              <ng-template epDatatableFilter *ngIf="!isMobile">
                <cgdis-portal-datatable-text-filter [filterName]="'cgdisRegistrationNumber'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- First name -->
            <ep-datatable-column *ngIf="!isMobile" [columnName]="'firstName'" [flexGrow]="1.5">
              <ng-template epDatatableHeader>
                <span [translate]="'people_medical_information.functions.firstname'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
                {{context.value | defaultValue:'-'}}
              </ng-template>

              <ng-template epDatatableFilter>
                <cgdis-portal-datatable-text-filter [filterName]="'firstName'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- Last name -->
            <ep-datatable-column *ngIf="!isMobile" [columnName]="'lastName'" [flexGrow]="2">
              <ng-template epDatatableHeader>
                <span [translate]="'people_medical_information.functions.lastname'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
                {{context.value | defaultValue:'-'}}
              </ng-template>

              <ng-template epDatatableFilter>
                <cgdis-portal-datatable-text-filter [filterName]="'lastName'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleMedicalInformationListService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- Aptitudes -->
            <ep-datatable-column [columnName]="'aptitudes'" [flexGrow]="7" [sortable]="false">
              <ng-template epDatatableHeader>
                <span
                  [translate]="isMobile ? 'people_medical_information.functions.aptitudesmobile' : 'people_medical_information.functions.aptitudes'"></span>
              </ng-template>
              <ng-template epDatatableCell let-context>
                <span *ngIf="context.value.length>0; else noDataBlock">
                  <div *ngIf="!isMobile; else short">
                    <ng-template ngFor let-aptitudeItem [ngForOf]="context.value" let-i="index">
                      <div class="people-aptitude row">
                        <div class="col-4 aptitude-category" [matTooltip]="aptitudeItem.aptitude.name">
                          {{aptitudeItem.aptitude.name | defaultValue:'-'}}
                        </div>
                        <div *ngIf="aptitudeItem.statut.externalId === 'CONCLU01'"
                             class="col-4 people-aptitude-badge people-apt">{{aptitudeItem.statut.name}}</div>
                        <div *ngIf="aptitudeItem.statut.externalId === 'CONCLU02'"
                             class="col-4 people-aptitude-badge people-apt-with-restriction">{{aptitudeItem.statut.name}}</div>
                        <div *ngIf="aptitudeItem.statut.externalId === 'CONCLU03'"
                             class="col-4 people-aptitude-badge people-inapt">{{aptitudeItem.statut.name}}</div>
                        <div
                          *ngIf="aptitudeItem.statut.externalId === 'CONCLU04' || aptitudeItem.statut.externalId === 'CONCLU05'"
                          class="col-4 people-aptitude-badge people-temporary-inapt">{{aptitudeItem.statut.name}}</div>
                        <div class="col-4 aptitude-category"
                             [ngClass]="{'aptitude-expired': isExpired(aptitudeItem.endDate)}">
                          {{getFormattedFullDateSpan(aptitudeItem)}}
                        </div>
                      </div>
                    </ng-template>
                  </div>
                  <ng-template #short>
                    <ng-template ngFor let-aptitudeItem [ngForOf]="context.value" let-i="index">
                      <div class="people-aptitude row">
                        <div class="aptitude-category people-aptitude-badge people-apt">
                          {{aptitudeItem.aptitude.name}}
                        </div>
                      </div>
                    </ng-template>
                  </ng-template>
                </span>
              </ng-template>
            </ep-datatable-column>

            <!-- Restrictions -->
            <ep-datatable-column [columnName]="'restrictions'" [flexGrow]="4" [sortable]="false">
              <ng-template epDatatableHeader>
                <span
                  [translate]="isMobile ? 'people_medical_information.functions.restrictionsmobile' : 'people_medical_information.functions.restrictions'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
                <span *ngIf="context.value.length>0; else noDataBlock">
                  <div *ngIf="!isMobile; else short">
                    <ng-template ngFor let-restrictionItem [ngForOf]="context.value" let-i="index">
                      <div class="row">
                        <div class="col-xl-8 col-12 people-restriction">
                          {{restrictionItem.restriction.description | defaultValue:'-'}}
                        </div>
                        <div class="col-xl-4 col-12 people-restriction">
                          {{getFormattedStartDate(restrictionItem.startDate)}}
                        </div>
                      </div>
                    </ng-template>
                  </div>

                  <ng-template #short>
                    {{context.value.length > 0 ? '✔' : '✘'}}
                  </ng-template>
                </span>
              </ng-template>

            </ep-datatable-column>

          </cgdis-portal-cgdisdatatable>
        </ng-container>
      </div>
    </cgdis-portal-panel>

    <!-- No entities -->
    <ng-template #noEntities>
      <div class="service-plan-list-empty">
        <span [translate]="'layout.navigation.menu.items.people_medical_information.list.empty'"></span>
      </div>
    </ng-template>

  </cgdis-portal-page-template>
</ng-container>


<ng-template #noDataBlock>
  <span>-</span>
</ng-template>
