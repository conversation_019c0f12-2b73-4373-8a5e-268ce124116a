import { Component, Input, OnInit } from '@angular/core';
import { PersonMedicalInformation } from '@app/model/person/person-medical-information.model';

@Component({
  selector: 'cgdis-portal-people-medical-information-selector-mobile',
  templateUrl: './people-medical-information-selector-mobile.component.html',
  styleUrls: ['../people-medical-information.component.scss'],
})
export class PeopleMedicalInformationSelectorMobileComponent implements OnInit {
  /**
   * The schedule
   */
  @Input() person: PersonMedicalInformation;

  constructor() {}

  ngOnInit(): void {}
}
