import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PEOPLE_MEDICAL_INFORMATION_ROUTE } from '@app/people-medical-information/people-medical-information.route';
import { SharedModule } from '@app/common/shared/shared.module';
import { PeopleMedicalInformationSelectorMobileComponent } from '@app/people-medical-information/people-medical-information-selector-mobile/people-medical-information-selector-mobile.component';
import { TabsListModule } from '@app/common/modules/tabs-list/tabs-list.module';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(PEOPLE_MEDICAL_INFORMATION_ROUTE),
    TabsListModule,
  ],
  declarations: [PeopleMedicalInformationSelectorMobileComponent],
  exports: [PeopleMedicalInformationSelectorMobileComponent],
})
export class PeopleMedicalInformationSelectorMobileModule {}
