import { Routes } from '@angular/router';
import { RoleGuard } from '../security/guards/role.guard';
import { PeopleMedicalInformationComponent } from '@app/people-medical-information/people-medical-information.component';
import { PeopleMedicalInformationListComponent } from '@app/people-medical-information/list/people-medical-information-list.component';

export const PEOPLE_MEDICAL_INFORMATION_ROUTE: Routes = [
  {
    path: '',
    component: PeopleMedicalInformationComponent,
    canActivate: [RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST'],
    },
    children: [
      {
        path: 'list',
        component: PeopleMedicalInformationListComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST'],
        },
      },
    ],
  },
];
