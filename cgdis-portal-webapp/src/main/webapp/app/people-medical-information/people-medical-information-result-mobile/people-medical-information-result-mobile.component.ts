import { Component, Input, OnInit } from '@angular/core';
import { PersonMedicalInformation } from '@app/model/person/person-medical-information.model';
import { DateModel, DateService } from '@eportal/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-people-medical-information-result-mobile',
  templateUrl: './people-medical-information-result-mobile.component.html',
})
export class PeopleMedicalInformationResultMobileComponent implements OnInit {
  /**
   * The schedule
   */
  @Input() person: PersonMedicalInformation;

  startDateFormat = 'DD/MM/YY';
  endDateFormat = 'MM/YY';

  constructor(
    private dateService: DateService,
    private translateService: TranslateService,
  ) {}

  ngOnInit(): void {}

  public isExpired(date: DateModel): boolean {
    return this.dateService.isAfter(this.dateService.now(), date);
  }

  public getFormattedEndDate(date: DateModel): string {
    if (date != null) {
      if (date.year >= 2200) {
        return this.translateService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.endDateFormat);
    } else {
      return '-';
    }
  }

  public getFormattedStartDate(date: DateModel): string {
    if (date != null) {
      if (date.year <= 1900) {
        return this.translateService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.startDateFormat);
    } else {
      return '-';
    }
  }
}
