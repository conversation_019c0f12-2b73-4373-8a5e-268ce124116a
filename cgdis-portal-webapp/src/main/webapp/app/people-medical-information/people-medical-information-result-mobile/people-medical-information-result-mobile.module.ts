import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PEOPLE_MEDICAL_INFORMATION_ROUTE } from '@app/people-medical-information/people-medical-information.route';
import { PeopleMedicalInformationResultMobileComponent } from '@app/people-medical-information/people-medical-information-result-mobile/people-medical-information-result-mobile.component';
import { SharedModule } from '@app/common/shared/shared.module';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(PEOPLE_MEDICAL_INFORMATION_ROUTE),
  ],
  declarations: [PeopleMedicalInformationResultMobileComponent],
  exports: [PeopleMedicalInformationResultMobileComponent],
})
export class PeopleMedicalInformationResultMobileModule {}
