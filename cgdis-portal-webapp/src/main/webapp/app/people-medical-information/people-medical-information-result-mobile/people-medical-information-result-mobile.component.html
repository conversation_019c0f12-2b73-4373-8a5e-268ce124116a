<table *ngIf="person.aptitudes.length>0; else empty" style="width: 100%;">
  <tbody>
    <tr *ngFor="let aptitudeItem of person.aptitudes;">
      <td style="padding: 0.5rem 0.5rem; width: 10%;" *ngIf="aptitudeItem.statut.externalId === 'CONCLU01'" class="people-aptitude-badge people-apt">●
      </td>
      <td style="padding: 0.5rem 0.5rem; width: 10%;" *ngIf="aptitudeItem.statut.externalId === 'CONCLU02'" class="people-aptitude-badge people-apt-with-restriction">●
      </td>
      <td style="padding: 0.5rem 0.5rem; width: 10%;" *ngIf="aptitudeItem.statut.externalId === 'CONCLU03'" class="people-aptitude-badge people-inapt">●
      </td>
      <td style="padding: 0.5rem 0.5rem; width: 10%;" *ngIf="aptitudeItem.statut.externalId === 'CONCLU04' || aptitudeItem.statut.externalId === 'CONCLU05'" class="people-aptitude-badge people-temporary-inapt">●
      </td>
      <td style="padding: 0.5rem 0.5rem; width: 50%;">
        {{aptitudeItem.aptitude.name}}
      </td>
      <td style="padding: 0.5rem 0.5rem; width: 40%;" class="aptitude-category" [ngClass]="{'aptitude-expired': isExpired(aptitudeItem.endDate)}">
          {{getFormattedStartDate(aptitudeItem.startDate)}}
          - {{getFormattedEndDate(aptitudeItem.endDate)}}
      </td>
    </tr>
  </tbody>
</table>

<ng-template #empty>
  <div style="margin-top: 16px;">{{'default.table.noresult' | translate}}</div>
</ng-template>
