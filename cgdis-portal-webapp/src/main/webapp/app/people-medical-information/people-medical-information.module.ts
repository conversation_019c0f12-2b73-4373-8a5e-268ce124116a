import { SharedModule } from '../common/shared/shared.module';
import { NgModule } from '@angular/core';
import { PeopleMedicalInformationComponent } from './people-medical-information.component';
import { RouterModule } from '@angular/router';
import { PEOPLE_MEDICAL_INFORMATION_ROUTE } from '@app/people-medical-information/people-medical-information.route';
import { PeopleMedicalInformationListModule } from '@app/people-medical-information/list/people-medical-information-list.module';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(PEOPLE_MEDICAL_INFORMATION_ROUTE),
    PeopleMedicalInformationListModule,
  ],
  declarations: [PeopleMedicalInformationComponent],
})
export class PeopleMedicalInformationModule {}
