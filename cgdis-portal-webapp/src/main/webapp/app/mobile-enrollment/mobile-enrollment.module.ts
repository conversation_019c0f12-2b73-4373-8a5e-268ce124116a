import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MobileEnrollmentRoutingModule } from './mobile-enrollment-routing.module';
import { MobileEnrollmentComponent } from './mobile-enrollment.component';
import { MobileEnrollmentInitComponent } from './mobile-enrollment-init/mobile-enrollment-init.component';
import { MobileEnrollmentAuthorizationComponent } from './mobile-enrollment-authorization/mobile-enrollment-authorization.component';
import { MobileEnrollmentService } from '@app/mobile-enrollment/mobile-enrollment.service';

import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { RoutingService } from '@app/routing/routing.service';
import { QRCodeModule } from 'angularx-qrcode';

@NgModule({
  imports: [
    CommonModule,
    MobileEnrollmentRoutingModule,
    PageTemplateModule,
    SharedModule,
    QRCodeModule,
  ],
  declarations: [
    MobileEnrollmentComponent,
    MobileEnrollmentInitComponent,
    MobileEnrollmentAuthorizationComponent,
  ],
  providers: [MobileEnrollmentService, RoutingService],
})
export class MobileEnrollmentModule {}
