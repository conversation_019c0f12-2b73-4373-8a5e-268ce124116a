import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { MobileEnrollmentService } from '@app/mobile-enrollment/mobile-enrollment.service';
import { TokenMobileEnrollment } from '@app/model/mobile/token-mobile-enrollment';

@Component({
  selector: 'cgdis-portal-mobile-enrollment-authorization',
  templateUrl: './mobile-enrollment-authorization.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MobileEnrollmentAuthorizationComponent implements OnInit {
  qrCode: TokenMobileEnrollment;

  constructor(
    private mobileEnrollmentService: MobileEnrollmentService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.mobileEnrollmentService.getQrCode().subscribe({
      next: (value) => {
        this.qrCode = value;
        this.cd.markForCheck();
      },
    });
  }
}
