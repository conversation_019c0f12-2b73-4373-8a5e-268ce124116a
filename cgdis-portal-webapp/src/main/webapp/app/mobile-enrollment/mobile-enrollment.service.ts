import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { TokenMobileEnrollment } from '@app/model/mobile/token-mobile-enrollment';

@Injectable()
export class MobileEnrollmentService {
  private baseUrl = ['mobileenrollment'];

  constructor(private restService: RestService) {}

  getQrCode(): Observable<TokenMobileEnrollment> {
    return this.restService.one<TokenMobileEnrollment>(...this.baseUrl).get();
  }
}
