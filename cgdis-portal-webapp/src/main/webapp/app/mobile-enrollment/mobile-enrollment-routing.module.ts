import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MobileEnrollmentComponent } from '@app/mobile-enrollment/mobile-enrollment.component';
import { MobileEnrollmentInitComponent } from '@app/mobile-enrollment/mobile-enrollment-init/mobile-enrollment-init.component';
import { MobileEnrollmentAuthorizationComponent } from '@app/mobile-enrollment/mobile-enrollment-authorization/mobile-enrollment-authorization.component';

const routes: Routes = [
  {
    path: '',
    component: MobileEnrollmentComponent,
    children: [
      {
        path: '',
        redirectTo: 'enrollment',
        pathMatch: 'full',
      },
      {
        path: 'enrollment',
        component: MobileEnrollmentInitComponent,
        data: {
          titleKey: 'mobile-enrollment.title',
        },
      },
      {
        path: 'authorization',
        component: MobileEnrollmentAuthorizationComponent,
        data: {
          titleKey: 'mobile-enrollment.title',
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MobileEnrollmentRoutingModule {}
