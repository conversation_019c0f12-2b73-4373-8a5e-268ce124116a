import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { RoutingService } from '@app/routing/routing.service';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-mobile-enrollment',
  templateUrl: './mobile-enrollment.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MobileEnrollmentComponent implements OnInit, OnDestroy {
  /**
   * Key used for the title of the administration page
   */
  titleKey: string;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private routingService: RoutingService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.titleKey = this.routingService.updateTitleKey(this.isMobile);
        }),
    );
  }

  ngOnInit() {
    this.titleKey = this.routingService.updateTitleKey(this.isMobile);
    this.subscriptions.push(
      this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.titleKey = this.routingService.updateTitleKey(this.isMobile);
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
