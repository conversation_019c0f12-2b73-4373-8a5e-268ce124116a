export enum AuditTypeEnum {
  ALLOWANCE_CONFIG = 'ALLOWANCE_CONFIG',
  PRESTATION = 'PRESTATION',
  PDS = 'PDS',
  LOGAS = 'LOGAS',
  MODEL = 'MODEL',
  VERSION_PDS = 'VERSION_PDS',
  VERSION_MODEL = 'VERSION_MODEL',
  COPY_PRESTATION = 'COPY_PRESTATION',
  SLOT = 'SLOT',
  PERM_DEPLOYMENT_PLAN = 'PERM_DEPLOYMENT_PLAN',
  PERM_SERVICE_PLAN = 'PERM_SERVICE_PLAN',
  PERM_CONFIG_DPCE = 'PERM_CONFIG_DPCE',
  PERM_CONFIGDPCE_COPY = 'PERM_CONFIGDPCE_COPY',
  RICI_ALERT_GROUP = 'RICI_ALERT_GROUP',
  RICI_PAGER = 'RICI_PAGER',
}

export enum AuditActionTypeEnum {
  ADD = 'ADD',
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  CLOSURE = 'CLOSURE',
  TEAM_CLOSURE = 'TEAM_CLOSURE',
  TEAM_ADD = 'TEAM_ADD',
  TEAM_DELETE = 'TEAM_DELETE',
  FULL_AV_REM_ADD = 'FULL_AV_REM_ADD',
  FULL_AV_REM_DELETE = 'FULL_AV_REM_DELETE',
  FULL_AV_ADD = 'FULL_AV_ADD',
  FULL_AV_DELETE = 'FULL_AV_DELETE',
  FULL_AV_CLOSURE = 'FULL_AV_CLOSURE',
  COPY = 'COPY',
  COPY_ADD = 'COPY_ADD',
  COPY_DELETE = 'COPY_DELETE',
  COPY_CLOSURE = 'COPY_CLOSURE',
  COPY_ENTITY = 'COPY_ENTITY',
  SLOT = 'SLOT',
  DAY = 'DAY',
  WEEK = 'WEEK',
  SPLIT = 'SPLIT',
  SPLIT_ADD = 'SPLIT_ADD',
  SPLIT_SPLITTED = 'SPLIT_SPLITTED',
  MERGE = 'MERGE',
  MERGE_DELETED = 'MERGE_DELETED',
  MERGE_EXTENDED = 'MERGE_EXTENDED',
  ASSOCIATE = 'ASSOCIATE',
  DEACTIVATE = 'DEACTIVATE',
  START = 'START',
  END = 'END',
  VIEW = 'VIEW',
  BOOKMARKED_UPDATE = 'BOOKMARKED_UPDATE',
  PERM_CATEGORY_UPDATE = 'PERM_CATEGORY_UPDATE',
  PERM_SUBCAT_UPDATE = 'PERM_SUBCAT_UPDATE',
}
