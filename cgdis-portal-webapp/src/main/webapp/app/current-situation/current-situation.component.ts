import { take } from 'rxjs/operators';
import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { LegendItem } from '../common/modules/legend/legend-item';
import { FieldGroupOption } from '../common/modules/form-module/model/field-group-option.model';
import _ from 'lodash';
import { FieldOption } from '../common/modules/form-module/model/field-option.model';
import { TranslateService } from '@ngx-translate/core';
import { CurrentSituationService } from './current-situation.service';
import { Schedule } from '../operational/service-plan/schedule/schedule.model';
import { IconConstants } from '../common/shared/icon/icon-constants';
import { ConnectedUserService } from '../security/connected-user.service';
import { interval, Subscription } from 'rxjs';
import { ServicePlanWithVehicleStatus } from '@app/model/service-plan-with-vehicle-status.model';
import { Entity } from '@app/model/entity.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { BackupModeGlobalService } from '@app/common/shared/services/backup-mode-global.service';

@Component({
  selector: 'cgdis-portal-current-situation',
  templateUrl: './current-situation.component.html',
  styleUrls: ['./current-situation.component.scss'],
  providers: [CurrentSituationService],
})
export class CurrentSituationComponent implements OnInit, OnDestroy {
  /**
   * All items displayed in the legend
   */
  public legendItems: LegendItem[];
  legendItemsOne: LegendItem[];
  legendItemsSecond: LegendItem[];
  legendItemsThird: LegendItem[];

  /**
   * Legend items for persons
   */
  public legendPersonsItems: LegendItem[];
  legendPersonsItemsOne: LegendItem[];

  /**
   * Entity accessible
   * Note: can be null depending on user's rights
   */
  public entities: FieldGroupOption<string, Entity>[] = [];
  public primaryBaseModel: Entity;

  /**
   * The selected entity
   */
  public selectedEntity: Entity;

  /**
   * All schedules for selected entity
   */
  public schedules: Schedule[];

  /**
   * All schedules for selected entity
   */
  public servicePlanIds: number[];

  /**
   * show/hide the spinner
   */
  public loading = false;

  public totalEntities: number;

  private periodUpdateVehicle = 15000; //(en ms)

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  isBackupGroupActivated: boolean = false;

  constructor(
    private currentSituationService: CurrentSituationService,
    private backupModeGlobalService: BackupModeGlobalService,
    private connectedUserService: ConnectedUserService,
    private cd: ChangeDetectorRef,
    private translateService: TranslateService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  public ngOnInit(): void {
    // Init legend items
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'service_plan.legend.status.complete',
        classes: ['-complete'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'service_plan.legend.status.partial',
        classes: ['-partial'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'service_plan.legend.status.degraded',
        classes: ['-degraded'],
      }),
      new LegendItem({
        id: '4',
        labelKey: 'service_plan.legend.status.incomplete',
        classes: ['-incomplete'],
      }),
      new LegendItem({
        id: '8',
        labelKey: 'service_plan.legend.status.empty',
        classes: ['-empty'],
      }),
      new LegendItem({
        id: '5',
        labelKey: 'dashboard.members.chart.fire',
        classes: [],
        imageId: IconConstants.FIRE,
      }),
      new LegendItem({
        id: '6',
        labelKey: 'dashboard.members.chart.ambulance',
        classes: [],
        imageId: IconConstants.AMBULANCE,
      }),
      new LegendItem({
        id: '7',
        labelKey: 'dashboard.members.chart.others',
        classes: [],
        imageId: IconConstants.OTHERS,
      }),

      new LegendItem({
        id: '7',
        labelKey: 'dashboard.members.chart.commandment',
        classes: [],
        imageId: IconConstants.COMMANDMENT,
      }),
      new LegendItem({
        id: '8',
        labelKey: 'dashboard.members.chart.samu',
        classes: [],
        imageId: IconConstants.SAMU,
      }),
      new LegendItem({
        id: '9',
        labelKey: 'dashboard.members.chart.gis',
        classes: [],
        imageId: IconConstants.GIS,
      }),
      new LegendItem({
        id: '10',
        labelKey: 'dashboard.members.chart.dms',
        classes: [],
        imageId: IconConstants.DMS,
      }),
      new LegendItem({
        id: '11',
        labelKey: 'dashboard.members.chart.others',
        classes: [],
        imageId: IconConstants.OTHERS,
      }),
    ];

    this.legendItemsOne = [
      this.legendItems[0],
      this.legendItems[1],
      this.legendItems[2],
    ];
    this.legendItemsSecond = [
      this.legendItems[3],
      this.legendItems[4],
      this.legendItems[6],
    ];
    this.legendItemsThird = [this.legendItems[7], this.legendItems[5]];

    this.legendPersonsItems = [
      new LegendItem({
        id: '1',
        labelKey: 'current-situation.legend.pro',
        classes: [],
        imageId: IconConstants.PRO,
      }),
      new LegendItem({
        id: '2',
        labelKey: 'current-situation.legend.barracked',
        classes: [],
        imageId: IconConstants.BARRACKED,
      }),
    ];
    this.legendPersonsItemsOne = [
      this.legendPersonsItems[0],
      this.legendPersonsItems[1],
    ];

    // Init list of accessible entities
    this.getAllEntities();

    const source = interval(this.periodUpdateVehicle);
    this.subscriptions.push(
      source.subscribe(() => this.refreshVehicleStatus()),
    );
    this.initConfig();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Get all schedule for selected entity
   */
  public getAllSchedule(): void {
    if (this.selectedEntity.tecid != undefined) {
      this.loading = true;
      this.currentSituationService
        .getSchedules(this.selectedEntity.tecid)
        .pipe(take(1))
        .subscribe(
          (allSchedules) => {
            this.schedules = allSchedules;
            this.cd.markForCheck();
            this.servicePlanIds = allSchedules.map((schedule) => {
              return schedule.servicePlan.tecid;
            });
          },
          () => {},
          () => {
            this.loading = false;
          },
        );
    }
  }

  /**
   * Get all accessible entities for connected user
   */
  private getAllEntities(): void {
    this.currentSituationService
      .getEntities()
      .pipe(take(1))
      .subscribe((allEntities) => {
        this.totalEntities = allEntities.length;
        let primaryId =
          this.connectedUserService.getCurrentCgdisPortalUser().userDetails
            .primaryEntity;
        let primaryCategory = 0;
        let primaryEntity = 0;

        let i = 0;
        this.entities = _.chain(allEntities)
          .groupBy('type')
          .toPairs()
          .map((typeAndEntities) => {
            // All entities for the current category
            let j = 0;
            let children = _.map(typeAndEntities[1], (oneEntity) => {
              let label = oneEntity.name;
              // Set primary base model
              if (oneEntity.tecid === primaryId) {
                primaryEntity = j;
                primaryCategory = i;
              }
              j += 1;

              return new FieldOption({
                label: label,
                value: oneEntity,
              });
            });

            i += 1;

            // Create the group
            return new FieldGroupOption<string, Entity>({
              I18NLabel: 'entities.type.' + typeAndEntities[0],
              label: this.translateService.instant(
                'entities.type.' + typeAndEntities[0],
              ),
              value: typeAndEntities[0],
              children: children,
            });
          })
          .value();

        if (this.entities != null && this.entities.length > 0) {
          this.primaryBaseModel =
            this.entities[primaryCategory.valueOf()].children[
              primaryEntity.valueOf()
            ].value;
          this.setEntity(this.primaryBaseModel);
        }
        this.cd.markForCheck();
      });
  }

  /**
   * RefeshVehcileStatus for a schedule (every minutes)
   */
  private refreshVehicleStatus(): void {
    if (
      !this.loading &&
      this.servicePlanIds != null &&
      this.servicePlanIds.length > 0
    ) {
      this.currentSituationService
        .refreshVehicleStatus(this.servicePlanIds)
        .pipe(take(1))
        .subscribe(
          (allServicePlan) => {
            this.mapRefresh(allServicePlan);
            this.cd.markForCheck();
          },
          () => {},
          () => {},
        );
    }
  }

  private mapRefresh(allServicePlan: ServicePlanWithVehicleStatus[]) {
    for (let oneSchedule of this.schedules) {
      for (let oneSp of allServicePlan) {
        if (oneSchedule.servicePlan.tecid === oneSp.servicePlan.tecid) {
          oneSchedule.isBackupGroupActive =
            oneSp.servicePlan.isBackupGroupActive;
          oneSchedule.servicePlanVehiculeStatus = oneSp.vehicleStatusHistoric;
        }
      }
    }
  }

  /**
   * Set selected entity into form control
   * @param entity: the selected entity
   */
  public setEntity(entity: Entity): void {
    this.selectedEntity = entity;
    this.getAllSchedule();
  }

  public initConfig() {
    let configurations = this.connectedUserService.getConfiguration();

    // Retrieve the correct configuration

    this.backupModeGlobalService.getCanViewBackupMode().subscribe((value) => {
      this.isBackupGroupActivated = value;
      this.cd.markForCheck();
    });
  }
}
