<cgdis-portal-popup-template
  [data]="data"
  (onClose)="closePopup()"
  [popupContentClasses]="['p-0']"
  [hideButtons]="true">

  <ng-container popupTitle>
    {{data.content.firstName}} {{data.content.lastName}}
  </ng-container>

  <div popupContent>
    <ng-container *ngIf="contactInformation">

        <cgdis-portal-form [formId]="'general-contact-id'" [hideFormActions]="true" [formReadonly]="true">

          <div style="display: flex;justify-content: center;">
            <div class="col-md-6">
              <cgdis-portal-input-field
                [name]="'privateMobileNumber'"
                [fieldMaxLength]="20"
                [labelKey]="'general_information.contact_information.private.mobile'"
                [initialValue]="isValid(contactInformation.generalContactInformation.privateMobileNumber) ? contactInformation.generalContactInformation.privateMobileNumber : '-'" class="col-md-6">
              </cgdis-portal-input-field>
            </div>
            <div class="col-md-6">
              <cgdis-portal-input-field
                [name]="'professionalMobileNumber'"
                [fieldMaxLength]="20"
                [labelKey]="'general_information.contact_information.professional.mobile'"
                [initialValue]="isValid(contactInformation.generalContactInformation.professionalMobileNumber) ? contactInformation.generalContactInformation.professionalMobileNumber : '-'" class="col-md-6">
              </cgdis-portal-input-field>
            </div>

          </div>

          <div style="display: flex;justify-content: center;">
            <div class="col-md-6">
              <cgdis-portal-input-field
                [name]="'privatePhoneNumber'"
                [fieldMaxLength]="20"
                [labelKey]="'general_information.contact_information.private.phone'"
                [initialValue]="isValid(contactInformation.generalContactInformation.privatePhoneNumber) ? contactInformation.generalContactInformation.privatePhoneNumber : '-'" class="col-md-6">
              </cgdis-portal-input-field>
            </div>
            <div class="col-md-6">
              <cgdis-portal-input-field
                [name]="'professionalMobileNumber'"
                [fieldMaxLength]="20"
                [labelKey]="'general_information.contact_information.professional.phone'"
                [initialValue]="isValid(contactInformation.generalContactInformation.professionalPhoneNumber) ? contactInformation.generalContactInformation.professionalPhoneNumber :'-'" class="col-md-6">
              </cgdis-portal-input-field>
            </div>
          </div>


          <div style="display: flex;justify-content: center;">
            <div class="col-md-6">
              <cgdis-portal-input-field
                [name]="'privateEmail'"
                [fieldMaxLength]="150"
                [labelKey]="'general_information.contact_information.private.email'"
                [initialValue]="isValid(contactInformation.generalContactInformation.privateEmail) ? contactInformation.generalContactInformation.privateEmail : '-'" class="col-md-6">
              </cgdis-portal-input-field>
            </div>
            <div class="col-md-6">
              <cgdis-portal-input-field
                [name]="'professionalEmail'"
                [fieldMaxLength]="150"
                [labelKey]="'general_information.contact_information.professional.email'"
                [initialValue]="isValid(contactInformation.generalContactInformation.professionalEmail) ? contactInformation.generalContactInformation.professionalEmail : '-'" class="col-md-6">
              </cgdis-portal-input-field>
            </div>
          </div>

        </cgdis-portal-form>

    </ng-container>

  </div>

</cgdis-portal-popup-template>
