import { take } from 'rxjs/operators';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SimplePopupDataModel } from '../../common/modules/popup/simple-popup-data.model';
import { GeneralContactInformationService } from '../../general-information/general-contact/general-contact-information.service';
import { PersonGeneralInformation } from '../../model/person/person-general-information.model';
import { FORM_SERVICE } from '../../common/modules/form-module/service/iform.service';
import { UpdateGeneralContactService } from '../../general-information/general-contact/update-general-contact.service';

@Component({
  selector: 'cgdis-portal-current-situation-person-contact-information',
  templateUrl: './current-situation-person-contact-information.component.html',
  styleUrls: ['../current-situation.component.scss'],
  providers: [
    GeneralContactInformationService,
    UpdateGeneralContactService,
    {
      provide: FORM_SERVICE,
      useExisting: UpdateGeneralContactService,
    },
  ],
})
export class CurrentSituationPersonContactInformationComponent
  implements OnInit
{
  /**
   * The person contact information
   */
  public contactInformation: PersonGeneralInformation;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: SimplePopupDataModel<any>,
    public dialogRef: MatDialogRef<any>,
    private contactService: GeneralContactInformationService,
  ) {}

  /**
   * Load person contact information on load
   */
  public ngOnInit(): void {
    this.contactService
      .get(this.data.content.tecid)
      .pipe(take(1))
      .subscribe((contactInformation: PersonGeneralInformation) => {
        this.contactInformation = contactInformation;
      });
  }

  /**
   * Close the popup
   */
  public closePopup(): void {
    this.dialogRef.close();
  }

  isValid(info: string): boolean {
    return info != null && info != undefined && info.length >= 1;
  }
}
