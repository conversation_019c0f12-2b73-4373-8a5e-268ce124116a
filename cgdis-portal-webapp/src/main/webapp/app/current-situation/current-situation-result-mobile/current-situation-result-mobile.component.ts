import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Schedule } from '@app/operational/service-plan/schedule/schedule.model';
import { BaseModel } from '@app/model/base-model.model';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { PrestationService } from '@app/common/shared/services/prestation.service';
import {
  DateModel,
  DateService,
  DatetimeModel,
  DatetimeService,
  TimeService,
} from '@eportal/core';
import { ServicePlanPosition } from '@app/model/service-plan-position.model';
import { ScheduleRow } from '@app/operational/service-plan/schedule/schedule-row.model';
import { Prestation } from '@app/model/prestation.model';

@Component({
  selector: 'cgdis-portal-current-situation-result-mobile',
  templateUrl: './current-situation-result-mobile.component.html',
  styleUrls: [
    '../current-situation.component.scss',
    'current-situation-result-mobile.component.scss',
  ],
})
export class CurrentSituationResultMobileComponent implements OnInit {
  /**
   * The schedule
   */
  @Input() schedule: Schedule;

  /**
   * The schedule
   */
  @Input() entity: BaseModel;

  @Input() isBackupGroupActivated: boolean = false;

  /**
   * Refresh the schedule when position have been updated
   */
  @Output() refreshSchedule = new EventEmitter<void>();

  public canAddPrestation = false;
  public canDeletePrestation = false;

  constructor(
    private popupService: SimplePopupService,
    private prestationService: PrestationService,
    private dateService: DateService,
    private timeService: TimeService,
    private dateTimeService: DatetimeService,
  ) {}

  public ngOnInit(): void {}

  /**
   * Retrieve all persons for a specific position and time slot
   * @param {ServicePlanPosition} position: the position
   * @param {ScheduleRow} row: the time slot
   * @returns {PrestationPerson[]}: all persons
   */
  public getPrestation(
    position: ServicePlanPosition,
    row: ScheduleRow,
  ): Prestation {
    if (row == undefined) {
      console.warn('Row is null for position ', position, row);
      return null;
    }
    const now = this.dateService.now();
    let persons: Prestation[] = [];
    let slotEndDateTime: DatetimeModel;
    if (this.timeService.isBefore(row.slot.startTime, row.slot.endTime)) {
      // slot on same day
      slotEndDateTime = this.dateTimeService.of(now, row.slot.endTime);
    } else {
      // Slot on 2 days
      let endDayToUse: DateModel;
      if (
        this.dateTimeService.isAfter(
          this.dateTimeService.of(now, row.slot.startTime),
          this.dateTimeService.now(),
        )
      ) {
        // display the slot started yesterday => the end date is today
        endDayToUse = now;
      } else {
        // display the slot starting today => the end date is tomorrow
        endDayToUse = this.dateService.add(now, 1, 'day');
      }
      slotEndDateTime = this.dateTimeService.of(endDayToUse, row.slot.endTime);
    }

    for (let prestation of row.prestations) {
      if (
        prestation.position.tecid === position.tecid &&
        this.dateTimeService.equals(prestation.endDateTime, slotEndDateTime)
      ) {
        persons = persons.concat(prestation);
      }
    }
    return persons.length > 0 ? persons[persons.length - 1] : undefined;
  }

  /**
   * Reduce name
   * @param name
   */
  public reduceName(name: string): string {
    return name.charAt(0) + '.';
  }

  /**
   * Retrieve the number of person available for given position and slot
   * @param positionId the position id
   * @param slot the slot id
   */
  public getPersonAvailable(positionId: number, slot: ScheduleRow): string {
    if (slot && slot.numberOfPersonAvailable) {
      return String(slot.numberOfPersonAvailable[positionId].count).concat(
        slot.numberOfPersonAvailable[positionId].partialAvailability ? '*' : '',
      );
    } else {
      return '0';
    }
  }
}
