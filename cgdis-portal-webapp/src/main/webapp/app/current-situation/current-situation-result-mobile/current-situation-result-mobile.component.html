<ng-container *ngIf="schedule">
  <span></span>
  <ul class="current-situation-result">
    <tr>
    <td [translate]="'current-situation.time-slot'"></td>
     <td
       *ngFor="let row of schedule.rows" class="current-situation-time" >
        <span class="current-situation-start" eportal-time-format [theTime]="row.slot.startTime" [timeFormat]="'HH:mm'"></span>
        <span class="current-situation-end" eportal-time-format [theTime]="row.slot.endTime" [timeFormat]="'HH:mm'"></span>
      </td>
    </tr>
    <tr *ngFor="let position of schedule.positions;" class="list-of-positions">
      <td class="position">{{position.label}}</td>
        <td>  <ng-container *ngIf="getPrestation(position, schedule.rows[0]); let prestation else addPersonToPosition">
          <span class="name">{{reduceName(prestation.person.firstName)}} {{prestation.person.lastName}}</span>
       <span class="matricule">- {{prestation.person.cgdisRegistrationNumber}}</span>
          <span class="contact" *ngIf="isBackupGroupActivated" >
        {{prestation.person.operationalContactInformation.mobileNumber}} / {{prestation.person.operationalContactInformation.pager}}
        </span>

      </ng-container></td>

      <ng-template #addPersonToPosition>-

      </ng-template>
    </tr>

    <ng-container *ngIf="schedule.backupGroup && schedule.isBackupGroupActive">
    <tr>
    <td  class="backup-group-label" [translate]="'current-situation.active-backup'"></td>
    <td class="backup-group">{{schedule.backupGroup.name}}</td>
    </tr>
    </ng-container>
  </ul>
  <!--<span *ngIf="schedule.servicePlanVehiculeStatus" [translate]="'current-situation.status'"
        [translateParams]="{
        statut: schedule.servicePlanVehiculeStatus.status.value,
        date: dateTimeService.format(schedule.servicePlanVehiculeStatus.date, 'DD/MM/YY hh:mm:ss')
        }"></span>-->

</ng-container>
