@import 'utilities/functions';
@import 'utilities/_variables';

.backup-group {
  color: red;
  font-style: italic;
  font-weight: bold;
}

.backup-group-label {
  color: red;
  font-style: italic;
  font-weight: bold;
}

.current-situation-actions {
  border-top-right-radius: 0.4rem;
  border-top-left-radius: 0.4rem;
  background-color: $c-primary;
  height: 5rem;
  max-height: 5rem;
  color: white;
  font-weight: lighter;
  font-size: 1.25rem;

  text-transform: uppercase;

  #column-status {
    display: inline-block;
    width: 4rem;
    max-width: 4rem;
  }

  #column-status-els {
    display: inline-block;
    width: 8rem;
    max-width: 8rem;
  }

  #column-service-plan {
    display: inline-block;
    width: 16em;
    max-width: 16em;
    line-height: 5rem;

    span {
      padding: 0 1rem;
    }
  }

  #column-schedule {
    display: inline-block;
    width: 10em;
    max-width: 10em;

    span {
      padding: 0 0.5rem;
    }
  }

  #column-ressources {
    display: inline-block;

    span {
      padding: 0 1rem;
    }

    .icon-plans-service {
      width: 3rem;
    }
  }
}

.current-situation-status {
  display: inline-block;
  vertical-align: middle;
  line-height: 3rem;
  text-align: center;
  @media (min-width: 768px) {
    padding: 0 2rem;
    width: 6rem;
    max-width: 6rem;
  }
  margin-left: 0;
  @media (min-width: 768px) {
    width: 2rem;
    max-width: 2rem;
  }
}

.current-situation-els-status {
  width: 70px;
  max-width: 70px;
}

.current-situation-service-plan {
  position: relative;
  @media (min-width: 992px) {
    padding: 0 2rem;
    width: 17em;
    max-width: 17em;
    color: black;
  }
  @media (max-width: 992px) {
    width: 15em;
    max-width: 15em;
    color: white;
  }

  span {
    display: block;
    margin-left: 1em;
  }

  .name {
    font-weight: 500;

    @media (max-width: 992px) {
      margin-left: 2.3em;
      color: white !important;
    }

    &:hover {
      cursor: pointer;
      text-decoration: underline;
    }
  }

  .type {
    font-weight: lighter;

    @media (max-width: 992px) {
      margin-left: 2.3em;
      color: white !important;
    }
  }

  .icon {
    position: absolute;
    top: calc(50% - 1.4rem);
    left: 0;
    height: 100%;
    line-height: 2.5rem;
    @media (min-width: 992px) {
      width: 1.7rem;
    }
    @media (max-width: 992px) {
      width: 2.5rem;
    }
  }
}

.current-situation-time {
  padding: 0 2rem;
  width: 12em;
  max-width: 12em;
  @media (max-width: 992px) {
    margin-bottom: 2rem;
    padding-left: 0 !important;
    font-weight: 500;
  }

  .current-situation-start {
    @media (min-width: 992px) {
      color: $c-gray;
    }
    vertical-align: middle;

    &::after {
      content: ' - ';
    }
  }

  .current-situation-end {
    @media (min-width: 992px) {
      color: $c-gray;
    }
    vertical-align: middle;
  }
}

.current-situation-row {
  display: flex;
  border-bottom: 1px solid #e9eef5;
  width: 100%;
}

cgdis-portal-tabs-list-item {
  outline: 0;
  height: 80px;
  max-height: none;
}

cgdis-portal-current-situation-selector {
  //float: left;
}

.schedule {
  justify-content: unset;
}

cgdis-portal-current-situation {
  .service-planner__schedule-selector.schedule.-complete.ng-star-inserted {
    @media (min-width: 768px) {
      border-bottom: 1px solid #e9eef5;
    }
  }
}

.icon-time,
.icon-plans-service {
  width: 1.6rem;
  height: 5rem;
}

.panel-left {
  float: left;
  width: 100%;
}

.current-situation-persons {
  @media (min-width: 768px) {
    min-width: 40em;
  }
  box-shadow: 0 0.3rem 1.2rem 0.3rem rgba(162, 195, 229, 0.3);

  ul {
    border-bottom-right-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .availabilities-entity {
    @media (max-width: 768px) {
      margin-left: -6%;
      width: 46%;
    }
  }

  .availabilities-prestations {
    @media (max-width: 768px) {
      margin-left: -6%;
      width: 46%;
    }
  }

  .availabilities-person {
    width: 200px;
  }

  .availabilities-interventions {
    width: 10rem;
  }
}

#current-situation-persons-header {
  border-top-right-radius: 0.5rem;
  border-top-left-radius: 0.5rem;
  background: $c-primary;
  padding: 1em;

  .service-planner__popup-search {
    margin: 0;
    border-radius: 0.3rem;
    background-position-x: 99%;
  }
}

.ressource.planner__people.people {
  padding: 1rem;
  width: 20em;
  min-width: 20em;
  max-width: 20em;
  @media (max-width: 768px) {
    width: 100%;
    max-width: 100%;
  }
  @media (max-width: 350px) {
    min-width: 15em;
  }
}

.mobile-panel-current-situation-header {
  @media (max-width: 768px) {
    background-color: #2c3e7b !important;
    padding: 0 12px !important;
  }

  span {
    &::after {
      color: white !important;
    }
  }
}

.current-situation-status {
  @media (max-width: 768px) {
    margin-right: 1rem;
  }
}

cgdis-portal-current-situation-detail {
  display: flex;
  flex-direction: column;
}
