import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { Schedule } from '@app/operational/service-plan/schedule/schedule.model';
import _ from 'lodash';
import { Subscription } from 'rxjs';
import { NgScrollbar } from 'ngx-scrollbar';
import { BaseModel } from '@app/model/base-model.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-current-situation-detail',
  templateUrl: './current-situation-detail.component.html',
  styleUrls: ['../current-situation.component.scss'],
})
export class CurrentSituationDetailComponent
  implements AfterViewInit, OnDestroy
{
  /**
   * All schedules for the entity
   */
  @Input() schedules: Schedule[];

  /**
   * selected entity
   */
  @Input() entity: BaseModel;

  @Input() loading = false;

  @Input() isBackupGroupActivated = false;

  /**
   * Refresh the schedule when position have been updated
   */
  @Output() refreshSchedule = new EventEmitter<void>();

  /**
   * The scrollbar
   */

  @ViewChildren('scrollSchedule') componentScrollSchedule: QueryList<any>;
  @ViewChild(NgScrollbar) scrollSchedule: NgScrollbar;

  /**
   * Show scrollbar if too many positions
   */
  public showScrollbar = true;

  /**
   * All subscriptions (to be kill with component)
   */
  public subscriptions: Subscription[] = [];

  public scrollCurrentValue = 0;

  isMobile: boolean = false;

  constructor(
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  public ngAfterViewInit(): void {}

  public ngOnDestroy(): void {
    this.showScrollbar = false;
    _.forEach(this.subscriptions, function (s: Subscription) {
      s.unsubscribe();
    });
  }

  /**
   * Scroll (left or right panel)
   * @param $event
   * @param scrollItem
   */
  public scroll($event: any, scrollItem: any): void {
    scrollItem.scrollYTo($event.target.scrollTop, 10);
    this.scrollCurrentValue = $event.target.scrollTop;
  }
}
