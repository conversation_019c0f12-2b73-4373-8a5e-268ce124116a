
<div *ngIf="schedules == undefined || schedules.length === 0; else showSchedules" class="panel" [ngStyle]="{'margin-top': isMobile ? '3.5rem' : ''}">
  <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
  <div class="service-plan-list-empty" style="padding-bottom: 0;">
    <span [translate]="'service_plan.versions.empty'"></span>
  </div>
</div>

<!-- table content -->
<ng-template #showSchedules>

  <div class="panel" *ngIf="!isMobile">

    <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>

    <div class="scrollable-content panel-left">
      <div class="current-situation-actions">
        <div id="column-status"></div>

        <div id="column-service-plan">
          <svg class="icon-plans-service"><use xlink:href="#icon-plans-service"></use></svg>
          <span [translate]="'current-situation.service-plan'"></span>
        </div>

        <div id="column-status-els">
          <span [translate]="'current-situation.els-status'"></span>
        </div>
        <div id="column-schedule">
          <svg class="icon-plans-service"><use xlink:href="#icon-time"></use></svg>
          <span [translate]="'current-situation.schedule'"></span>
        </div>
      </div>

      <cgdis-portal-scroll >
        <div *ngFor="let schedule of schedules" class="current-situation-row">
        <cgdis-portal-current-situation-selector [schedule]="schedule" [isBackupGroupActivated]="isBackupGroupActivated"></cgdis-portal-current-situation-selector>
          <cgdis-portal-current-situation-result [schedule]="schedule" (refreshSchedule)="refreshSchedule.emit()" [entity]="entity" [isBackupGroupActivated]="isBackupGroupActivated"></cgdis-portal-current-situation-result>
      </div>
      </cgdis-portal-scroll>
    </div>



  </div>

  <div class="panel" *ngIf="isMobile">
    <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>

    <cgdis-portal-mobile-expansion-panel [data]="schedules" [hasPaginator]="false"
                                         [templateRefHeader]="templateRefHeader"
                                         [templateRefBody]="templateRefBody">
      <ng-template let-oneData="oneData" #templateRefHeader>
        <cgdis-portal-current-situation-selector [schedule]="oneData"></cgdis-portal-current-situation-selector>
      </ng-template>
      <ng-template let-oneData="oneData" #templateRefBody>
        <cgdis-portal-current-situation-result-mobile [schedule]="oneData" (refreshSchedule)="refreshSchedule.emit()" [isBackupGroupActivated]="isBackupGroupActivated"></cgdis-portal-current-situation-result-mobile>
      </ng-template>
    </cgdis-portal-mobile-expansion-panel>

  </div>

  <div class="service-plan-list-footer">
    <span *ngIf="schedules.length > 0" class="total-item" [translate]="'admin.service_plan.list.totalElements'" [translateParams]="{totalElements: schedules.length}"></span>
  </div>

</ng-template>
