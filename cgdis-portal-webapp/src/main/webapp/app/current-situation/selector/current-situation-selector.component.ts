import { ChangeDetectorRef, Component, Input, OnDestroy } from '@angular/core';
import { Schedule } from '@app/operational/service-plan/schedule/schedule.model';
import { ActivatedRoute, Router } from '@angular/router';
import { DateService } from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-current-situation-selector',
  templateUrl: './current-situation-selector.component.html',
  styleUrls: ['../current-situation.component.scss'],
})
export class CurrentSituationSelectorComponent implements OnDestroy {
  /**
   * The schedule
   */
  @Input() schedule: Schedule;

  @Input() isBackupGroupActivated = false;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  /**
   * Redirect user to service plan detail
   * @param servicePlanId the service plan id
   */
  public goToServicePlanDetails(servicePlanId: number): void {
    const dateStr = this.dateService.format(this.dateService.now());
    this.router.navigate([
      'service-plan',
      'detail',
      servicePlanId,
      { selectedDate: dateStr },
    ]);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
