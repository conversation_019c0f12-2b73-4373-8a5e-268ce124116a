<ng-container *ngIf="schedule" >

  <cgdis-portal-tabs-list class="planner-timeslots">
    <cgdis-portal-tabs-list-item [ngStyle]="{'min-height': isBackupGroupActivated ? '140px' : '80px'}"
      *ngFor="let row of schedule.rows"
      [tabClasses]="['service-planner__schedule-selector','schedule', '-complete']"
      [tabId]="'schedule-tab'">

      <span  class="current-situation-status status " [ngClass]="'-' + row.status.toLowerCase() + ' schedule__status--last'">●</span>

      <span class="current-situation-service-plan">
        <cgdis-portal-icon class="icon" [icon]="'icon-' + schedule.servicePlanType.label" [iconClasses]="['-job']"></cgdis-portal-icon>
        <span class="name" (click)="goToServicePlanDetails(schedule.servicePlan.tecid)">{{schedule.servicePlanName}}</span>
        <span class="type" [translate]="schedule.servicePlanVersionAutomatic ? 'service_plan.versions.filling.automatic' : 'service_plan.versions.filling.manual'"></span>
    <ng-container *ngIf="!isMobile && schedule.backupGroup && schedule.isBackupGroupActive">
    <span class="backup-group-label" [translate]="'current-situation.active-backup'"></span>
    <span class="backup-group">{{schedule.backupGroup.name}}</span>
    </ng-container>
        <ng-container *ngIf="!isMobile && schedule.backupGroup && isBackupGroupActivated">
    <span class="type">{{schedule.backupGroup.name}}/ {{schedule.backupGroup.pagerRic}}</span>
    </ng-container>
        <ng-container *ngIf="!isMobile && schedule.optionalGroup && isBackupGroupActivated">
    <span class="type">{{schedule.optionalGroup.name}}/ {{schedule.optionalGroup.pagerRic}}</span>
    </ng-container>
      </span>
      <span class="current-situation-els-status">
        <cgdis-portal-input-els-status *ngIf="schedule.servicePlanVehiculeStatus" [value]="schedule.servicePlanVehiculeStatus" >
        </cgdis-portal-input-els-status>
      </span>

      <span *ngIf="!isMobile" class="current-situation-time" >
        <span class="current-situation-start" eportal-time-format [theTime]="row.slot.startTime" [timeFormat]="'HH:mm'"></span>
        <span class="current-situation-end" eportal-time-format [theTime]="row.slot.endTime" [timeFormat]="'HH:mm'"></span>
      </span>


    </cgdis-portal-tabs-list-item>
  </cgdis-portal-tabs-list>

</ng-container>
