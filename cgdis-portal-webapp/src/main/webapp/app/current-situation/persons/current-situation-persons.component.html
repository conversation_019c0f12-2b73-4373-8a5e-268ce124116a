<div class="service-planner__popup current-situation-persons">

  <div id="current-situation-persons-header">
    <input type="text"
           class="service-planner__popup-search"
           placeholder="{{ 'default.placeholder.search' | translate}}"
           [(ngModel)]="searchText"
           (ngModelChange)="executeSearch()">
  </div>

  <div class="small-scrollable-content" [ngStyle]="isMobile ? {'margin-top':'2rem'}: {} ">

    <cgdis-portal-spinner [loading]="loadPerson"></cgdis-portal-spinner>

    <cgdis-portal-scroll [infiniteScroll]="true" [scrollHeightSize]="'xs'" (scrollAtBottom)="executeSearchNext()">
      <ul class="unstyled-list">
        <li class="people-selector" *ngFor="let onePerson of availablePersons; index as $i">

          <div *ngIf="{expand: false, height: 'unset'} as variable">
            <label id="person-row-{{$i}}" [ngStyle]="{'height': variable.height}" (click)="selectPerson(onePerson)">

              <!-- ICON -->
              <div>
                <cgdis-portal-icon *ngIf="!onePerson.exclusive && onePerson.isProfessional" [icon]="'icon-pro'" [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="!onePerson.exclusive && onePerson.isTrainee" [icon]="'icon-stagiaire'" [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.barracked" [icon]="'icon-barracked'" [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.exclusive" [icon]="'icon-lock'" [iconClasses]="['-job']"></cgdis-portal-icon>
              </div>

              <!-- PERSON -->
              <cgdis-portal-service-plan-person-info
                [person]="onePerson"
                [showPhone]="true"
              ></cgdis-portal-service-plan-person-info>

              <div *ngIf="!isMobile" class="availabilities-interventions">
                <cgdis-portal-icon *ngIf="onePerson.ambulance" [icon]="'icon-ambulance'" [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.fire" [icon]="'icon-fire'" [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.commandment" [icon]="'icon-commandment'" [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.samu" [icon]="'icon-samu'"
                                   [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.gis" [icon]="'icon-gis'"
                                   [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.dms" [icon]="'icon-dms'"
                                   [iconClasses]="['-job']"></cgdis-portal-icon>
                <cgdis-portal-icon *ngIf="onePerson.others" [icon]="'icon-others'" [iconClasses]="['-job']"></cgdis-portal-icon>
              </div>

              <!-- AVAILABILITY DURATION -->
              <div class="availabilities-entity">
            <span [translate]="'current-situation.fromto'"
                  [translateParams]="{
                  startDateTime: currentDateTime| timeFormat:'HH:mm',
                  endDateTime: calcDateTime(currentDateTime, onePerson.availabilityDuration)|timeFormat:'HH:mm'}">
            </span>
              </div>

            </label>
          </div>
        </li>
        <li *ngIf="availablePersons == undefined || availablePersons.length === 0" class="service-plan-list-empty" style="padding-bottom: 0;">
          <span [translate]="'current-situation.person-not-found'"></span>
        </li>
      </ul>
    </cgdis-portal-scroll>

  </div>

  <div class="service-plan-list-footer" [ngStyle]="isMobile ? {'margin-top': '1rem'} : {}">
    <span *ngIf="maximumPersonAvailable > 0" class="total-item" [translate]="'admin.service_plan.list.totalElements'" [translateParams]="{totalElements: maximumPersonAvailable}"></span>
  </div>

</div>
