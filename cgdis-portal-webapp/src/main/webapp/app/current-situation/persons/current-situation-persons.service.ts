import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { IOneRestResource, IPage, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { AvailablePerson } from '../../model/person/person-availability';

@Injectable()
export class CurrentSituationPersonsService {
  /**
   * Based url to access availabilities
   * @type {string}
   */
  baseUrl = ['availabilities'];

  constructor(
    private httpClient: HttpClient,
    private _restService: RestService,
  ) {}

  /**
   * Get persons available
   * @param {number} entityId
   * @param {string} searchQuery
   * @param {string} pageSize
   * @return {Observable<IPage<AvailablePerson>>}
   */
  getPersonsAvailable(
    entityId: number,
    searchQuery: string,
    pageSize: number,
  ): Observable<IPage<AvailablePerson>> {
    const resource: IOneRestResource<IPage<AvailablePerson>> =
      this._restService.one('availabilities', 'byEntity');
    let map: Record<string, string | number> = {
      entityId: entityId,
      page: 0,
      pageSize: pageSize,
    };
    if (searchQuery !== undefined) {
      map['search'] = searchQuery;
    }
    return resource.get(map);
  }
}
