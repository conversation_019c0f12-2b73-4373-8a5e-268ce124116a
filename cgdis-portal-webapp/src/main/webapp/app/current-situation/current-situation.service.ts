import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { Entity } from '../model/entity.model';
import { Schedule } from '../operational/service-plan/schedule/schedule.model';
import { ServicePlanWithVehicleStatus } from '@app/model/service-plan-with-vehicle-status.model';

@Injectable()
export class CurrentSituationService {
  /**
   * Based url to access current situation
   * @type {string}
   */
  private baseUrl = ['current-situation'];

  constructor(private restService: RestService) {}

  /**
   * Get all entities
   * @return {Observable<Entity[]>} the entities
   */
  getEntities(): Observable<Entity[]> {
    const restResource = this.restService.all(...this.baseUrl, 'entities');
    return restResource.get().pipe(
      map((value: Entity[]) => {
        return value;
      }),
    );
  }

  /**
   * Get all schedules for the given entity
   * @param entityId the entity id
   */
  getSchedules(entityId: number): Observable<Schedule[]> {
    const restResource = this.restService.all(
      ...this.baseUrl,
      'schedules',
      String(entityId),
    );
    return restResource.get().pipe(
      map((value: Schedule[]) => {
        return value;
      }),
    );
  }

  refreshVehicleStatus(
    ids: number[],
  ): Observable<ServicePlanWithVehicleStatus[]> {
    const restResource = this.restService.all(
      ...this.baseUrl,
      'refreshVehicleStatus',
    );
    return restResource.get({ ids: ids }).pipe(
      map((value: ServicePlanWithVehicleStatus[]) => {
        return value;
      }),
    );
  }
}
