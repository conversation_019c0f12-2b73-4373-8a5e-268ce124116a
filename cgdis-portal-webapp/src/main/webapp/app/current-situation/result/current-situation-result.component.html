<ng-container *ngIf="schedule">
  <ul class="current-situation-result" [ngStyle]="{'min-height': isBackupGroupActivated ? '140px' : '80px'}">
    <li *ngFor="let position of schedule.positions;" [ngStyle]="{'height': isBackupGroupActivated ? '140px' : '80px'}">

      <!-- show person information -->
      <ng-container *ngIf="getPrestation(position, schedule.rows[0]); let prestation else addPersonToPosition">
        <div class="ressource planner__people people" >
          <span class="position">{{position.label}}</span>
          <span class="name">{{reduceName(prestation.person.firstName)}} {{prestation.person.lastName}}</span>
          <span class="matricule">{{prestation.person.cgdisRegistrationNumber}}</span>
          <span class="contact" *ngIf="isBackupGroupActivated" >
        {{prestation.person.operationalContactInformation.mobileNumber}} / {{prestation.person.operationalContactInformation.pager}}
        </span>

          <cgdis-portal-link-with-icon-delete
            class="prestation-remove"
            [roles]="['ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION']"
            *ngIf="canAddPrestation"
            [tooltipText]="'tooltip.delete' | translate"
            [smallIcon]="true"
            [rounded]="true"
            (click)="deletePrestation(prestation)">
          </cgdis-portal-link-with-icon-delete>

        </div>
      </ng-container>

      <ng-template #addPersonToPosition>

        <!-- user can add person -->
        <div class="ressource planner__people people" >
          <span>
            <cgdis-portal-button-link
              [roles]="['ROLE_PERMISSION_PDS_FILL_ADD_PERSON']"
              *ngIf="canAddPrestation; else canNotAddPerson"
              (click)="addPerson(position)">
                <span [translate]="'current-situation.add-person'"></span>
                {{position.label}} ({{getPersonAvailable(position.tecid, schedule.rows[0])}})
            </cgdis-portal-button-link>
          </span>
        </div>

        <!-- user can not add person -->
        <ng-template #canNotAddPerson>
          <div class="ressource planner__people people" >
            <span class="position">{{position.label}}</span>
            <span class="name" [translate]="'service_plan.no_data'"></span>
          </div>
        </ng-template>

      </ng-template>

    </li>
  </ul>
</ng-container>
