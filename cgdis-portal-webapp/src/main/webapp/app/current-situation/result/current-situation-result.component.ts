import { first } from 'rxjs/operators';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { Schedule } from '@app/operational/service-plan/schedule/schedule.model';
import { ServicePlanPosition } from '@app/model/service-plan-position.model';
import { ScheduleRow } from '@app/operational/service-plan/schedule/schedule-row.model';
import { Prestation } from '@app/model/prestation.model';
import { ServicePlanMemberPopupComponent } from '@app/common/modules/popup/service-plan-member/service-plan-member-popup.component';
import { ServicePlanMemberPopupData } from '@app/common/modules/popup/service-plan-member/service-plan-member-popup-data-content.model';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import {
  DateModel,
  DateService,
  DatetimeModel,
  DatetimeService,
  TimeService,
} from '@eportal/core';
import _ from 'lodash';
import { SimpleYesNoPopupComponent } from '@app/common/modules/popup/yes-no/simple-yes-no-popup.component';
import { SimpleYesNoPopupData } from '@app/common/modules/popup/yes-no/simple-yes-no-popup-data';
import { Observable, Subscription } from 'rxjs';
import { PrestationService } from '@app/common/shared/services/prestation.service';
import { BaseModel } from '@app/model/base-model.model';
import * as moment from 'moment';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { ConnectedUserService } from '@app/security/connected-user.service';

@Component({
  selector: 'cgdis-portal-current-situation-result',
  templateUrl: './current-situation-result.component.html',
  styleUrls: [
    '../current-situation.component.scss',
    'current-situation-result.component.scss',
  ],
})
export class CurrentSituationResultComponent implements OnInit, OnDestroy {
  /**
   * The schedule
   */
  @Input() schedule: Schedule;

  /**
   * The schedule
   */
  @Input() entity: BaseModel;

  @Input() isBackupGroupActivated: boolean = false;

  /**
   * Refresh the schedule when position have been updated
   */
  @Output() refreshSchedule = new EventEmitter<void>();

  public canAddPrestation = false;
  public canDeletePrestation = false;

  isMobile: boolean = false;
  isBackupModeActivated: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private popupService: SimplePopupService,
    private prestationService: PrestationService,
    private dateService: DateService,
    private timeService: TimeService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private connectedUserService: ConnectedUserService,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  public ngOnInit(): void {
    if (this.schedule) {
      this.canAddPrestation = _.includes(
        this.schedule.roles,
        'ROLE_PERMISSION_PDS_FILL_ADD_PERSON',
      );
      this.canDeletePrestation = _.includes(
        this.schedule.roles,
        'ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION',
      );
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Retrieve all persons for a specific position and time slot
   * @param {ServicePlanPosition} position: the position
   * @param {ScheduleRow} row: the time slot
   * @returns {PrestationPerson[]}: all persons
   */
  public getPrestation(
    position: ServicePlanPosition,
    row: ScheduleRow,
  ): Prestation {
    if (row == undefined) {
      console.warn('Row is null for position ', position, row);
      return null;
    }
    const now = this.dateService.now();
    let persons: Prestation[] = [];
    let slotEndDateTime: DatetimeModel;
    if (this.timeService.isBefore(row.slot.startTime, row.slot.endTime)) {
      // slot on same day
      slotEndDateTime = this.dateTimeService.of(now, row.slot.endTime);
    } else {
      // Slot on 2 days
      let endDayToUse: DateModel;
      if (
        this.dateTimeService.isAfter(
          this.dateTimeService.of(now, row.slot.startTime),
          this.dateTimeService.now(),
        )
      ) {
        // display the slot started yesterday => the end date is today
        endDayToUse = now;
      } else {
        // display the slot starting today => the end date is tomorrow
        endDayToUse = this.dateService.add(now, 1, 'day');
      }
      slotEndDateTime = this.dateTimeService.of(endDayToUse, row.slot.endTime);
    }

    for (let prestation of row.prestations) {
      if (
        prestation.position.tecid === position.tecid &&
        this.dateTimeService.equals(prestation.endDateTime, slotEndDateTime)
      ) {
        persons = persons.concat(prestation);
      }
    }
    return persons.length > 0 ? persons[persons.length - 1] : undefined;
  }

  /**
   * Add a person for a specific position and time slot
   * @param position ServicePlanPosition
   */
  public addPerson(position: ServicePlanPosition): void {
    let popupDialog = this.popupService.open(ServicePlanMemberPopupComponent, {
      panelClass: !this.isMobile
        ? ['simple-popup', 'xl-simple-popup']
        : ['simple-popup-mobile', 'xl-simple-popup-mobile'],
      data: new ServicePlanMemberPopupData({
        content: {
          schedule: this.schedule,
          exclusive: this.schedule.servicePlanExclusive,
          row: this.schedule.rows[0],
          nextSlotDuration: moment.duration(24, 'hours').asSeconds(),
          job: position,
          entity: this.entity,
          onAssignSuccess: () => this.refreshSchedule.emit(),
        },
      }),
    });
    popupDialog.afterOpened().subscribe(() => {
      document.body.style.overflow = 'hidden';
    });

    popupDialog.afterClosed().subscribe(() => {
      document.body.style.overflow = null;
    });
  }

  /**
   * Delete a prestation.
   * @param prestation the prestation to delete
   */
  public deletePrestation(prestation: Prestation): void {
    let popupDialog = this.popupService.open(SimpleYesNoPopupComponent, {
      data: new SimpleYesNoPopupData({
        title: 'service_plan.popup.delete.prestation.title',
        subtitle: 'service_plan.popup.delete.prestation.subtitle',
        message: 'service_plan.popup.delete.prestation.message',
        messageParams: {
          firstname: prestation.person.firstName,
          lastname: prestation.person.lastName,
        },
        messageHtml: true,
        onYes: () => {
          return new Observable((subscriber) => {
            this.prestationService
              .deletePrestation(prestation)
              .pipe(first())
              .subscribe(
                (resultOK) => {
                  subscriber.next(resultOK);
                  this.refreshSchedule.emit();
                },
                (error) => subscriber.error(error),
                () => subscriber.complete(),
              );
          });
        },
      }),
    });
    popupDialog.afterOpened().subscribe(() => {
      document.body.style.overflow = 'hidden';
    });

    popupDialog.afterClosed().subscribe(() => {
      document.body.style.overflow = null;
    });
  }

  /**
   * Reduce name
   * @param name
   */
  public reduceName(name: string): string {
    return name.charAt(0) + '.';
  }

  /**
   * Retrieve the number of person available for given position and slot
   * @param positionId the position id
   * @param slot the slot id
   */
  public getPersonAvailable(positionId: number, slot: ScheduleRow): string {
    if (slot && slot.numberOfPersonAvailable) {
      return String(slot.numberOfPersonAvailable[positionId].count).concat(
        slot.numberOfPersonAvailable[positionId].partialAvailability ? '*' : '',
      );
    } else {
      return '0';
    }
  }
}
