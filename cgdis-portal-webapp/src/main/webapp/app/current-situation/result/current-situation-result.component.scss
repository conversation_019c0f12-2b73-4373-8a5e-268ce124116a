@import '../sass/mixins/breakpoints';

ul {
  list-style-type: none;
}
li {
  float: left;
  //border-bottom: 1px solid #e9eef5;
}

.current-situation-result {
  flex-wrap: nowrap;
  margin-bottom: 0;
  outline: 0;
  padding-left: 0;
  min-height: 80px;
  @media (min-width: 768px) {
    display: flex;
  }
  @media (max-width: 768px) {
    display: inline-table;
  }
  @include media-breakpoint-down(lg) {
    display: table;
    -moz-column-gap: 50%;
    -webkit-column-gap: 50%;
    column-gap: 50%;
    -moz-column-count: 2;
    -webkit-column-count: 2;
    column-count: 3;
  }

  li {
    display: flex;
    align-items: center;
    height: 80px;
  }
}

.ressource {
  @media (max-width: 768px) {
    height: 55px;
  }
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  padding: 0;
  height: 80px;
  max-height: 80px;
  overflow: hidden !important;
  span {
    width: 100%;
  }
}

.position,
.matricule {
  font-weight: lighter;
}

.name {
  font-weight: 500;
}
