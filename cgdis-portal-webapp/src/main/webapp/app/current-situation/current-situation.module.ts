import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../common/shared/shared.module';
import { PageTemplateModule } from '../common/template/page-template/page-template.module';
import { CurrentSituationComponent } from './current-situation.component';
import { CURRENT_SITUATION_ROUTES } from './current-situation.route';
import { LegendModule } from '../common/modules/legend/legend.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { CurrentSituationDetailComponent } from './details/current-situation-detail.component';
import { CurrentSituationSelectorComponent } from './selector/current-situation-selector.component';
import { TabsListModule } from '../common/modules/tabs-list/tabs-list.module';
import { DatetimeModule } from '@eportal/core';
import { CurrentSituationResultComponent } from './result/current-situation-result.component';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { SimplePopupModule } from '../common/modules/popup/simple-popup.module';
import { CurrentSituationPersonsComponent } from './persons/current-situation-persons.component';
import { FormsModule } from '@angular/forms';
import { CurrentSituationPersonContactInformationComponent } from './person-contact-information/current-situation-person-contact-information.component';
import { FormModule } from '../common/modules/form-module/form.module';
import { MatExpansionModule } from '@angular/material/expansion';
import { InputModule } from '@app/common/modules/input/input.module';
import { CurrentSituationResultMobileComponent } from './current-situation-result-mobile/current-situation-result-mobile.component';
import { MobileExpansionPanelModule } from '@app/common/template/mobile-expansion-panel/mobile-expansion-panel.module';
import { ServicePlanStateWarningModule } from '@app/common/modules/service-plan-state/warning/service-plan-state-warning.module';
import { ScrollModule } from '@app/common/modules/scroll/scroll.module';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';

@NgModule({
  imports: [
    SharedModule,
    FormsModule,
    FormModule,
    PageTemplateModule,
    LegendModule,
    NgxSelectModule,
    TabsListModule,
    DatetimeModule,
    NgScrollbarModule,
    SimplePopupModule,
    RouterModule.forChild(CURRENT_SITUATION_ROUTES),
    MatExpansionModule,
    InputModule,
    MobileExpansionPanelModule,
    ServicePlanStateWarningModule,
    ScrollModule,
    EntityFilterModule,
  ],
  declarations: [
    CurrentSituationComponent,
    CurrentSituationDetailComponent,
    CurrentSituationSelectorComponent,
    CurrentSituationResultComponent,
    CurrentSituationPersonsComponent,
    CurrentSituationPersonContactInformationComponent,
    CurrentSituationResultMobileComponent,
  ],
  exports: [
    CurrentSituationComponent,
    CurrentSituationPersonContactInformationComponent,
  ],
})
export class CurrentSituationModule {}
