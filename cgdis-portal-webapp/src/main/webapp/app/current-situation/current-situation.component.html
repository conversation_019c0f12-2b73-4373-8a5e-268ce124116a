<section class="section" *ngIf="primaryBaseModel">

  <cgdis-portal-page-template>

    <!-- Header -->
    <cgdis-portal-page-header [titleKey]="'current-situation.title'" [subtitleAlign]="true">
      <div page-header-subtitle >
        <!-- Entity filter -->
        <cgdis-portal-entity-filter [entityPermission]="['ROLE_PERMISSION_PDS_LIST_VIEW', 'ROLE_PERMISSION_CURRENT_SITUATION']" [underDisable]="true" [initPrimaryEntity]="true" (filterSelected)="setEntity($event.entity)"></cgdis-portal-entity-filter>

      </div>
    </cgdis-portal-page-header>

    <cgdis-portal-service-plan-state-warning-for-entity [entityId]="selectedEntity?.tecid"></cgdis-portal-service-plan-state-warning-for-entity>

    <!-- List of current time slots -->
    <cgdis-portal-current-situation-detail [loading]="loading" [schedules]="schedules" (refreshSchedule)="getAllSchedule()" [entity]="selectedEntity" [isBackupGroupActivated]="isBackupGroupActivated"></cgdis-portal-current-situation-detail>

    <!-- Legend -->
    <cgdis-portal-legend [items]="legendItems" [legendClasses]="['-inline']"></cgdis-portal-legend>
    <ng-container *ngIf="(schedules != undefined && schedules.length != 0) && !isMobile">
      <!-- Header-->
      <cgdis-portal-page-header class="mobile-situation-person" [titleKey]="'current-situation.persons'" [subtitleAlign]="true" *ngIf="selectedEntity"></cgdis-portal-page-header>

      <!-- List of available persons -->
      <cgdis-portal-current-situation-persons [entityId]="selectedEntity.tecid"></cgdis-portal-current-situation-persons>

      <!-- Legend -->
      <cgdis-portal-legend *ngIf="!isMobile" [items]="legendPersonsItems" [legendClasses]="['-inline']"></cgdis-portal-legend>
      <div *ngIf="isMobile" style="margin-top: 2rem">
        <cgdis-portal-legend class="bottom-tab" *ngIf="isMobile " [items]="legendPersonsItemsOne">      </cgdis-portal-legend>
      </div>
    </ng-container>

  </cgdis-portal-page-template>
</section>

