import {
  ChangeDetectionStrategy,
  Component,
  Injector,
  OnInit,
  ViewContainerRef,
} from '@angular/core';
import { ConfigService, ERROR_MESSAGE_SERVICE } from '@eportal/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { AuthenticationService } from '@app/security/authentication.service';
import { TokenLoginService } from '@app/login/token-login.service';
import { ErrorMessageService } from '@app/common/modules/error-management/services/error-message.service';
import { askFaceTouchId } from '@app/app.module';
import { interval } from 'rxjs';
import { take } from 'rxjs/operators';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';

@Component({
  selector: 'cgdis-portal-app-mobile',
  templateUrl: './app-mobile.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppMobileComponent implements OnInit {
  constructor(
    //private toastr: ToastsManager,
    private vcr: ViewContainerRef,
    private injector: Injector,
  ) {
    // this.toastr.setRootViewContainerRef(vcr);
  }

  ngOnInit() {
    document.addEventListener('resume', () => {
      sessionStorage.clear();
      this.relaunchAuthentication();
    });

    document.addEventListener('pause', () => {
      let tokenLoginService = this.getTokenLoginService();
      tokenLoginService.setPause(true);
    });

    interval(20000).subscribe({
      next: () => {
        let tokenLoginService = this.getTokenLoginService();
        if (tokenLoginService.mustRefreshToken()) {
          tokenLoginService
            .loadTokenFromRefreshToken()
            .pipe(take(1))
            .subscribe({
              next: () => {},
              error: () => {
                this.injector
                  .get(ToastService)
                  .error('error.authentication.expired');
                this.injector.get(AuthenticationService).logout(true, false);
              },
            });
        }
      },
    });
  }

  relaunchAuthentication() {
    let configService = this.getConfigService();
    let connectedUserService = this.getConnectedUserService();
    let authenticationService = this.getAuthenticationService();
    let tokenLoginService = this.getTokenLoginService();
    let errorMessage = this.getErrorMessageService();

    const url = window.localStorage.getItem('url');
    const code = window.localStorage.getItem('code');
    if (
      (url != undefined && code != undefined) ||
      tokenLoginService.hasToken()
    ) {
      let promise = new Promise((resolve, reject) => {});

      askFaceTouchId(
        configService,
        connectedUserService,
        authenticationService,
        tokenLoginService,
        errorMessage,
        promise,
        0,
      );
    } else {
      authenticationService.goToLoginPage();

      // throw new Error('No url, code or refresh token found');
    }
  }

  private getErrorMessageService() {
    let errorMessage = this.injector.get(
      ERROR_MESSAGE_SERVICE,
    ) as ErrorMessageService;
    return errorMessage;
  }

  private getTokenLoginService() {
    let tokenLoginService = this.injector.get(TokenLoginService);
    return tokenLoginService;
  }

  private getAuthenticationService() {
    let authenticationService = this.injector.get(AuthenticationService);
    return authenticationService;
  }

  private getConnectedUserService() {
    let connectedUserService = this.injector.get(ConnectedUserService);
    return connectedUserService;
  }

  private getConfigService() {
    let configService = this.injector.get(ConfigService);
    return configService;
  }
}
