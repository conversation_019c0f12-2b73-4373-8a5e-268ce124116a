import { NgModule } from '@angular/core';
import { ErrorManagementComponent } from './error-management.component';
import { ErrorManagementListComponent } from './list/error-management-list.component';
import { RouterModule } from '@angular/router';
import { ERROR_MANAGEMENT_ROUTES } from './error-management.route';
import { SharedModule } from '../common/shared/shared.module';
import { EpDatatableModule } from '@eportal/components';
import { DatatableModule } from '../common/modules/datatable/datatable.module';
import { DatetimeModule } from '@eportal/core';
import { PageTemplateModule } from '../common/template/page-template/page-template.module';
import { SimpleTableModule } from '../common/modules/simple-table/simple-table.module';
import { ErrorManagementDetailComponent } from './detail/error-management-detail.component';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    RouterModule.forChild(ERROR_MANAGEMENT_ROUTES),
    DatatableModule,
    EpDatatableModule,
    DatetimeModule,
    SimpleTableModule,
  ],
  declarations: [
    ErrorManagementComponent,
    ErrorManagementListComponent,
    ErrorManagementDetailComponent,
  ],
})
export class ErrorManagementModule {}
