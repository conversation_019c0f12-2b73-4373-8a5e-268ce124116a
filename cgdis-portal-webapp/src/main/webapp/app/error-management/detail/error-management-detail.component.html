<div class="error-detail-row accordion__panel" style="word-wrap: break-word;">
  <div class="row ">
    <div [ngClass]="isMobile ? 'col-3' : 'col-2'">
      {{'error-management.list.header.type' | translate}}
    </div>
    <div [ngClass]="isMobile ? 'col-9' : 'col-10'">
      {{'error-management.type.'+errorMessage.type | translate}}
    </div>
  </div>
  <div class="row">
    <div [ngClass]="isMobile ? 'col-3' : 'col-2'">
      {{'error-management.list.header.status' | translate}}
    </div>
    <div [ngClass]="isMobile ? 'col-9' : 'col-10'">
      {{'error-management.status.'+errorMessage.status | translate}}
    </div>
  </div>

  <div class="row">
    <div [ngClass]="isMobile ? 'col-3' : 'col-2'">
      {{'error-management.list.header.creationdate' | translate}}
    </div>
    <div [ngClass]="isMobile ? 'col-9' : 'col-10'">
      {{ formatDateTime(errorMessage.creationDate) }}
    </div>
  </div>

  <div class="row">
    <div [ngClass]="isMobile ? 'col-3' : 'col-2'">
      {{'error-management.list.header.cause' | translate}}
    </div>
    <div [ngClass]="isMobile ? 'col-9' : 'col-10'" style="text-wrap: auto">
      {{errorMessage.cause}}
    </div>
  </div>

  <div class="row">
    <div [ngClass]="isMobile ? 'col-4' : 'col-2'">
      {{'error-management.list.header.message' | translate}}
    </div>
    <pre [ngClass]="isMobile ? 'col-8' : 'col-10'" style="max-height: 20em;background-color: #f8f8f8;">
      {{jsonErrorMessage | json}}
    </pre>
  </div>
</div>
