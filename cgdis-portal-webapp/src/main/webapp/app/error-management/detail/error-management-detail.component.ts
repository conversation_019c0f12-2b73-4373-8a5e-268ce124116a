import {
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { ErrorManagementMessage } from '../error-management-message.model';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-error-management-detail',
  templateUrl: './error-management-detail.component.html',
})
export class ErrorManagementDetailComponent implements OnInit, OnDestroy {
  /**
   * The person
   */
  @Input() errorMessage: ErrorManagementMessage;

  jsonErrorMessage: any;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private datetimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  formatDateTime(datetime: DatetimeModel) {
    return this.datetimeService.format(datetime, 'DD/MM/YYYY HH:mm:ss');
  }

  ngOnInit(): void {
    this.jsonErrorMessage = JSON.parse(this.errorMessage.message);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
