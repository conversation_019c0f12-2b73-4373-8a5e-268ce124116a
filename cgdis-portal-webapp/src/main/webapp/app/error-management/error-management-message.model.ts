import { DatetimeModel } from '@eportal/core';
import { BaseModel } from '../model/base-model.model';

export class ErrorManagementMessage extends BaseModel {
  /**
   * The Type.
   */
  type: string;

  /**
   * The Status.
   */
  status: string;

  /**
   * The Creation date.
   */
  creationDate: DatetimeModel;

  /**
   * The Update datetime.
   */
  updateDatetime: DatetimeModel;

  /**
   * The Message.
   */
  message: string;

  /**
   * The Cause.
   */
  cause?: string;

  /**
   * The Object identifier.
   */
  objectIdentifier?: string;

  /**
   * can be resent
   */
  canbeResent: boolean;

  constructor(args: ErrorManagementMessage) {
    super(args);
    this.type = args.type;
    this.status = args.status;
    this.creationDate = args.creationDate;
    this.updateDatetime = args.updateDatetime;
    this.message = args.message;
    this.cause = args.cause;
    this.objectIdentifier = args.objectIdentifier;
    this.canbeResent = args.canbeResent;
  }
}
