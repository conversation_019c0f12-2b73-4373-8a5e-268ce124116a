import { Injectable } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { ErrorManagementMessage } from '../error-management-message.model';
import { Observable } from 'rxjs';
import { ToastService } from '../../common/shared/toasts/CGDISToastService';

@Injectable()
export class ErrorManagementListService extends CgdisDatatableService<ErrorManagementMessage> {
  // TODO split datatable service
  private baseUrl = ['error-management'];

  constructor(
    private alertService: ToastService,
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(restService.all(...this.baseUrl, 'search'));
  }

  getAllTypes(): Observable<string[]> {
    return this.restService.all<string>(...this.baseUrl, 'types').get();
  }

  deleteMessage(tecid: number) {
    this.restService
      .one(...this.baseUrl, String(tecid))
      .delete()
      .subscribe((value: boolean) => {
        this.alertService.success('error-management.delete.success');
        super.search();
      });
  }

  resendMessage(tecid: number): Observable<any> {
    return this.restService
      .one(...this.baseUrl, String(tecid), 'resend')
      .update({});
  }
}
