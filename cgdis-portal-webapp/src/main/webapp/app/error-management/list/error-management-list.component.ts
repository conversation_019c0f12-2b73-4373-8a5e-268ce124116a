import { take } from 'rxjs/operators';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { ErrorManagementListService } from './error-management-list.service';
import _ from 'lodash';
import { ErrorManagementMessage } from '../error-management-message.model';
import { DateService, DatetimeModel, DatetimeService } from '@eportal/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { ErrorManagmentStatusEnum } from '@app/model/error-managment-status.enum';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-error-management-list',
  templateUrl: './error-management-list.component.html',
  providers: [ErrorManagementListService],
})
export class ErrorManagementListComponent implements OnInit, OnDestroy {
  allTypes: FieldGroupOption<string, any>[];
  errorStatus: FieldOption<string>[];
  showFilter = false;
  creationDateFilterConfig = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public errorManagementListService: ErrorManagementListService,
    private alertService: ToastService,
    private datetimeService: DatetimeService,
    public dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 768px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  formatDateTime(datetime: DatetimeModel) {
    return this.datetimeService.format(datetime, 'DD/MM/YYYY HH:mm:ss');
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.creationDateFilterConfig = new FilterConfig({
      inUrl: false,
      operator: SearchOperator.eq,
      defaultValue: this.datetimeService.now(),
    });

    this.loadErrorStatus();
    this.errorManagementListService.getAllTypes().subscribe((values) => {
      this.allTypes = _.map(
        values,
        (oneValue) =>
          new FieldGroupOption({
            value: oneValue,
            I18NLabel: 'error-management.type.' + oneValue,
          }),
      );
    });

    this.errorManagementListService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.errorManagementListService.getNumberOfFilters();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.errorManagementListService.getNumberOfFilters();
  }

  deleteErrorMessage(row: ErrorManagementMessage) {
    this.errorManagementListService.deleteMessage(row.tecid);
  }

  resendErrorMessage(row: ErrorManagementMessage) {
    this.errorManagementListService
      .resendMessage(row.tecid)
      .pipe(take(1))
      .subscribe(() => {
        let that = this;
        // DEVCGDIS-727: timeout does not solve the issue, but (sometimes) do the trick. The internal gateway should tell us when to reload the table.
        // if the error message has already been deleted, we should not have an error alert
        setTimeout(function () {
          that.alertService.success('error-management.resend.success');
          that.errorManagementListService.search();
        }, 0);
      });
  }

  /**
   * Local error status into select list for filter
   */
  private loadErrorStatus(): void {
    let allStatus: string[] = [];

    for (let n in ErrorManagmentStatusEnum) {
      allStatus.push(
        <string>(
          ErrorManagmentStatusEnum[n as keyof typeof ErrorManagmentStatusEnum]
        ),
      );
    }

    this.errorStatus = _.map(allStatus, (oneType) => {
      return new FieldGroupOption({
        I18NLabel: 'error-management.status.' + oneType,
        value: oneType,
      });
    });
  }
}
