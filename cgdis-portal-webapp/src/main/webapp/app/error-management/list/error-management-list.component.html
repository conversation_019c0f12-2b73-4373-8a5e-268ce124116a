<ng-container >

  <cgdis-portal-page-template [ngClass]="'error-management'">

    <cgdis-portal-page-header [titleKey]="'error-management.title'" ></cgdis-portal-page-header>

    <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
      <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
      <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
    </cgdis-portal-button-link>
    <ng-container *ngIf="isMobile">
      <div class="row search-filter" [hidden]="!showFilter">
        <div class="col-md-2">
          <label class="form-label" [translate]="'error-management.list.header.type'"></label>
          <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [filterName]="'type'" [allowClear]="true" [possibleValues]="allTypes"
                                                [datatableService]="errorManagementListService"></cgdis-portal-datatable-select-filter>
        </div>
        <div class="col-md-2">
          <label class="form-label" [translate]="'error-management.list.header.status'"></label>
          <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [possibleValues]="errorStatus" [filterName]="'status'"
                                                [datatableService]="errorManagementListService"></cgdis-portal-datatable-select-filter>
        </div>
        <div class="col-md-2">
          <label class="form-label" [translate]="'error-management.list.header.creationdate'"></label>
          <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="error-management-datepicker-filter"

                                                    [filterName]="'creationDate'"
                  [filterConfig]="creationDateFilterConfig"
                  [datatableService]="errorManagementListService"></cgdis-portal-datatable-datepicker-filter>

        </div>
      </div>
    </ng-container>
    <cgdis-portal-panel>

      <!-- error management list table-->
      <cgdis-portal-cgdisdatatable
        [datatableService]="errorManagementListService"
        [id]="'error-management-list-table-Id'"
        [class]="'error-management__table'"
        [sorts]="[{dir:'desc',prop:'creationDate'}]"
        [showDetails]="'ALWAYS'"
      >

        <ng-template #template let-row="row">
          <div class="error-detail-container">
            <cgdis-portal-error-management-detail [errorMessage]="row"></cgdis-portal-error-management-detail>
          </div>
        </ng-template>


        <!-- name (string) -->
        <ep-datatable-column [columnName]="'type'" [flexGrow]="2">
          <ng-template epDatatableHeader>
            {{('error-management.list.header.type' | translate) | defaultValue:'-'}}
          </ng-template>
          <ng-template epDatatableCell let-context>
            {{('error-management.type.'+context.value | translate) | defaultValue:'-'}}
          </ng-template>

          <ng-template epDatatableFilter>
          <cgdis-portal-datatable-select-filter [filterName]="'type'" [allowClear]="true" [possibleValues]="allTypes"
          [datatableService]="errorManagementListService"></cgdis-portal-datatable-select-filter>
          </ng-template>
        </ep-datatable-column>

        <!-- status -->
        <ep-datatable-column [columnName]="'status'" [flexGrow]="1">
          <ng-template epDatatableHeader>
            {{('error-management.list.header.status' | translate) | defaultValue:'-'}}
          </ng-template>

          <ng-template epDatatableCell let-context>
            {{('error-management.status.'+context.value | translate) | defaultValue:'-'}}
          </ng-template>

          <ng-template epDatatableFilter >
            <cgdis-portal-datatable-select-filter [allowClear]="true" [possibleValues]="errorStatus" [filterName]="'status'"
                                                  [datatableService]="errorManagementListService"></cgdis-portal-datatable-select-filter>
          </ng-template>

        </ep-datatable-column>

        <!-- creation date -->
        <ep-datatable-column [columnName]="'creationDate'" [flexGrow]="2">
          <ng-template epDatatableHeader>
            {{ 'error-management.list.header.creationdate' | translate }}
          </ng-template>

          <ng-template epDatatableCell let-context>
            {{ formatDateTime(context.value)  | defaultValue:'-'}}
          </ng-template>

          <ng-template epDatatableFilter >
            <cgdis-portal-datatable-datepicker-filter
              [filterName]="'creationDate'"
              [filterConfig]="creationDateFilterConfig"
              [datatableService]="errorManagementListService"></cgdis-portal-datatable-datepicker-filter>
          </ng-template>
        </ep-datatable-column>

        <ep-datatable-column [columnName]="'cause'" [flexGrow]="2">
          <ng-template epDatatableHeader>
            {{ 'error-management.list.header.cause' | translate }}
          </ng-template>
          <ng-template epDatatableCell  let-context>
            <div [innerHTML]="context.value"></div>
          </ng-template>
        </ep-datatable-column>


        <ep-datatable-column [columnName]="'message'" [flexGrow]="1">
          <ng-template epDatatableHeader>
            {{'error-management.list.header.message' | translate}}
          </ng-template>

          <ng-template epDatatableCell let-context>
            {{context.value | defaultValue:'-'}}
          </ng-template>

        </ep-datatable-column>

        <!-- Actions -->
        <ep-datatable-column [columnName]="'actions'" [sortable]="false" [flexGrow]="1">
          <ng-template epDatatableHeader>
            {{ 'error-management.list.header.actions' | translate }}
          </ng-template>

          <ng-template let-context epDatatableCell [disableClickOnCell]="true">
            <cgdis-portal-simple-table-button   (onclick)="deleteErrorMessage(context.row)">
              <cgdis-portal-link-with-icon-delete
                [rounded]="true"
                [tooltipText]="'tooltip.delete' | translate"
                [smallIcon]="true"></cgdis-portal-link-with-icon-delete>
            </cgdis-portal-simple-table-button>
            <cgdis-portal-simple-table-button   (onclick)="context.row.canBeResent ? resendErrorMessage(context.row): null">
              <cgdis-portal-link-with-icon
                [rounded]="true"
                [icon]="'icon-reload'"
                [tooltipText]="'tooltip.refresh' | translate"
                [smallIcon]="true"
                [linkDisabled]="!context.row.canBeResent">
              </cgdis-portal-link-with-icon>
            </cgdis-portal-simple-table-button>
          </ng-template>
        </ep-datatable-column>

      </cgdis-portal-cgdisdatatable>

    </cgdis-portal-panel>

  </cgdis-portal-page-template>
</ng-container>
