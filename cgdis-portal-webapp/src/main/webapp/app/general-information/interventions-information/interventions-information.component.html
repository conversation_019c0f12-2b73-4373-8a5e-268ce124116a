<div class="accordion__panel">

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.interventions.number'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'interventionNumber'" [filterConfig]="filterLike" [datatableService]="interventionsService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.interventions.keyword'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'interventionKeyword'" [filterConfig]="filterLike" [datatableService]="interventionsService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.interventions.alarm'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [customFormControl]="alarmDateFormControl" [filterName]="'alarmDatetime'" [filterConfig]="alarmDateFilterConfig" [datatableService]="interventionsService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.interventions.start'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [readOnly]="true" [customFormControl]="startDateFormControl" [filterName]="'startDatetime'" [filterConfig]="startDateFilterConfig" [datatableService]="interventionsService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.interventions.end'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [readOnly]="true" [customFormControl]="endDateFormControl" [filterName]="'endDatetime'" [filterConfig]="endDateFilterConfig" [datatableService]="interventionsService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [datatableService]="interventionsService"
    [sorts]="[{dir:'asc',prop:'interventionNumber'}]"
    [id]="'general-information-interventions-table-Id'"
    [showDetails]="'MOBILE'">

    <ng-template #template let-row="row">
      <div class="error-detail-container">
        <cgdis-portal-interventions-information-detail [intervention]="row"></cgdis-portal-interventions-information-detail>
      </div>
    </ng-template>

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'personId'"
                                          [customFormControl]="formControl"
                                          [datatableService]="interventionsService"></cgdis-portal-datatable-number-filter>




    <ep-datatable-column [columnName]="'interventionNumber'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.interventions.number' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'interventionNumber'" [filterConfig]="filterLike" [datatableService]="interventionsService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'interventionKeyword'" [flexGrow]="1.5">
      <ng-template epDatatableHeader>
        {{'general_information.activity.interventions.keyword' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'interventionKeyword'" [filterConfig]="filterLike" [datatableService]="interventionsService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'alarmDatetime'" [flexGrow]="1.5">
      <ng-template epDatatableHeader>
        {{'general_information.activity.interventions.alarm' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [customFormControl]="alarmDateFormControl" [filterName]="'alarmDatetime'" [filterConfig]="alarmDateFilterConfig" [datatableService]="interventionsService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.alarmDatetime)}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'startDatetime'" [flexGrow]="1.5">
      <ng-template epDatatableHeader>
        {{'general_information.activity.interventions.start'.concat(isMobile ? '_mobile' : '') | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [readOnly]="true" [customFormControl]="startDateFormControl" [filterName]="'startDatetime'" [filterConfig]="startDateFilterConfig" [datatableService]="interventionsService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.startDatetime)}}
      </ng-template>
    </ep-datatable-column>


    <ep-datatable-column [columnName]="'endDatetime'" [flexGrow]="1.5">
      <ng-template epDatatableHeader>
        {{'general_information.activity.interventions.end'.concat(isMobile ? '_mobile' : '') | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [readOnly]="true" [customFormControl]="endDateFormControl" [filterName]="'endDatetime'" [filterConfig]="endDateFilterConfig" [datatableService]="interventionsService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.endDatetime)}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'allowance'" [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        <span *ngIf="!isMobile; else euroIcon">
          {{'general_information.activity.allowance' | translate}}
        </span>
        <ng-template #euroIcon>
          <svg class="icon-euro -small ng-star-inserted">
            <use xlink:href="#icon-euro"></use>
          </svg>
        </ng-template>
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-number-filter [hidden]="true" [filterName]="'allowance'" [filterConfig]="filterConfigAllowance" [datatableService]="interventionsService"></cgdis-portal-datatable-number-filter>
      </ng-template>
      <ng-template epDatatableCell>
        {{this.enableAllowance ? "N/A" : "-"}}
      </ng-template>
    </ep-datatable-column>

  </cgdis-portal-cgdisdatatable>
</div>
