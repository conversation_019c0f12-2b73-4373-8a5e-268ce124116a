import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { InterventionsInformationService } from './interventions-information.service';
import { DateModel, DateService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { GeneralInformationService } from '../general-information.service';
import { PRO_VOLUNTEER_BOTH } from '@app/general-information/general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-interventions-information',
  templateUrl: './interventions-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [InterventionsInformationService, GeneralInformationService],
})
export class InterventionsInformationComponent
  implements OnInit, OnChanges, OnDestroy
{
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];

  startDateFilterConfig: FilterConfig;
  endDateFilterConfig: FilterConfig;
  alarmDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });
  formControl = new UntypedFormControl();
  startDateFormControl: UntypedFormControl = new UntypedFormControl();
  endDateFormControl: UntypedFormControl = new UntypedFormControl();
  alarmDateFormControl: UntypedFormControl = new UntypedFormControl();
  filterConfigAllowance = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  enableAllowance: boolean;
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public interventionsService: InterventionsInformationService,
    private dateService: DateService,
    private generalInformationService: GeneralInformationService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    if (this.startDateFilterConfig == undefined) {
      this.startDateFilterConfig = new FilterConfig({
        operator: SearchOperator.eq,
        inUrl: false,
        defaultValue: this.startDate,
      });
    }
    if (this.endDateFilterConfig == undefined) {
      this.endDateFilterConfig = new FilterConfig({
        operator: SearchOperator.eq,
        inUrl: false,
        defaultValue: this.endDate,
      });
    }
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);
    this.startDateFormControl.setValue(this.startDate);
    this.alarmDateFormControl.setValue(this.startDate);
    this.endDateFormControl.setValue(this.endDate);
    this.loadProVolunteerChoice();

    this.interventionsService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.interventionsService.getNumberOfFilters([
          'personId',
          'alarmDatetime',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.interventionsService.getNumberOfFilters([
      'personId',
      'alarmDatetime',
    ]);
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.startDate || changes.endDate) {
      if (
        changes.startDate.previousValue === undefined &&
        changes.endDate.previousValue === undefined
      ) {
        this.startDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.startDate,
          );
        this.endDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.endDate,
          );
      } else if (
        (changes.startDate &&
          changes.startDate.previousValue !== changes.startDate.currentValue) ||
        (changes.endDate &&
          changes.endDate.previousValue !== changes.endDate.currentValue)
      ) {
        this.startDateFormControl.setValue(this.startDate);
        this.endDateFormControl.setValue(this.endDate);
      }
    }
    if (
      changes.proOrAndVolunteer &&
      changes.proOrAndVolunteer.previousValue !==
        changes.proOrAndVolunteer.currentValue
    ) {
      this.loadProVolunteerChoice();
    }
  }

  private loadProVolunteerChoice(): void {
    let result: Map<string, boolean> =
      this.generalInformationService.buildProVolunteerChoice(
        this.proOrAndVolunteer,
      );
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.enableAllowance = false;
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.enableAllowance = true;
    } else if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.enableAllowance = true;
    }
  }
}
