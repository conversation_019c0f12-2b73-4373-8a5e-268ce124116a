import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewEncapsulation,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { Subscription } from 'rxjs';
import { PrestationStatistics } from '@app/model/prestation-statistics.model';
import { PieChartData } from '@app/common/modules/highcharts/pie/pie-chart.model';
import { LegendItem } from '@app/common/modules/legend/legend-item';
import { TranslateService } from '@ngx-translate/core';
import { GeneralInformationService } from '../../general-information.service';
import { InterventionsInformationSummaryService } from '@app/general-information/interventions-information/summary/interventions-information-summary.service';
import { PRO_VOLUNTEER_BOTH } from '@app/general-information/general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { take } from 'rxjs/operators';
import * as _ from 'lodash';

@Component({
  selector: 'cgdis-portal-interventions-information-summary',
  templateUrl: './interventions-information-summary.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    InterventionsInformationSummaryService,
    GeneralInformationService,
  ],
})
export class InterventionsInformationSummaryComponent
  implements OnInit, OnDestroy, OnChanges
{
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];

  reloadChart = false;
  public chartTitle: string;
  public legendItems: LegendItem[];
  public subscriptions: Subscription[] = [];
  public pieChartData: PieChartData[] = [];
  public chartDisplayParams: any;

  fireValue = 0;
  ambulanceValue = 0;
  commandmentValue = 0;
  samuValue = 0;
  gisValue = 0;
  dmsValue = 0;
  otherValue = 0;
  isMobile: boolean = false;

  constructor(
    private service: InterventionsInformationSummaryService,
    private generalInformationService: GeneralInformationService,
    private cd: ChangeDetectorRef,
    private dateService: DateService,
    private translateService: TranslateService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.chartDisplayParams = {
      margin: [10, 10, 10, 10],
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0,
    };
    let prefix = '';

    // Init chart legend
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'dashboard.members.chart.' + prefix + 'fire',
        classes: ['-incomplete'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'dashboard.members.chart.' + prefix + 'ambulance',
        classes: ['-degraded'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'dashboard.members.chart.' + prefix + 'others',
        classes: ['-complete'],
      }),
    ];
    // this.loadStatistics();
    this.subscriptions.push(
      this.translateService.onLangChange.subscribe(() => {
        this.chartTitle = this.capitalizeFirstLetter(
          this.translateService.instant(
            'date.months.' +
              this.dateService.firstDayOfMonth(this.startDate).month +
              '.abr',
          ),
        );
      }),
    );
  }

  /**
   * Capitalize the first letter of a string
   * @param {string} string
   * @returns {string} String
   */
  private capitalizeFirstLetter(string: string): string {
    if (string != undefined) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    } else {
      return string;
    }
  }

  loadStatistics(): void {
    this.pieChartData = [];
    this.reloadChart = true;

    let result = this.generalInformationService.buildProVolunteerChoice(
      this.proOrAndVolunteer,
    );

    let isPro = result.get(PRO_VOLUNTEER_BOTH.is_professional);

    // Init chart title
    this.chartTitle = this.capitalizeFirstLetter(
      this.translateService.instant(
        'date.months.' +
          this.dateService.firstDayOfMonth(this.startDate).month +
          '.abr',
      ),
    );

    this.service
      .getStatistics(this.personId, this.startDate, this.endDate, isPro)
      .pipe(take(1))
      .subscribe((value: PrestationStatistics) => {
        // Init chart data
        this.fireValue =
          (value.repartition.fire ? value.repartition.fire : 0) * 60;
        this.ambulanceValue =
          (value.repartition.ambulance ? value.repartition.ambulance : 0) * 60;
        this.commandmentValue =
          (value.repartition.commandment ? value.repartition.commandment : 0) *
          60;
        this.gisValue =
          (value.repartition.gis ? value.repartition.gis : 0) * 60;
        this.dmsValue =
          (value.repartition.dms ? value.repartition.dms : 0) * 60;
        this.samuValue =
          (value.repartition.samu ? value.repartition.samu : 0) * 60;
        this.otherValue =
          (value.repartition.others ? value.repartition.others : 0) * 60;

        let total =
          this.fireValue +
          this.ambulanceValue +
          this.commandmentValue +
          this.gisValue +
          this.samuValue +
          this.dmsValue +
          this.otherValue;

        let firePercentage =
          this.fireValue > 0 ? _.round((100 * this.fireValue) / total, 0) : 0;
        let ambulancePercentage =
          this.ambulanceValue > 0
            ? _.round((100 * this.ambulanceValue) / total, 0)
            : 0;
        let commandmentPercentage =
          this.commandmentValue > 0
            ? _.round((100 * this.commandmentValue) / total, 0)
            : 0;
        let samuPercentage =
          this.samuValue > 0 ? _.round((100 * this.samuValue) / total, 0) : 0;
        let gisPercentage =
          this.gisValue > 0 ? _.round((100 * this.gisValue) / total, 0) : 0;
        let dmsPercentage =
          this.dmsValue > 0 ? _.round((100 * this.dmsValue) / total, 0) : 0;
        let otherPercentage =
          this.otherValue > 0 ? _.round((100 * this.otherValue) / total, 0) : 0;

        this.pieChartData.push(new PieChartData({ type: '', value: 0 }));
        this.pieChartData.push(
          new PieChartData({ type: 'fire', value: firePercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'ambulance', value: ambulancePercentage }),
        );
        this.pieChartData.push(
          new PieChartData({
            type: 'commandment',
            value: commandmentPercentage,
          }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'samu', value: samuPercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'gis', value: gisPercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'dms', value: dmsPercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'others', value: otherPercentage }),
        );
        // adding of a fourth value because pie chart have 4 colors before the 'no-data' one
        this.pieChartData.push(new PieChartData({ type: '', value: 0 }));

        this.reloadChart = false;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Convert seconds to duration
   * @param seconds
   */
  public convertSecondsToDuration(seconds: number): string {
    return this.getHours(seconds) + 'h' + this.getMinutes(seconds);
  }

  /**
   * Extract number of minutes in given number of seconds
   * @param seconds
   */
  private getMinutes(seconds: number): string {
    if (seconds <= 0 || seconds === undefined) {
      return '00';
    }

    seconds %= 3600;
    let minutes = Math.floor(seconds / 60);

    if (minutes < 10) {
      return '0' + minutes;
    }
    return '' + minutes;
  }

  /**
   * Get number of hours in given number of seconds
   * @param seconds
   */
  private getHours(seconds: number): number {
    return seconds > 0 ? Math.floor(seconds / 3600) : 0;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes.startDate &&
        changes.startDate.previousValue !== changes.startDate.currentValue) ||
      (changes.endDate &&
        changes.endDate.previousValue !== changes.endDate.currentValue) ||
      (changes.proOrAndVolunteer &&
        changes.proOrAndVolunteer.previousValue !==
          changes.proOrAndVolunteer.currentValue)
    ) {
      // this.loadStatistics();
    }
  }
}
