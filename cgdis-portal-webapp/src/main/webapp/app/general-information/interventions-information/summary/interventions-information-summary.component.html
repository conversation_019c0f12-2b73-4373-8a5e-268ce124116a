<div class="row justify-content-center align-items-center member-summary">

    <div class="col-md-4" *ngIf="!isMobile">
    <div class="chart" *ngIf="!reloadChart">
      <div id="performance-chart_" class="member-summary">
        <cgdis-portal-pie-chart
          [data]="pieChartData"
          [chartDisplayParam]="chartDisplayParams"
          [styleClass]="['inherit-parent-height']"
          [middleText]="chartTitle"
          [innerSize]="'90%'"
          [textPosition]="5"
          [unitText]="'dashboard.members.chart.tooltip'"
          [unitFormat]="'%'"></cgdis-portal-pie-chart>
      </div>
        </div>
    </div>

  <div class="col-md-2">
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="'general_information.availability.type.fire'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value margin-top">
              <span>{{convertSecondsToDuration(this.fireValue)}}</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
  </div>

  <div class="col-md-2">
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="'general_information.availability.type.ambulance'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value margin-top">
              <span>{{convertSecondsToDuration(this.ambulanceValue)}}</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
  </div>

  <div class="col-2">
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="'general_information.availability.type.commandment'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value margin-top">
              <span>{{convertSecondsToDuration(this.commandmentValue)}}</span>
            </li>

          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-2">
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="'general_information.availability.type.others'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value margin-top">
              <span>{{convertSecondsToDuration(this.otherValue)}}</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
  </div>

</div>
