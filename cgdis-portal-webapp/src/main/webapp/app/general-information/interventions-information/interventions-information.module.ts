import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InterventionsInformationComponent } from './interventions-information.component';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimpleTableModule } from '../../common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '../../common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '../../common/modules/tile-group/tile-group.module';
import { InputModule } from '../../common/modules/input/input.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { InterventionsInformationDetailComponent } from './detail/interventions-information-detail.component';
import { InterventionsInformationSummaryComponent } from '@app/general-information/interventions-information/summary/interventions-information-summary.component';
import { HighchartsModule } from '@app/common/modules/highcharts/highcharts.module';
import { LegendModule } from '@app/common/modules/legend/legend.module';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
    HighchartsModule,
    LegendModule,
  ],
  declarations: [
    InterventionsInformationComponent,
    InterventionsInformationDetailComponent,
    InterventionsInformationSummaryComponent,
  ],
  exports: [
    InterventionsInformationComponent,
    InterventionsInformationDetailComponent,
    InterventionsInformationSummaryComponent,
  ],
})
export class InterventionsInformationModule {}
