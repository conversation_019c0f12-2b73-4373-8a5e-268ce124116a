import { Component, Input, OnInit } from '@angular/core';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { Intervention } from '../../../model/intervention.model';

@Component({
  selector: 'cgdis-portal-interventions-information-detail',
  templateUrl: './interventions-information-detail.component.html',
})
export class InterventionsInformationDetailComponent implements OnInit {
  /**
   * The intervention
   */
  @Input() intervention: Intervention;

  /**
   * Person functions
   */
  functionsName: string[] = [];

  /**
   * The mission numbers
   */
  missionNumbers: string[] = [];

  /**
   * The mission name (radio call name)
   */
  radios: string[] = [];

  constructor(private datetimeService: DatetimeService) {}

  ngOnInit(): void {
    if (this.intervention !== null) {
      for (let vehicle of this.intervention.vehicles) {
        if (vehicle.missionNumber) {
          this.missionNumbers.push(vehicle.missionNumber);
        }
        if (vehicle.name) {
          this.radios.push(vehicle.name);
        }
        for (let member of vehicle.crew) {
          this.functionsName.push(member.functionName);
        }
      }
    }
  }

  /**
   * Format DatetimeModel to string
   * @param datetime
   */
  getFormattedDate(datetime: DatetimeModel): string {
    return this.datetimeService.format(datetime, 'DD/MM/YYYY HH:mm:ss');
  }
}
