import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { OperationalOccupationsComponent } from './operational-occupations.component';
import { OperationalOccupationsDetailComponent } from '@app/general-information/operational-occupations/detail/operational-occupations-detail.component';

@NgModule({
  imports: [SharedModule, CommonModule, DatatableModule, EpDatatableModule],
  declarations: [
    OperationalOccupationsComponent,
    OperationalOccupationsDetailComponent,
  ],
  exports: [
    OperationalOccupationsComponent,
    OperationalOccupationsDetailComponent,
  ],
})
export class OperationalOccupationsModule {}
