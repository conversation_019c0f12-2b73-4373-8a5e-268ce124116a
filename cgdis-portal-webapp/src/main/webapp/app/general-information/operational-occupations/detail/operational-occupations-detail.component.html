<div class="row ">

    <div  *ngIf="!occupation; else occupationInformation">

    </div>
    <ng-template #occupationInformation>
        <div class="col-sm-12 ">
            <table>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.type' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{occupation.operationalOccupation.type}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.label' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{('i18n.data.operational_occupations.' + occupation.operationalOccupation.code.split(' ').join('')) | translate}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.code' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{occupation.operationalOccupation.code}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.assignment' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{occupation.entity? occupation.entity.name :'-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.volunteer' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{occupation.volunteer ? '✔' : '-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.professional' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{occupation.professional ? '✔' : '-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.external' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{occupation.external ? '✔' : '-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.status.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'general_information.managerial_occupations.list.header.status.list.' + occupation.status | translate}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.startDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{formatDate(occupation.startDate) | defaultValue:'-' }}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.managerial_occupations.list.header.endDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{formatDate(occupation.endDate) | defaultValue:'-'}}</span>
                    </td>
                </tr>
            </table>
        </div>
    </ng-template>
</div>
