import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import {
  DateModel,
  DownloadOptions,
  IOneRestResource,
  RestService,
} from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonGeneralInformation } from '../model/person/person-general-information.model';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { PRO_VOLUNTEER_BOTH } from './general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { FieldGroupOption } from '../common/modules/form-module/model/field-group-option.model';
import { TranslateService } from '@ngx-translate/core';
import _ from 'lodash';

@Injectable()
export class GeneralInformationService {
  /**
   * The base url to access person services
   * @type {string[]}
   *
   */
  baseUrl = ['person'];

  constructor(private _restService: RestService) {}

  /**
   * Get general information of the connected user
   * @returns {Observable<PersonGeneralInformation>}
   */
  get(): Observable<PersonGeneralInformation> {
    return this._restService
      .one<PersonGeneralInformation>(...this.baseUrl, 'generalinformation')
      .get()
      .pipe(
        map((value: PersonGeneralInformation) => {
          return value;
        }),
      );
  }

  public loadDateTimeFilterConfig(
    operator: SearchOperator,
    inUrl: boolean,
    defaultValue: DateModel,
  ): FilterConfig {
    return new FilterConfig({
      operator: operator,
      inUrl: inUrl,
      defaultValue: defaultValue,
    });
  }

  public buildProVolunteerChoice(
    choice: [string, boolean],
  ): Map<string, boolean> {
    let result = new Map<string, boolean>();
    if (!_.isEmpty(choice)) {
      switch (choice[0]) {
        case PRO_VOLUNTEER_BOTH.is_professional:
          result
            .set(PRO_VOLUNTEER_BOTH.is_professional, true)
            .set(PRO_VOLUNTEER_BOTH.is_volunteer, false);
          break;
        case PRO_VOLUNTEER_BOTH.is_volunteer:
          result
            .set(PRO_VOLUNTEER_BOTH.is_professional, false)
            .set(PRO_VOLUNTEER_BOTH.is_volunteer, true);
          break;
        case PRO_VOLUNTEER_BOTH.both:
        default:
          result
            .set(PRO_VOLUNTEER_BOTH.is_professional, true)
            .set(PRO_VOLUNTEER_BOTH.is_volunteer, true);
          break;
      }
    }

    return result;
  }

  public loadBarrackedPossibleValues(
    translateService: TranslateService,
  ): FieldGroupOption<string, any>[] {
    let result: FieldGroupOption<string, any>[] = [];

    result.push(
      new FieldGroupOption({
        value: 'true',
        label: translateService.instant(
          'admin.service_plan.list.header.closed-yes',
        ),
      }),
    );

    result.push(
      new FieldGroupOption({
        value: 'false',
        label: translateService.instant(
          'admin.service_plan.list.header.closed-no',
        ),
      }),
    );

    return result;
  }

  exportPrestations(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
    isPro: boolean,
    portalLabel: string,
    positionLabel: string,
    entityLabel: string,
    barracked: boolean,
  ): Observable<boolean> {
    const resource: IOneRestResource<boolean> = this._restService
      .one('export', 'persons', 'prestation', String(personId))
      .one('csv');
    let theMap: { [key: string]: any } = {
      from: startDate,
      to: endDate,
    };
    if (isPro != undefined) {
      // @ts-ignore
      theMap['isPro'] = isPro;
    }
    if (portalLabel != undefined) {
      // @ts-ignore
      theMap['portalLabel'] = portalLabel;
    }
    if (positionLabel != undefined) {
      // @ts-ignore
      theMap['position'] = positionLabel;
    }
    if (entityLabel != undefined) {
      // @ts-ignore
      theMap['entityLabel'] = entityLabel;
    }
    if (barracked != undefined) {
      // @ts-ignore
      theMap['barracked'] = barracked;
    }
    return resource.download(new DownloadOptions({ params: theMap })).pipe(
      map((value) => {
        return true;
      }),
    );
  }

  exportPrestationsPenalty(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
  ): Observable<boolean> {
    return this._restService
      .one('export', 'persons', 'prestation-penalty', String(personId))
      .one('csv')
      .download(
        new DownloadOptions({ params: { from: startDate, to: endDate } }),
      )
      .pipe(
        map((value) => {
          return true;
        }),
      );
  }

  exportPrestationsDetail(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
    isPro: boolean,
    portalLabel: string,
    positionLabel: string,
    entityLabel: string,
    barracked: boolean,
  ): Observable<boolean> {
    const resource: IOneRestResource<boolean> = this._restService
      .one('export', 'persons', 'prestation-detail', String(personId))
      .one('csv');
    let theMap: { [key: string]: any } = {
      from: startDate,
      to: endDate,
    };
    if (isPro != undefined) {
      // @ts-ignore
      theMap['isPro'] = isPro;
    }
    if (portalLabel != undefined) {
      // @ts-ignore
      theMap['portalLabel'] = portalLabel;
    }
    if (positionLabel != undefined) {
      // @ts-ignore
      theMap['position'] = positionLabel;
    }
    if (entityLabel != undefined) {
      // @ts-ignore
      theMap['entityLabel'] = entityLabel;
    }
    if (barracked != undefined) {
      // @ts-ignore
      theMap['barracked'] = barracked;
    }
    return resource.download(new DownloadOptions({ params: theMap })).pipe(
      map((value) => {
        return true;
      }),
    );
  }
}
