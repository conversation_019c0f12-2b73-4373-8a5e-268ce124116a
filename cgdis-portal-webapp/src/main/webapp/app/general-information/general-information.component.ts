import {
  afterNextRender,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { ConnectedUserService } from '../security/connected-user.service';
import { of, Subscription } from 'rxjs';
import { CgdisPortalUser } from '@app/security/model/cgdis-portal-user.model';
import { OperationalVolunteerInternshipService } from '@app/general-information/operational-volunteer-internship/operational-volunteer-internship.service';
import { LogAsService } from '../security/log-as.service';
import { switchMap } from 'rxjs/operators';
import { AptitudesInformationService } from '@app/general-information/aptitudes-information/aptitudes-information.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { OwlOptions, SlidesOutputData } from 'ngx-owl-carousel-o';
import * as _ from 'lodash';

@Component({
  selector: 'cgdis-portal-general-information',
  templateUrl: './general-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    OperationalVolunteerInternshipService,
    AptitudesInformationService,
  ],
})
export class GeneralInformationComponent implements OnInit, OnDestroy {
  @Input() personId: number;

  /**
   * User must have at least one of these roles to display general tab
   */
  @Input() generalTabRoles: string[];

  /**
   * User must have at least one of these roles to display operational tab
   */
  @Input() operationalTabRoles: string[];

  /**
   * User must have at least one of these roles to display medical tab
   */
  @Input() medicalTabRoles: string[];

  /**
   * User must have at least one of these roles to display medical tab
   */
  @Input() myActivityRoles: string[];

  /**
   * User must have at least one of these roles to display update contact in general tab
   */
  @Input() updateContactGeneralTabRoles: string[];

  /**
   * Selected tab
   * @type {number}
   */
  public index = 1;
  public logas: boolean;
  public viewRolesLogas: boolean;

  subscriptions: Subscription[] = [];

  accessGeneralTab = false;
  accessOperationalTab = false;
  accessMedicalTab = false;
  accessMycActivityTab = false;

  currentUser: CgdisPortalUser;

  showVolunteerInternship: boolean;
  showExpiredAptitudes: boolean;

  tabs: { text: string }[] = [];

  owlCarouselOptions: OwlOptions = {
    loop: false,
    nav: true,
    dots: false,
    mouseDrag: true,
    touchDrag: true,
    navText: ['<', '>'],
    responsive: {
      0: {
        items: 1,
        slideBy: 1,
      },
    },
    startPosition: 0,
  };

  viewRendered = false;

  startPosition = 0;

  isMobile: boolean = false;

  constructor(
    private userService: ConnectedUserService,
    private cd: ChangeDetectorRef,
    private aptitudesInformationService: AptitudesInformationService,
    private logAsService: LogAsService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.startPositionModified(this.startPosition);
          this.cd.markForCheck();
        }),
    );

    afterNextRender(() => {
      this.viewRendered = true;
    });
  }

  ngOnInit(): void {
    this.currentUser = this.userService.getCurrentCgdisPortalUser();
    this.logas = this.currentUser.userDetails.tecid !== this.personId;

    this.tabs = [
      { text: 'general_information.general' },
      { text: 'general_information.operational' },
      { text: 'general_information.medical' },
      {
        text: this.logas
          ? 'general_information.activity.title_logas'
          : 'general_information.activity.title',
      },
    ];

    if (!_.isEmpty(this.generalTabRoles)) {
      this.subscriptions.push(
        this.userService
          .hasAnyRolesObservable(this.generalTabRoles)
          .subscribe((hasRole) => {
            this.accessGeneralTab = hasRole;
            this.initSelectedIndex();
            this.cd.markForCheck();
          }),
      );
    }

    if (!_.isEmpty(this.operationalTabRoles)) {
      this.subscriptions.push(
        this.userService
          .hasAnyRolesObservable(this.operationalTabRoles)
          .pipe(
            switchMap((hasRole) =>
              hasRole === true
                ? this.logAsService.canAccessOperationalInformations(
                    this.personId,
                  )
                : of(false),
            ),
          )
          .subscribe((hasRole) => {
            this.accessOperationalTab = hasRole;
            this.initSelectedIndex();
            this.cd.markForCheck();
          }),
      );
    }

    if (!_.isEmpty(this.medicalTabRoles)) {
      this.subscriptions.push(
        this.userService
          .hasAnyRolesObservable(this.medicalTabRoles)
          .pipe(
            switchMap((hasRole) =>
              hasRole === true
                ? this.logAsService.canAccessMedicalInformations(this.personId)
                : of(false),
            ),
          )
          .subscribe((hasRole) => {
            this.accessMedicalTab = hasRole;
            this.initSelectedIndex();
            this.cd.markForCheck();
          }),
      );
    }

    if (!_.isEmpty(this.myActivityRoles)) {
      this.subscriptions.push(
        this.userService
          .hasAnyRolesObservable(this.myActivityRoles)
          .pipe(
            switchMap((hasRole) =>
              hasRole === true
                ? this.logAsService.canAccessActivityInformations(this.personId)
                : of(false),
            ),
          )
          .subscribe((hasRole) => {
            this.accessMycActivityTab = hasRole;
            this.initSelectedIndex();
            this.cd.markForCheck();
          }),
      );
    }

    this.subscriptions.push(
      this.aptitudesInformationService
        .checkExpired(this.personId)
        .subscribe((value) => {
          this.showExpiredAptitudes = value;
          this.cd.markForCheck();
        }),
    );

    this.viewRolesLogas = this.userService.hasAnyRoles([
      'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE',
    ]);
    this.cd.markForCheck();
  }

  slideTranslated($event: SlidesOutputData) {
    this.index = $event.startPosition + 1;
    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  startPositionModified(index: number) {
    this.startPosition = index;

    this.owlCarouselOptions = {
      loop: false,
      nav: true,
      dots: false,
      mouseDrag: false,
      touchDrag: true,
      navText: ['<', '>'],
      responsive: {
        0: {
          items: 1,
          slideBy: 1,
        },
      },
      startPosition: this.startPosition,
    };
    this.cd.markForCheck();
  }

  private initSelectedIndex() {
    if (this.accessGeneralTab) {
      this.index = 1;
    } else if (this.accessOperationalTab) {
      this.index = 2;
    } else if (this.accessMedicalTab) {
      this.index = 3;
    } else {
      this.index = 1;
    }
  }
}
