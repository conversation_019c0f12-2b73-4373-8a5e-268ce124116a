<div class="accordion__panel">
  <div *ngIf="hasGrade">
    <cgdis-portal-datatable-toggle-filter
      [labelKey]="'general_information.operational_grades.list.header.allClosedGrades'"
      [filterName]="'allClosed'"
      [filterConfig]="filterConfigAllPersons"
      [datatableService]="operationalGradesService"
      [customFormControl]="formControlToggle"

    ></cgdis-portal-datatable-toggle-filter>
    <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
      <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
      <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
    </cgdis-portal-button-link>
</div>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.operational_grades.list.header.title'"></label>
    <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'code'"
                                        [filterConfig]="filterLike"
                                        [datatableService]="operationalGradesService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.operational_grades.list.header.code'"></label>
    <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'code'"
                                        [filterConfig]="filterLike"
                                        [datatableService]="operationalGradesService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.operational_grades.list.header.startDate'"></label>
    <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'startDate'"
                                              [filterConfig]="startDateFilterConfig"
                                              [datatableService]="operationalGradesService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.operational_grades.list.header.endDate'"></label>
    <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'endDate'"
                                              [filterConfig]="endDateFilterConfig"
                                              [datatableService]="operationalGradesService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.operational_grades.list.header.decreeDate'"></label>
    <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'decreeDate'"
                                              [filterConfig]="endDateFilterConfig"
                                              [datatableService]="operationalGradesService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>

    <cgdis-portal-cgdisdatatable
      [pageSize]="50"
      [datatableService]="operationalGradesService"
      [sorts]="[{dir:'asc',prop:isMobile?'galon':'type'}]"
      [id]="'general-information-operational-grade-table-Id'"
      [showDetails]="'MOBILE'">

      <!-- Filter for person -->
      <cgdis-portal-datatable-number-filter [hidden]="true"
                                            [filterConfig]="filterConfigPersonId"
                                            [filterName]="'person'"
                                            [customFormControl]="formControl"
                                            [datatableService]="operationalGradesService"></cgdis-portal-datatable-number-filter>

      <ng-template #template let-row="row">
        <div class="error-detail-container">
          <cgdis-portal-operational-grades-detail [grade]="row"></cgdis-portal-operational-grades-detail>
        </div>
      </ng-template>

  <ep-datatable-column [columnName]="'galon'" [flexGrow]="2">
        <ng-template epDatatableCell let-context>
      <a cgdis-portal-icon
         [icon]="context.row.operationalGrade.code.replace(' ','')+'_'+context.row.operationalGrade.gradeType"
         [iconClasses]="['galon_grade']"></a>
        </ng-template>
      </ep-datatable-column>

      <!-- gradeType (string) -->
      <ep-datatable-column [columnName]="'operationalGrade.gradeType'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.gradeType.title' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
      {{('general_information.operational_grades.list.header.gradeType.list.' + context.row.operationalGrade.gradeType | translate) | defaultValue:'-'}}
        </ng-template>
      </ep-datatable-column>

      <!-- label (string) -->
      <ep-datatable-column [columnName]="'code'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.title' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
      {{(('i18n.data.operational_grades.' + context.row.operationalGrade.code.split(' ').join('')) | translate) | defaultValue:'-'}}
        </ng-template>
        <ng-template epDatatableFilter>
      <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'code'" [filterConfig]="filterLike"
                                          [datatableService]="operationalGradesService"></cgdis-portal-datatable-text-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- code (string) -->
      <ep-datatable-column [columnName]="'code'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.code' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
      {{context.row.operationalGrade.code | defaultValue:'-'}}
        </ng-template>
        <ng-template epDatatableFilter *ngIf="!isMobile">
      <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'code'" [filterConfig]="filterLike"
                                          [datatableService]="operationalGradesService"></cgdis-portal-datatable-text-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- volunteer (boolean) -->
  <ep-datatable-column [columnName]="'isVolunteer'" [flexGrow]="isMobile?3:1">
        <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.volunteer'.concat(isMobile ? '_mobile' : '_desktop_reduced') | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{ (context.row.isVolunteer ? '✔' : '-') }}
        </ng-template>
      </ep-datatable-column>

      <!-- professional (boolean) -->
  <ep-datatable-column [columnName]="'isProfessional'" [flexGrow]="isMobile?3:1">
        <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.professional'.concat(isMobile ? '_mobile' : '_desktop_reduced') | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{ (context.row.isProfessional ? '✔' : '-') }}
        </ng-template>
      </ep-datatable-column>

      <!-- samu (boolean) -->
      <ep-datatable-column [columnName]="'isSamu'" [flexGrow]="1">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.samu'.concat(isMobile?'_mobile':'') | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{ (context.row.isSamu ? '✔' : '-') }}
        </ng-template>
      </ep-datatable-column>

      <!-- start date (date) -->
      <ep-datatable-column [columnName]="'startDate'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.startDate' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{getFormattedDate(context.row.startDate)}}
        </ng-template>
        <ng-template epDatatableFilter>
      <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig"
                                                [datatableService]="operationalGradesService"></cgdis-portal-datatable-datepicker-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- end date (date) -->
      <ep-datatable-column [columnName]="'endDate'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.endDate' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
      {{getFormattedDate(context.row.endDate)}}
        </ng-template>
        <ng-template epDatatableFilter>
      <cgdis-portal-datatable-datepicker-filter [filterName]="'endDate'" [filterConfig]="endDateFilterConfig"
                                                [datatableService]="operationalGradesService"></cgdis-portal-datatable-datepicker-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- decree date (date) -->
      <ep-datatable-column [columnName]="'decreeDate'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.decreeDate' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
      {{getFormattedDate(context.row.decreeDate)}}
        </ng-template>
        <ng-template epDatatableFilter>
      <cgdis-portal-datatable-datepicker-filter [filterName]="'decreeDate'" [filterConfig]="endDateFilterConfig"
                                                [datatableService]="operationalGradesService"></cgdis-portal-datatable-datepicker-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- echelon (String)-->
      <ep-datatable-column [columnName]="'echelon'" [flexGrow]="2">
        <ng-template epDatatableHeader>
          {{'general_information.operational_grades.list.header.echelon' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{context.row.echelon | defaultValue:'-'}}
        </ng-template>
      </ep-datatable-column>


</cgdis-portal-cgdisdatatable>

  <div *ngIf="isSupportFirefighter">
    <a  cgdis-portal-icon [icon]="'PSUPP_INCSASAP'" [iconClasses]="['galon_grade']" style="vertical-align: middle; display: inline-block; height: 40px"></a>
    <span [translate]="'general_information.operational_grades.list.header.psupp'"></span>
  </div>
</div>
