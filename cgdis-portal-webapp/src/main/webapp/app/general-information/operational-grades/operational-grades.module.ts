import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { OperationalGradesComponent } from '@app/general-information/operational-grades/operational-grades.component';
import { OperationalGradesDetailComponent } from '@app/general-information/operational-grades/detail/operational-grades-detail.component';

@NgModule({
  imports: [SharedModule, CommonModule, DatatableModule, EpDatatableModule],
  declarations: [OperationalGradesComponent, OperationalGradesDetailComponent],
  exports: [OperationalGradesComponent, OperationalGradesDetailComponent],
})
export class OperationalGradesModule {}
