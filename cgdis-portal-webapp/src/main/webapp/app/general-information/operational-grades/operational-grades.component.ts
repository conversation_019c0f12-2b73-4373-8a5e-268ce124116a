import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { OperationalGradesService } from '@app/general-information/operational-grades/operational-grades.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-operational-grades',
  templateUrl: './operational-grades.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OperationalGradesService],
})
export class OperationalGradesComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  @Input() isSupportFirefighter: boolean;

  @Input() hasGrade: boolean;

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterLike = new FilterConfig({
    operator: SearchOperator.like,
    inUrl: false,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterConfigAllPersons = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  formControlToggle = new UntypedFormControl();
  formControl = new UntypedFormControl();
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public operationalGradesService: OperationalGradesService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 768px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.operationalGradesService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.operationalGradesService.getNumberOfFilters(
          ['person', 'allClosed'],
        );
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.operationalGradesService.getNumberOfFilters([
      'person',
      'allClosed',
    ]);
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
