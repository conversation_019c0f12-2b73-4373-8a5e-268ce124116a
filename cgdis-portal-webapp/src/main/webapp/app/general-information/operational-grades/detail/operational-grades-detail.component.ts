import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { PersonOperationalGrade } from '@app/model/person/person-operational-grade.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-operational-grades-detail',
  templateUrl: './operational-grades-detail.component.html',
  styleUrls: ['./_operational-grades-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OperationalGradesDetailComponent implements OnDestroy {
  @Input() grade: PersonOperationalGrade;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  formatDate(date: DateModel) {
    return this.dateService.format(date, this.dateFormat);
  }
}
