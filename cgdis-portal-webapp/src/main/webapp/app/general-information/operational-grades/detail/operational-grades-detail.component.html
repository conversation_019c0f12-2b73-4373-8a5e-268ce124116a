<div class="row ">

    <div  *ngIf="!grade; else gradeInformation">

    </div>
    <ng-template #gradeInformation>
        <div class="col-sm-12 ">
            <table>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.gradeType.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'general_information.operational_grades.list.header.gradeType.list.' + grade.operationalGrade.gradeType | translate | defaultValue:'-'}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{('i18n.data.operational_grades.' + grade.operationalGrade.code.split(' ').join('')) | translate}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.code' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{grade.operationalGrade.code | defaultValue:'-'}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.volunteer' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{grade.isVolunteer ? '✔' : '-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.professional' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{grade.isProfessional ? '✔' : '-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.samu' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{grade.isSamu ? '✔' : '-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.startDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{formatDate(grade.startDate)}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.endDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{formatDate(grade.endDate) | defaultValue:'-' }}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.decreeDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{formatDate(grade.decreeDate) | defaultValue:'-'}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_grades.list.header.echelon' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{grade.echelon | defaultValue:'-'}}</span>
                    </td>
                </tr>
            </table>
        </div>
    </ng-template>
</div>