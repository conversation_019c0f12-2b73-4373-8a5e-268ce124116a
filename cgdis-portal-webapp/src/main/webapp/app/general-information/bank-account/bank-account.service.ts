import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonGeneralInformation } from '../../model/person/person-general-information.model';
import { BankAccount } from '@app/model/bank-account.model';

@Injectable()
export class BankAccountService {
  /**
   * The base url to access person services
   * @type {string[]}
   *
   */
  baseUrl = ['person'];

  constructor(private _restService: RestService) {}

  /**
   * Get general information of the connected user
   * @returns {Observable<PersonGeneralInformation>}
   */
  get(personId: number): Observable<BankAccount> {
    return this._restService
      .one<BankAccount>(...this.baseUrl, 'bankaccount', String(personId))
      .get()
      .pipe(
        map((value: BankAccount) => {
          return value;
        }),
      );
  }
}
