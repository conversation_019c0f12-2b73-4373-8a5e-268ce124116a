import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule } from '@angular/forms';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { BankAccountComponent } from '@app/general-information/bank-account/bank-account.component';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    FormModule,
    SimplePopupModule,
  ],
  declarations: [BankAccountComponent],
  exports: [BankAccountComponent],
})
export class BankAccountModule {}
