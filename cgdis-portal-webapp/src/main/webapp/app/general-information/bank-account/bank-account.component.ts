import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { BankAccount } from '@app/model/bank-account.model';
import { BankAccountService } from '@app/general-information/bank-account/bank-account.service';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-bank-account-information',
  templateUrl: './bank-account.component.html',
  styleUrls: ['./_bank-account.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [BankAccountService],
})
export class BankAccountComponent implements OnInit {
  /**
   * The person id.
   */
  @Input() personId: number;

  initialData: BankAccount;
  loading = true;

  constructor(
    private bankAccountService: BankAccountService,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
  ) {}

  ngOnInit(): void {
    if (!this.personId) {
      this.personId = this.connectedUserService.getConnectedUserId();
    }

    this.bankAccountService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((value: BankAccount) => {
        this.initialData = value;
        this.loading = false;
        this.cd.markForCheck();
      });
  }
}
