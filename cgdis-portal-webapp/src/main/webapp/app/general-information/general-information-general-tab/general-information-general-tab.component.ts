import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { GeneralContactInformationService } from '@app/general-information/general-contact/general-contact-information.service';
import { GeneralContactInformation } from '@app/model/general-contact-information.model';
import { switchMap, take } from 'rxjs/operators';
import { LogAsService } from '@app/security/log-as.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { of, Subject } from 'rxjs';

@Component({
  selector: 'cgdis-portal-general-information-general-tab',
  templateUrl: './general-information-general-tab.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  providers: [GeneralContactInformationService],
})
export class GeneralInformationGeneralTabComponent
  implements OnInit, OnD<PERSON>roy
{
  @Input() personId: number;

  @Input() showRoles: boolean;

  @Input() showRolesLogas: boolean;

  @Input() updateContactRoles: string[];

  @Input() logas: boolean;

  noMail = false;

  initialData: GeneralContactInformation;

  protected canAccessDiplomas = false;

  protected showContactForm = false;

  private _unsubscribe$ = new Subject<void>();

  constructor(
    private generalContactInformationService: GeneralContactInformationService,
    private logAsService: LogAsService,
    private userService: ConnectedUserService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  ngOnInit(): void {
    if (this.logas) {
      this.userService
        .hasAnyRolesObservable([
          'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS',
        ])
        .pipe(
          switchMap((hasRole) =>
            hasRole === true
              ? this.logAsService.canAccessDiploma(this.personId)
              : of(false),
          ),
        )
        .subscribe((hasRole) => {
          this.canAccessDiplomas = hasRole;
          this.cd.markForCheck();
        });
    } else {
      this.canAccessDiplomas = true;
    }
    this.generalContactInformationService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((initialData) => {
        this.initialData = initialData.generalContactInformation;

        if (
          (this.initialData.professionalEmail == undefined ||
            this.initialData.professionalEmail === '') &&
          (this.initialData.privateEmail == undefined ||
            this.initialData.privateEmail === '')
        ) {
          this.noMail = true;
        } else {
          this.noMail = false;
        }

        this.cd.markForCheck();
      });
  }

  noMailVal(event: boolean) {
    this.noMail = event;
  }
}
