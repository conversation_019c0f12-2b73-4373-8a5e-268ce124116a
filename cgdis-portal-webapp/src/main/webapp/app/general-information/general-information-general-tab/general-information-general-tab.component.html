<div >
  <div id="accordion">
    <mat-accordion>

      <!-- Personal Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">

        <mat-expansion-panel-header [collapsedHeight]="'48px'" [expandedHeight]="'50px'"
                                    class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.personal_information.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <ng-template matExpansionPanelContent>
          <div class="collapse show">
            <div class="card-body">
              <div aria-hidden="false" role="region" aria-labelledby="accordion1" class="accordion__panel">
                <cgdis-portal-personal-information [personId]="personId" [logas]="logas"></cgdis-portal-personal-information>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Roles -->
      <mat-expansion-panel class="accordion__group" [expanded]="false" *ngIf="showRoles">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.roles.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-roles-information></cgdis-portal-roles-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false" *ngIf="showRolesLogas">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.roles.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-roles-logas-information [personId]="personId"></cgdis-portal-roles-logas-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- General Contact Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false" (opened)="showContactForm = true"
                           (closed)="showContactForm = false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.contact_information.general'"></span>

            <div *ngIf="noMail" class="alert-mail-profil">
              <cgdis-portal-icon [icon]="'icon-warning'" [class]="'mr-1'"></cgdis-portal-icon>
              <span class="d-none d-md-inline" [translate]="'general_information.contact_information.warning_no_mail'"> ></span>
              <span class="d-md-none"
                    [translate]="'general_information.contact_information.warning_no_mail_mobile'"> ></span>
            </div>
          </mat-panel-title>

        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body" *ngIf="showContactForm" style="text-wrap: auto">
            <div aria-hidden="false" role="region" class="accordion__panel">
              <cgdis-portal-general-contact-form (noMailEvent)="noMailVal($event)"  [updateRoles]="updateContactRoles" [personId]="personId"
                                                 [formId]="'general-contact-form'"></cgdis-portal-general-contact-form>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Postal Address Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.address.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-postal-address-information [personId]="personId"></cgdis-portal-postal-address-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Driver's license information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.driver_license.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-driver-license-information [personId]="personId"></cgdis-portal-driver-license-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Diplomas information-->
      @if (canAccessDiplomas) {
        <mat-expansion-panel class="accordion__group" [expanded]="false"
                             *cgdisPortalAuthRoles="logas ? ['ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS'] : ['ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS']"
        >
          <mat-expansion-panel-header class="general-information-header" role="heading">
            <mat-panel-title class="mb-0">
              <span class="accordion__title" [translate]="'general_information.diplomas.title'"></span>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <ng-template matExpansionPanelContent>
            <div class="card-body">
              <cgdis-portal-diplomas [personId]="personId"></cgdis-portal-diplomas>
            </div>
          </ng-template>
        </mat-expansion-panel>
      }
      <!-- BankAccount -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.bank_account.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <div aria-hidden="false" role="region" class="accordion__panel">
              <cgdis-portal-bank-account-information [personId]="personId"></cgdis-portal-bank-account-information>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

    </mat-accordion>
  </div>
</div>
