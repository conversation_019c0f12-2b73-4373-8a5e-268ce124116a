import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { PersonRestriction } from '../../model/person/person-restriction.model';

@Injectable()
export class RestrictionsInformationService extends CgdisDatatableService<PersonRestriction> {
  constructor(
    restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(restService.all('person-restriction', 'all'));
  }
}
