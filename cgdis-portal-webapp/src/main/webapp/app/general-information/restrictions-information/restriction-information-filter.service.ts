import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { Restriction } from '@app/model/person/restriction.model';

@Injectable()
export class RestrictionInformationFilterService {
  /**
   * Based url to access all restriction
   * @type {string}
   */
  private baseUrl = ['person-restriction'];

  constructor(private restService: RestService) {}

  /**
   * Return all existing restriction type
   * @return {Observable<string>}
   */
  getAllTypes(): Observable<string[]> {
    const restResource = this.restService.all<string>(
      ...this.baseUrl,
      'all-types',
    );
    return restResource.get();
  }

  /**
   * Return all existing descriptions
   * @return {Observable<string>}
   */
  getAllDescriptions(): Observable<string[]> {
    const restResource = this.restService.all<string>(
      ...this.baseUrl,
      'all-descriptions',
    );
    return restResource.get();
  }

  /**
   * Return all existing description details (description and external id)
   * @return {Observable<string>}
   */
  getAllRestrictions(): Observable<Restriction[]> {
    const restResource = this.restService.all<Restriction>(
      ...this.baseUrl,
      'all-restrictions',
    );
    return restResource.get();
  }
}
