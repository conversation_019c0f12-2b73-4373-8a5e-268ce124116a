import { NgModule } from '@angular/core';
import { SharedModule } from '../../common/shared/shared.module';
import { CommonModule } from '@angular/common';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimpleTableModule } from '../../common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '../../common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '../../common/modules/tile-group/tile-group.module';
import { InputModule } from '../../common/modules/input/input.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { RestrictionsInformationComponent } from './restrictions-information.component';
import { RestrictionsInformationDetailComponent } from './restrictions-information-detail/restrictions-information-detail.component';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
  ],
  declarations: [
    RestrictionsInformationComponent,
    RestrictionsInformationDetailComponent,
  ],
  exports: [RestrictionsInformationComponent],
})
export class RestrictionsInformationModule {}
