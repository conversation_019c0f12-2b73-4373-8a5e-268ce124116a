<div class="accordion__panel">

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.restrictions.list.type'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [possibleValues]="typeFilterValues" [filterName]="'restriction'"
                                              [flexGrow]="3"
                                              [datatableService]="restrictionsInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.restrictions.list.description'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [possibleValues]="descriptionFilterValues" [filterName]="'restrictionDescription'"
                                              [flexGrow]="3"
                                              [datatableService]="restrictionsInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.restrictions.list.startDate'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="restrictionsInformationService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>
  <cgdis-portal-cgdisdatatable
    [datatableService]="restrictionsInformationService"
    [sorts]="[{dir:'asc',prop:'restriction'}]"
    [id]="'general-information-restriction-table-Id'"
    [showDetails]="'MOBILE'">


    <ng-template #template let-row="row" >
      <div class="error-detail-container">
        <cgdis-portal-restrictions-information-detail [content]="row"></cgdis-portal-restrictions-information-detail>
      </div>
    </ng-template>

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'person'"
                                          [customFormControl]="formControl"
                                          [datatableService]="restrictionsInformationService"></cgdis-portal-datatable-number-filter>

    <ep-datatable-column [columnName]="'restriction'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'general_information.restrictions.list.type' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.restriction.externalId | defaultValue:'-'}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [possibleValues]="typeFilterValues" [filterName]="'restriction'"
                                              [flexGrow]="3"
                                              [datatableService]="restrictionsInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'restrictionDescription'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.restrictions.list.description' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [possibleValues]="descriptionFilterValues" [filterName]="'restrictionDescription'"
                                              [flexGrow]="3"
                                              [datatableService]="restrictionsInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{(('i18n.data.restrictions.' + context.row.restriction.externalId.split(' ').join('')) | translate) | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>


    <ep-datatable-column [columnName]="'startDate'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'general_information.restrictions.list.startDate'.concat(isMobile? '_mobile' : '') | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="restrictionsInformationService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.startDate)}}
      </ng-template>
    </ep-datatable-column>
  </cgdis-portal-cgdisdatatable>
</div>
