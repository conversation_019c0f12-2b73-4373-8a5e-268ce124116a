<div class="detail-row accordion__panel card-body">
  <div class="row ">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.restrictions.list.type' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{content.restriction.externalId}}
    </div>
  </div>
  <div class="row">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.restrictions.list.description' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{('i18n.data.restrictions.' + content.restriction.externalId.split(' ').join('')) | translate}}
    </div>
  </div>

  <div class="row">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.restrictions.list.startDate' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{ formatDate(content.startDate) }}
    </div>
  </div>
</div>
