import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { RestrictionsInformationService } from './restrictions-information.service';
import { DateModel, DateService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { RestrictionInformationFilterService } from './restriction-information-filter.service';
import _ from 'lodash';
import { UntypedFormControl } from '@angular/forms';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-restrictions-information',
  templateUrl: './restrictions-information.component.html',
  providers: [
    RestrictionsInformationService,
    RestrictionInformationFilterService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RestrictionsInformationComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  /**
   * Values for the restriction type filter
   */
  typeFilterValues: FieldGroupOption<string, any>[];

  /**
   * Values for the restriction description filter
   */
  descriptionFilterValues: FieldGroupOption<string, any>[];

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  numberOfFilters: number;

  constructor(
    private dateService: DateService,
    public restrictionsInformationService: RestrictionsInformationService,
    public restrictionInformationFilterService: RestrictionInformationFilterService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.restrictionInformationFilterService
      .getAllTypes()
      .subscribe((types) => {
        this.typeFilterValues = _.map(
          types,
          (oneType) =>
            new FieldGroupOption({
              value: oneType,
              I18NLabel: oneType,
            }),
        );
      });

    this.restrictionInformationFilterService
      .getAllDescriptions()
      .subscribe((types) => {
        this.descriptionFilterValues = _.map(
          types,
          (oneType) =>
            new FieldGroupOption({
              value: oneType,
              I18NLabel: oneType,
            }),
        );
      });

    this.restrictionsInformationService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.restrictionsInformationService.getNumberOfFilters(['person']);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters =
      this.restrictionsInformationService.getNumberOfFilters(['person']);
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
