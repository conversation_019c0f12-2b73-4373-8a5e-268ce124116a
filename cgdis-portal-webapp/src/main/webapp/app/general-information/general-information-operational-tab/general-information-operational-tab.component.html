<div >
  <div id="accordion">
    <mat-accordion>

      <!-- Operational Contact Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading" collapsedHeight="48px"
                                    expandedHeight="48px">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.contact_information.operational'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="card-body">
          <div aria-hidden="false" role="region" class="accordion__panel">
            <cgdis-portal-operational-contact-form [personId]="personId"
                                                   [formId]="'operational-contact-form'"></cgdis-portal-operational-contact-form>
          </div>
        </div>
      </mat-expansion-panel>

      <!-- Active assignments information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.active_assignment.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-active-assignments-information
              [personId]="personId"></cgdis-portal-active-assignments-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Operational Occupations information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.operational_occupations.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-operational-occupations [personId]="personId"></cgdis-portal-operational-occupations>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Managerial Occupations information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.managerial_occupations.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-managerial-occupations [personId]="personId"></cgdis-portal-managerial-occupations>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Operational grade information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.operational_grades.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-operational-grades [personId]="personId" [isSupportFirefighter]="isSupportFirefighter"
                                             [hasGrade]="hasGrade"></cgdis-portal-operational-grades>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Operational Dates information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.operational_dates.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-operational-dates [personId]="personId"></cgdis-portal-operational-dates>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="logas ? ['ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL'] : ['ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title"
                  [translate]="'general_information.function_operational.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="collapse show">
            <div class="card-body">
              <div aria-hidden="false" role="region" aria-labelledby="accordion1" class="accordion__panel">
                <cgdis-portal-person-function-operational
                  [personId]="personId" [logas]="logas"></cgdis-portal-person-function-operational>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false" *ngIf="showInternship">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title"
                  [translate]="'general_information.operational_volunteer_internship.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="collapse show">
            <div class="card-body">
              <div aria-hidden="false" role="region" aria-labelledby="accordion1" class="accordion__panel">
                <cgdis-portal-operational-volunteer-internship
                  [personId]="personId"></cgdis-portal-operational-volunteer-internship>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false" *ngIf="isYoungFirefighter">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title"
                  [translate]="'general_information.operational_young_firefighter.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="collapse show">
            <div class="card-body">
              <div aria-hidden="false" role="region" aria-labelledby="accordion1" class="accordion__panel">
                <cgdis-portal-operational-young-firefighter
                  [personId]="personId"></cgdis-portal-operational-young-firefighter>
              </div>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Suspension Information -->
      <mat-expansion-panel *ngIf="!isMobile" class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.suspension.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-suspensions-information [personId]="personId"></cgdis-portal-suspensions-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

    </mat-accordion>
  </div>
</div>
