import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { OperationalContactService } from '@app/general-information/operational-contact/operational-contact.service';
import { PersonGeneralInformation } from '@app/model/person/person-general-information.model';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { OperationalVolunteerInternshipService } from '@app/general-information/operational-volunteer-internship/operational-volunteer-internship.service';

@Component({
  selector: 'cgdis-portal-general-information-operational-tab',
  templateUrl: './general-information-operational-tab.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OperationalContactService],
  encapsulation: ViewEncapsulation.None,
})
export class GeneralInformationOperationalTabComponent
  implements OnInit, OnD<PERSON>roy
{
  @Input() personId: number;

  @Input() logas: boolean;

  showInternship: boolean;

  isYoungFirefighter: boolean;
  isSupportFirefighter: boolean;
  hasGrade: boolean;

  isMobile: boolean = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private operationalContactService: OperationalContactService,
    private cd: ChangeDetectorRef,
    private volunteerInternshipService: OperationalVolunteerInternshipService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription: Subscription) =>
      subscription.unsubscribe(),
    );
  }

  ngOnInit(): void {
    this.subscriptions.push(
      this.volunteerInternshipService
        .checkIfExists(this.personId)
        .subscribe((value) => {
          this.showInternship = value;
          this.cd.markForCheck();
        }),
      this.operationalContactService
        .get(this.personId)
        .subscribe((value: PersonGeneralInformation) => {
          this.isYoungFirefighter = value.youngFirefighter;
          this.isSupportFirefighter = value.supportFirefighter;
          this.hasGrade = value.hasGrade;
          this.cd.markForCheck();
        }),
    );
  }
}
