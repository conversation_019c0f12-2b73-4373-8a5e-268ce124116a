<div class="detail-row accordion__panel card-body">
  <div class="row ">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.aptitudes.list.aptitude' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{('i18n.data.aptitudes.' + content.aptitude.externalId.split(' ').join('')) | translate}}
    </div>
  </div>
  <div class="row">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.aptitudes.list.status.title' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{content.statut.name}}
    </div>
  </div>

  <div class="row">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.aptitudes.list.startDate' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{getFormattedDate(content.startDate)}}
    </div>
  </div>

  <div class="row">
    <div [ngClass]="{'col-5' : isMobile, 'col-2' : !isMobile}">
      {{'general_information.aptitudes.list.endDate' | translate}}:
    </div>
    <div [ngClass]="{'col-7' : isMobile, 'col-10' : !isMobile}">
      {{formatShortDate(content.endDate)}}
    </div>
  </div>
</div>
