import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { TranslateService } from '@ngx-translate/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-aptitudes-information-detail',
  templateUrl: './aptitudes-information-detail.component.html',
  styles: ['.row {margin-bottom: 10px;}'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AptitudesInformationDetailComponent implements OnDestroy {
  @Input()
  content: any;
  dateFormat = 'DD/MM/YYYY';
  shortDateFormat = 'MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private translationService: TranslateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
          this.shortDateFormat = this.isMobile ? 'MM/YY' : 'MM/YYYY';
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      if (date.year <= 1900) {
        return this.translationService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  formatShortDate(date: DateModel) {
    if (date != null) {
      if (date.year >= 2200) {
        return this.translationService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.shortDateFormat);
    } else {
      return '-';
    }
  }
}
