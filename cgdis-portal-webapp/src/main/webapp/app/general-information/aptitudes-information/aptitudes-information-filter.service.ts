import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { Aptitude } from '@app/model/person/aptitude.model';

@Injectable()
export class AptitudesInformationFilterService {
  /**
   * Based url to access all aptitude
   * @type {string}
   */
  private baseUrl = ['person-aptitude'];

  constructor(private restService: RestService) {}

  /**
   * Return all existing aptitude status
   * @return {Observable<string>}
   */
  getAllStatus(): Observable<string[]> {
    const restResource = this.restService.all<string>(
      ...this.baseUrl,
      'all-status',
    );
    return restResource.get();
  }

  /**
   * Return all existing aptitude name
   * @return {Observable<string>}
   */
  getAllAptitude(): Observable<string[]> {
    const restResource = this.restService.all<string>(
      ...this.baseUrl,
      'all-aptitude',
    );
    return restResource.get();
  }

  /**
   * Return all existing aptitude
   * @return {Observable<string>}
   */
  getAllAptitudeDetails(): Observable<Aptitude[]> {
    const restResource = this.restService.all<Aptitude>(
      ...this.baseUrl,
      'all-aptitude-details',
    );
    return restResource.get();
  }
}
