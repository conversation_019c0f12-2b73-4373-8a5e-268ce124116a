import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { AptitudesInformationService } from './aptitudes-information.service';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { AptitudesInformationFilterService } from './aptitudes-information-filter.service';
import _ from 'lodash';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-aptitudes-information',
  templateUrl: './aptitudes-information.component.html',
  providers: [AptitudesInformationService, AptitudesInformationFilterService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AptitudesInformationComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  /**
   * Values for the aptitude filter
   */
  aptitudeFilterValues: FieldGroupOption<string, any>[];

  /**
   * Values for the aptitude filter
   */
  statusFilterValues: FieldGroupOption<string, any>[];
  showFilter = false;
  isImported = false;

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  formControl = new UntypedFormControl();
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  dateFormat = 'DD/MM/YYYY';
  shortDateFormat = 'MM/YYYY';

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    public aptitudesInformationService: AptitudesInformationService,
    public aptitudesInformationFilterService: AptitudesInformationFilterService,
    private translationService: TranslateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
          this.shortDateFormat = this.isMobile ? 'MM/YY' : 'MM/YYYY';
        }),
    );
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.aptitudesInformationFilterService.getAllStatus().subscribe((types) => {
      this.statusFilterValues = _.map(
        types,
        (oneType) =>
          new FieldGroupOption({
            value: oneType,
            I18NLabel: oneType,
          }),
      );
    });

    this.aptitudesInformationFilterService
      .getAllAptitude()
      .subscribe((types) => {
        this.aptitudeFilterValues = _.map(
          types,
          (oneType) =>
            new FieldGroupOption({
              value: oneType,
              I18NLabel: oneType,
            }),
        );
      });

    this.aptitudesInformationService
      .isImported(this.personId)
      .subscribe((isImported) => {
        this.isImported = isImported;
      });

    this.aptitudesInformationService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.aptitudesInformationService.getNumberOfFilters(['person']);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.aptitudesInformationService.getNumberOfFilters([
      'person',
    ]);
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      if (date.year <= 1900) {
        return this.translationService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  public getFormattedShortDate(date: DateModel): string {
    if (date != null) {
      if (date.year >= 2200) {
        return this.translationService.instant(
          'people_medical_information.functions.undefined',
        );
      }
      return this.dateService.format(date, this.shortDateFormat);
    } else {
      return '-';
    }
  }

  public isExpired(date: DateModel): boolean {
    return this.dateService.isAfter(this.dateService.now(), date);
  }
}
