<div class="accordion__panel">
  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.aptitudes.list.aptitude'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [possibleValues]="aptitudeFilterValues" [filterName]="'aptitudeName'"
                                              [flexGrow]="3"
                                              [datatableService]="aptitudesInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.aptitudes.list.status.title'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [possibleValues]="aptitudeFilterValues" [filterName]="'aptitudeName'"
                                               [flexGrow]="3"
                                               [datatableService]="aptitudesInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>

      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.aptitudes.list.startDate'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="aptitudesInformationService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.aptitudes.list.endDate'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'endDate'" [filterConfig]="endDateFilterConfig" [datatableService]="aptitudesInformationService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [datatableService]="aptitudesInformationService"
    [sorts]="[{dir:'asc',prop:'aptitudeName'}]"
    [id]="'general-information-suspensions-table-Id'"
    [showDetails]="'MOBILE'">

    <ng-template #template let-row="row" >
      <div class="error-detail-container">
        <cgdis-portal-aptitudes-information-detail [content]="row"></cgdis-portal-aptitudes-information-detail>
      </div>
    </ng-template>

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'person'"
                                          [customFormControl]="formControl"
                                          [datatableService]="aptitudesInformationService"></cgdis-portal-datatable-number-filter>


    <ep-datatable-column [columnName]="'aptitudeName'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'general_information.aptitudes.list.aptitude' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter  [possibleValues]="aptitudeFilterValues" [filterName]="'aptitudeName'"
                                              [flexGrow]="1"
                                              [datatableService]="aptitudesInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{(('i18n.data.aptitudes.' + context.row.aptitude.externalId.split(' ').join('')) | translate) | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'statutName'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'general_information.aptitudes.list.status.title' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [possibleValues]="statusFilterValues" [filterName]="'statutName'"
                                              [flexGrow]="1"
                                              [datatableService]="aptitudesInformationService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.statut.name | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'startDate'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'general_information.aptitudes.list.startDate' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="aptitudesInformationService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.startDate)}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'endDate'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'general_information.aptitudes.list.endDate' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'endDate'" [filterConfig]="endDateFilterConfig" [datatableService]="aptitudesInformationService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div [ngClass]="{'aptitude-expired': isExpired(context.row.endDate)}">{{getFormattedShortDate(context.row.endDate) | defaultValue:'-'}}</div>
      </ng-template>
    </ep-datatable-column>

  </cgdis-portal-cgdisdatatable>

  <div *ngIf="isImported" class="aptitudes-imported" [translate]="'general_information.aptitudes.list.isImported'"></div>
</div>
