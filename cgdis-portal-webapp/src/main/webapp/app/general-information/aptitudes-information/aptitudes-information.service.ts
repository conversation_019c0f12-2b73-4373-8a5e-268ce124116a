import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { PersonAptitude } from '../../model/person/person-aptitude.model';
import { Observable } from 'rxjs';

@Injectable()
export class AptitudesInformationService extends CgdisDatatableService<PersonAptitude> {
  // TODO split datatable service
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(restService.all('person-aptitude', 'all'));
  }

  /**
   * Get all existing versions of a service plan
   * @return {Observable<ServicePlan[]>}
   */
  checkExpired(personTecid: number): Observable<boolean> {
    const restResource = this.restService.one(
      'person-aptitude',
      String(personTecid),
      'checkexpiredaptitudes',
    );
    return restResource.get().pipe(
      map((value: boolean) => {
        return value;
      }),
    );
  }

  /**
   * Get all existing versions of a service plan
   * @return {Observable<ServicePlan[]>}
   */
  isImported(personTecid: number): Observable<boolean> {
    const restResource = this.restService.one(
      'person-aptitude',
      String(personTecid),
      'isimported',
    );
    return restResource.get().pipe(
      map((value: boolean) => {
        return value;
      }),
    );
  }
}
