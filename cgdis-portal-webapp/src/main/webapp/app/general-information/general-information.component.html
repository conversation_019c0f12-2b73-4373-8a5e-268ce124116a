<section class="section">
  <div class="container">

    <ul class="general-information-tab" *ngIf="!isMobile">
      <li *ngIf="accessGeneralTab">
        <button (click)="index = 1;  startPositionModified(1)" class="seperator"
                [ngClass]="{'selected': index === 1}">{{'general_information.general' | translate}}</button>
      </li>
      <li *ngIf="accessOperationalTab">
        <button (click)="index = 2;  startPositionModified(2)" class="seperator"
                [ngClass]="{'selected': index === 2}">{{'general_information.operational' | translate}}</button>
      </li>
      <li *ngIf="accessMedicalTab">
        <button (click)="index = 3;  startPositionModified(3)" class="seperator" [ngClass]="{'selected': index === 3}">
          <cgdis-portal-icon *ngIf="showExpiredAptitudes" [icon]="'icon-warning'"></cgdis-portal-icon>
          {{'general_information.medical' | translate}}</button>
      </li>
      <li *ngIf="accessMycActivityTab">
        <button *ngIf="!logas" (click)="index = 4;  startPositionModified(3)"
                [ngClass]="{'selected': index === 4}">{{'general_information.activity.title' | translate}}</button>
        <button *ngIf="logas" (click)="index = 4;  startPositionModified(4)"
                [ngClass]="{'selected': index === 4}">{{'general_information.activity.title_logas' | translate}}</button>
      </li>
    </ul>

    <owl-carousel-o *ngIf="isMobile && viewRendered"
                  #owlElement
                    (translated)="slideTranslated($event)"
                  [options]="owlCarouselOptions"
                  [class]="['owl-popup']">
      <ng-container *ngFor="let tab of tabs; index as $index">
        <ng-template carouselSlide [id]="'gi_'+$index">
          <div >
            <div>
              {{tab.text| translate}}
            </div>
          </div>
        </ng-template>
      </ng-container>
    </owl-carousel-o>
    <section class="section">

      <cgdis-portal-general-information-general-tab *ngIf="index === 1 && accessGeneralTab" [personId]="personId"
                                                    [logas]="logas" [showRoles]="!logas"
                                                    [showRolesLogas]="logas && viewRolesLogas"
                                                    [updateContactRoles]="updateContactGeneralTabRoles"></cgdis-portal-general-information-general-tab>
      <cgdis-portal-general-information-operational-tab *ngIf="index === 2 && accessOperationalTab"
                                                        [personId]="personId"
                                                        [logas]="logas"
                                                       ></cgdis-portal-general-information-operational-tab>
      <cgdis-portal-general-information-medical-tab *ngIf="index === 3 && accessMedicalTab"
                                                    [personId]="personId"></cgdis-portal-general-information-medical-tab>
      <cgdis-portal-general-information-activity-tab *ngIf="index === 4 && accessMycActivityTab" [personId]="personId"
                                                     [logas]="logas"></cgdis-portal-general-information-activity-tab>
    </section>
  </div>
</section>

