import { take, takeUntil } from 'rxjs/operators';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { UpdateOperationalContactService } from './update-operational-contact.service';
import { OperationalContactInformation } from '@app/model/operational-contact-information.model';
import { ValidatorFn } from '@angular/forms';
import { CustomValidatorsService } from '@app/common/modules/form-module/validator/custom-validators.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { Subject, Subscription } from 'rxjs';
import { OperationalContactService } from './operational-contact.service';
import { PersonGeneralInformation } from '@app/model/person/person-general-information.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-operational-contact-form',
  templateUrl: './operational-contact.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    UpdateOperationalContactService,
    OperationalContactService,
    {
      provide: FORM_SERVICE,
      useExisting: UpdateOperationalContactService,
    },
  ],
})
export class OperationalContactComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  /**
   * The initial data
   */
  initialData: OperationalContactInformation;

  /**
   * The form ID
   */
  @Input() formId: string;

  phoneValidator: ValidatorFn[];
  emailValidator: ValidatorFn[];
  pagerValidator: ValidatorFn[];
  issiValidator: ValidatorFn[];
  grantedPossibleValue: FieldOption<boolean>[];

  canEdit = true;
  canViewGSMPager = false;
  private rolesSubscription: Subscription;

  buttonBlockClasses = {};

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  private _unsubscribe$ = new Subject<void>();

  constructor(
    private service: UpdateOperationalContactService,
    private operationalContactService: OperationalContactService,
    private validatorService: CustomValidatorsService,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    if (!this.personId) {
      this.personId = this.connectedUserService.getConnectedUserId();
    }

    this.grantedPossibleValue = [
      new FieldOption<boolean>({
        I18NLabel:
          'general_information.operational_contact' + '.send_alarm_clock',
        value: true,
      }),
    ];

    // get initial data
    this.operationalContactService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((value: PersonGeneralInformation) => {
        this.initialData = value.operationalContactInformation;
        this.cd.markForCheck();
      });

    this.service
      .onModelUpdated()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((model: OperationalContactInformation) => {
        this.initialData = model;
      });

    this.buttonBlockClasses = { 'col-md-4': true, 'width-80': true };
    this.phoneValidator = [this.validatorService.validatePhoneNumber()];
    this.emailValidator = [this.validatorService.validateEmail()];
    this.pagerValidator = [this.validatorService.validatePager()];
    this.issiValidator = [this.validatorService.validateIssi()];

    this.rolesSubscription = this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK',
      ])
      .subscribe((newValue) => {
        this.canEdit = newValue;
        this.cd.markForCheck();
      });

    this.rolesSubscription = this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PAGER_VIEW',
        'ROLE_PERMISSION_PAGER_UPDATE',
      ])
      .subscribe((newValue) => {
        this.canViewGSMPager = newValue;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.rolesSubscription.unsubscribe();
    this._unsubscribe$.next();
    this._unsubscribe$.complete();

    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public hasAnyRoles(role: string[]): boolean {
    return this.connectedUserService.hasAnyRoles(role);
  }

  isValid(info: string): boolean {
    return info != null && info != undefined && info.length >= 1;
  }
}
