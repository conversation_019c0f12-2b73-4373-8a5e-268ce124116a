import { BaseModel } from "../../model/base-model.model";

/**
 * Form for operational contact information form
 */
export class UpdateOperationalContactInformationForm extends BaseModel {
  phoneNumber: String;
  mobileNumber: String;
  email: String;
  pager: String;
  pagerMobile: String;
  issi: String;

  constructor(args: UpdateOperationalContactInformationForm) {
    super(args);
    this.phoneNumber = args.phoneNumber;
    this.mobileNumber = args.mobileNumber;
    this.email = args.email;
    this.pager = args.pager;
    this.pagerMobile = args.pagerMobile;
    this.issi = args.issi;
  }
}
