import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { UpdateOperationalContactInformationForm } from './update-operational-contact-information-forms.model';
import { OperationalContactInformation } from '../../model/operational-contact-information.model';
import { DefaultFormService } from '../../common/modules/form-module/service/default-form.service';
import { ToastService } from '../../common/shared/toasts/CGDISToastService';
import { FormErrorService } from '../../common/modules/form-module/service/form-error.service';
import { FormError } from '../../common/modules/error-management/model/form-error.model';

@Injectable()
export class UpdateOperationalContactService extends DefaultFormService<
  UpdateOperationalContactInformationForm,
  OperationalContactInformation
> {
  private _baseUrl: string[] = ['operationalcontact'];

  constructor(
    private _restService: RestService,
    toastr: ToastService,
    formErrorService: FormErrorService,
  ) {
    super(
      toastr,
      'people_management.operational_contact.update.success',
      formErrorService,
    );
  }

  submit(
    form: UpdateOperationalContactInformationForm,
  ): Observable<OperationalContactInformation> {
    return this._restService
      .all<OperationalContactInformation>(...this._baseUrl)
      .update(form)
      .pipe(
        map((value) => {
          return value;
        }),
      );
  }

  submitSuccess(result: OperationalContactInformation): void {}

  submitError(formError: FormError): void {}
}
