import { NgModule } from '@angular/core';
import { SharedModule } from '../../common/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimplePopupModule } from '../../common/modules/popup/simple-popup.module';
import { OperationalContactComponent } from './operational-contact.component';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    FormModule,
    SimplePopupModule,
  ],
  declarations: [OperationalContactComponent],
  exports: [OperationalContactComponent],
})
export class OperationalContactModule {}
