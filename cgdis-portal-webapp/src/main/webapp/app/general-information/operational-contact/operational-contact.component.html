<ng-container *ngIf="initialData">
  <cgdis-portal-form [formId]="'operational-contact-id'" [formClasses]="['row']" [buttonBlockClasses]="buttonBlockClasses" [hideFormActions]="!canEdit" [formReadonly]="!canEdit">
    <cgdis-portal-number-field
      [name]="'tecid'"
      [initialValue]="initialData.tecid"
      [visible]="false"
      [disableIfNotVisible]="false">
    </cgdis-portal-number-field>

    <cgdis-portal-number-field
      [name]="'teclock'"
      [initialValue]="initialData.teclock"
      [visible]="false"
      [disableIfNotVisible]="false">
    </cgdis-portal-number-field>

    <div class="row" style="display: flex;justify-content: center;">
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}">
        <cgdis-portal-input-field
          [name]="'mobileNumber'"
          [customValidators]="phoneValidator"
          [fieldMaxLength]="20"
          [fieldReadonly]="true"
          [labelKey]="'general_information.operational_contact.mobile'"
          [initialValue]="isValid(initialData.mobileNumber) ? initialData.mobileNumber : '-'"
          class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}">
        <cgdis-portal-input-field
          [name]="'pager'"
          [customValidators]="pagerValidator"
          [fieldMinLength]="7"
          [fieldMaxLength]="7"
          [fieldReadonly]="true"
          [labelKey]="'general_information.operational_contact.pager'"
          [initialValue]="isValid(initialData.pager) ? initialData.pager : '-'" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}">
        <cgdis-portal-input-field
          [name]="'email'"
          [customValidators]="emailValidator"
          [fieldMaxLength]="150"
          [fieldReadonly]="true"
          [labelKey]="'general_information.operational_contact.email'"
          [initialValue]="isValid(initialData.email) ? initialData.email : '-'" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}">
        <cgdis-portal-checkbox-field
          [name]="'sendAlarmClock'"
          [slider]="true"
          [possibleValues]="grantedPossibleValue"
          [labelKey]="''"
          [fieldReadonly]="!canEdit"
          [initialValue]="initialData.sendAlarmClock" class="col-md-6" style="display: contents;">
        </cgdis-portal-checkbox-field>
      </div>
    </div>

    <div class="row" style="display: flex;justify-content: center;" *ngIf="canViewGSMPager">
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}">
      <cgdis-portal-input-field
              [name]="'pagerMobile'"
              [customValidators]="pagerValidator"
              [fieldMinLength]="7"
              [fieldMaxLength]="7"
              [fieldReadonly]="true"
              [labelKey]="'general_information.operational_contact.pager_mobile'"
              [initialValue]="isValid(initialData.pagerMobile) ? initialData.pagerMobile : '-'" class="col-md-6" style="display: contents;">
      </cgdis-portal-input-field>
      </div>
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}"></div>
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}"></div>
      <div class="col-sm-4 col-lg-2" [ngClass]="{'operational-contact-mobile' : isMobile}"></div>
    </div>

  </cgdis-portal-form>
</ng-container>
