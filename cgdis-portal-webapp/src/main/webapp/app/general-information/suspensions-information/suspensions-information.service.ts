import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Suspension } from '../../model/person/person-suspension.model';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { DateModel, RestService } from '@eportal/core';
import { Location } from '@angular/common';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { Observable } from 'rxjs';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class SuspensionsInformationService extends CgdisDatatableService<Suspension> {
  // TODO split datatable service
  private baseUrl = ['suspensioninformation'];

  constructor(
    private alertService: ToastService,
    private httpClient: HttpClient,
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(restService.all(...this.baseUrl, 'all'));
  }

  /**
   * Get all existing intervention types
   * @return {Observable<InterventionType[]>}
   */
  getPersonIsSuspended(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
  ): Observable<boolean> {
    const restResource = this.restService.one(
      ...this.baseUrl,
      String(personId),
      'issuspended',
    );
    return restResource.get({ start: startDate, end: endDate }).pipe(
      map((value: boolean) => {
        return value;
      }),
    );
  }
}
