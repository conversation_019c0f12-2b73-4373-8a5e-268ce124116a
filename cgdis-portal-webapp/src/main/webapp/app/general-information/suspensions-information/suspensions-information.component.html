<div class="accordion__panel">
  <cgdis-portal-cgdisdatatable
    [datatableService]="suspensionService"
    [sorts]="[{dir:'asc',prop:'startDate'}]"
    [id]="'general-information-suspensions-table-Id'">

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'person'"
                                          [customFormControl]="formControl"
                                          [datatableService]="suspensionService"></cgdis-portal-datatable-number-filter>

    <ep-datatable-column [columnName]="'startDate'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.suspension.list.startDate' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="suspensionService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.startDate)}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'endDate'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.suspension.list.endDate' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'endDate'" [filterConfig]="endDateFilterConfig" [datatableService]="suspensionService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.endDate)}}
      </ng-template>
    </ep-datatable-column>

  </cgdis-portal-cgdisdatatable>
</div>
