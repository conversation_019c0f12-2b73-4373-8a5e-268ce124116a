import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { DrivingLicense } from '@app/model/person/person-driver-license.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-driver-license-information-detail',
  templateUrl: './driver-license-information-detail.component.html',
  styleUrls: ['./_driver-license-information-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DriverLicenseInformationDetailComponent implements OnDestroy {
  @Input() driverLicense: DrivingLicense;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  formatDate(date: DateModel) {
    return this.dateService.format(date, this.dateFormat);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
