<div class="accordion__panel">

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.driver_license.list.category'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [filterName]="'category'" [filterConfig]="filterLike" [datatableService]="drivingLicenseService"></cgdis-portal-datatable-text-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [datatableService]="drivingLicenseService"
    [sorts]="[{dir:'asc',prop:'category'}]"
    [id]="'general-information-driving-license-table-Id'"
    [showDetails]="'MOBILE'">

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'person'"
                                          [customFormControl]="formControl"
                                          [datatableService]="drivingLicenseService"></cgdis-portal-datatable-number-filter>

    <ng-template #template let-row="row">
      <div class="error-detail-container">
        <cgdis-portal-driver-license-information-detail [driverLicense]="row"></cgdis-portal-driver-license-information-detail>
      </div>
    </ng-template>

    <ep-datatable-column [columnName]="'category'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.driver_license.list.category' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.category | defaultValue:'-'}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'category'" [filterConfig]="filterLike" [datatableService]="drivingLicenseService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'startDate'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.driver_license.list.startDate' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="drivingLicenseService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.startDate)}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'endDate'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.driver_license.list.endDate' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'endDate'" [filterConfig]="endDateFilterConfig" [datatableService]="drivingLicenseService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.endDate)}}
      </ng-template>
    </ep-datatable-column>

  </cgdis-portal-cgdisdatatable>
</div>
