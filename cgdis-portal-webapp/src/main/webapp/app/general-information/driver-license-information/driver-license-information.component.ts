import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { DriverLicenseInformationService } from './driver-license-information.service';
import { DateModel, DateService } from '@eportal/core';
import { UntypedFormControl } from '@angular/forms';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-driver-license-information',
  templateUrl: './driver-license-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DriverLicenseInformationService],
})
export class DriverLicenseInformationComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  dateFormat = 'DD/MM/YYYY';

  showFilter = false;

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public drivingLicenseService: DriverLicenseInformationService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.drivingLicenseService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.drivingLicenseService.getNumberOfFilters([
          'person',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.drivingLicenseService.getNumberOfFilters([
      'person',
    ]);
  }

  /**
   * Get formatted date
   * @param date
   */
  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
