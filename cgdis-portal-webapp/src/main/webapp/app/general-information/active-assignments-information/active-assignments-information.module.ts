import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActiveAssignmentsInformationComponent } from './active-assignments-information.component';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimpleTableModule } from '../../common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '../../common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '../../common/modules/tile-group/tile-group.module';
import { InputModule } from '../../common/modules/input/input.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { ActiveAssignmentsInformationDetailComponent } from '@app/general-information/active-assignments-information/detail/active-assignments-information-detail.component';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
  ],
  declarations: [
    ActiveAssignmentsInformationComponent,
    ActiveAssignmentsInformationDetailComponent,
  ],
  exports: [
    ActiveAssignmentsInformationComponent,
    ActiveAssignmentsInformationDetailComponent,
  ],
})
export class ActiveAssignmentsInformationModule {}
