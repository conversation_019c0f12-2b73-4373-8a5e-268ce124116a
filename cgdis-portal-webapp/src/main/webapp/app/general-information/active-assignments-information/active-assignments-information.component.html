<div class="accordion__panel">
  <cgdis-portal-datatable-toggle-filter
    *ngIf="!filterToggleIsHidden"
    [labelKey]="'general_information.active_assignment.list.allClosedAssignment'"
    [filterName]="'allClosed'"
    [filterConfig]="filterConfigAllPersons"
    [datatableService]="activeAssignmentsService"
    [customFormControl]="formControlToggle"

  ></cgdis-portal-datatable-toggle-filter>
  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.active_assignment.list.entityName'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'entityName'" [filterConfig]="filterLike" [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.active_assignment.list.startDate'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.active_assignment.list.endDate'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'endDateOrNull'" [filterConfig]="endDateFilterConfig" [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>
  <cgdis-portal-cgdisdatatable
    [datatableService]="activeAssignmentsService"
    [sorts]="[{dir:'asc',prop:'entityName'}]"
    [id]="'general-information-active-assignments-table-Id'"
    [showDetails]="'MOBILE'">

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'person'"
                                          [customFormControl]="formControl"
                                          [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-number-filter>

    <ng-template #template let-row="row">
      <div class="error-detail-container">
        <cgdis-portal-active-assignments-information-detail [assignment]="row"></cgdis-portal-active-assignments-information-detail>
      </div>
    </ng-template>

    <ng-container *ngFor="let columnName of columns">
      <ng-container [ngSwitch]="columnName">

        <ep-datatable-column *ngSwitchCase="'entityName'" [columnName]="'entityName'" [flexGrow]="2">
          <ng-template epDatatableHeader>
            {{'general_information.active_assignment.list.entityName' | translate}}
          </ng-template>
          <ng-template epDatatableFilter *ngIf="!isMobile">
            <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'entityName'" [filterConfig]="filterLike" [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-text-filter>
          </ng-template>
          <ng-template let-context epDatatableCell>
            {{ (context.row.entity != null ? context.row.entity.name : '-') }}
          </ng-template>
        </ep-datatable-column>

        <ng-container *ngSwitchCase="'primaryType'">
          <ep-datatable-column [columnName]="'primaryType'" [flexGrow]="2" *ngIf="!isMobile">
            <ng-template epDatatableFilter >
              <cgdis-portal-datatable-select-filter  [possibleValues]="primaryTypeValues" [filterName]="'primaryType'"
                                                     [datatableService]="activeAssignmentsService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
            </ng-template>
            <ng-template epDatatableHeader>
              {{'general_information.active_assignment.list.primaryType.title' | translate}}
            </ng-template>
            <ng-template let-context epDatatableCell>
              {{ 'general_information.active_assignment.list.primaryType.list.'+context.row.primaryType | translate}}
            </ng-template>
          </ep-datatable-column>
        </ng-container>

        <ep-datatable-column *ngSwitchCase="'Type'" [columnName]="'Type'" [flexGrow]="2">
          <ng-template epDatatableFilter >
            <cgdis-portal-datatable-select-filter  [possibleValues]="typeValues" [filterName]="'type'"
                                                   [datatableService]="activeAssignmentsService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
          </ng-template>
          <ng-template epDatatableHeader>
            {{'general_information.active_assignment.list.type.title' | translate}}
          </ng-template>
          <ng-template let-context epDatatableCell>
            {{( 'general_information.active_assignment.list.type.list.'+context.row.type | translate) | defaultValue:'-'}}
          </ng-template>
        </ep-datatable-column>


        <ng-container *ngSwitchCase="'startDate'">
          <ep-datatable-column [columnName]="'startDate'" [flexGrow]="2" *ngIf="!isMobile">
            <ng-template epDatatableHeader>
              {{'general_information.active_assignment.list.startDate' | translate}}
            </ng-template>
            <ng-template epDatatableFilter>
              <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-datepicker-filter>
            </ng-template>
            <ng-template epDatatableCell let-context>
              {{getFormattedDate(context.row.startDate)}}
            </ng-template>
          </ep-datatable-column>
        </ng-container>

        <ng-container *ngSwitchCase="'endDate'">
          <ep-datatable-column [columnName]="'endDate'" [flexGrow]="2" *ngIf="!isMobile">
            <ng-template epDatatableHeader>
              {{'general_information.active_assignment.list.endDate' | translate}}
            </ng-template>
            <ng-template epDatatableFilter>
              <cgdis-portal-datatable-datepicker-filter [filterName]="'endDateOrNull'" [filterConfig]="endDateFilterConfig" [datatableService]="activeAssignmentsService"></cgdis-portal-datatable-datepicker-filter>
            </ng-template>
            <ng-template epDatatableCell let-context>
              {{getFormattedDate(context.row.endDate)}}
            </ng-template>
          </ep-datatable-column>
        </ng-container>

      </ng-container>
    </ng-container>
  </cgdis-portal-cgdisdatatable>
</div>
