import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { ActiveAssignmentsInformationService } from './active-assignments-information.service';
import { DateModel, DateService } from '@eportal/core';
import { UntypedFormControl } from '@angular/forms';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { PrimaryType } from '@app/model/primary-type.enum';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-active-assignments-information',
  templateUrl: './active-assignments-information.component.html',
  providers: [ActiveAssignmentsInformationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActiveAssignmentsInformationComponent
  implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnChanges
{
  /**
   * The person id.
   */
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() filterToggleIsHidden = false;

  /**
   * Optional array of column names to display. If not provided, all columns are shown.
   * Also controls the order of columns.
   */
  @Input() columns: string[];

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterConfigAllPersons = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  primaryTypeValues: FieldOption<PrimaryType>[];
  typeValues: FieldOption<String>[];
  formControlToggle = new UntypedFormControl();
  formControl = new UntypedFormControl();
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public activeAssignmentsService: ActiveAssignmentsInformationService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    if (!this.columns) {
      this.columns = [
        'entityName',
        'primaryType',
        'Type',
        'startDate',
        'endDate',
      ];
    }
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);
    this.primaryTypeValues = [
      new FieldOption<PrimaryType>({
        I18NLabel:
          'general_information.active_assignment.list.primaryType.list.PRIMARY',
        value: PrimaryType.PRIMARY,
      }),
      new FieldOption<PrimaryType>({
        I18NLabel:
          'general_information.active_assignment.list.primaryType.list.SECONDARY',
        value: PrimaryType.SECONDARY,
      }),
      new FieldOption<PrimaryType>({
        I18NLabel:
          'general_information.active_assignment.list.primaryType.list.TECHNICAL',
        value: PrimaryType.TECHNICAL,
      }),
    ];
    this.typeValues = [
      new FieldOption<String>({
        I18NLabel: 'general_information.active_assignment.list.type.list.PRO',
        value: 'PRO',
      }),
      new FieldOption<String>({
        I18NLabel: 'general_information.active_assignment.list.type.list.VOL',
        value: 'VOL',
      }),
      new FieldOption<String>({
        I18NLabel: 'general_information.active_assignment.list.type.list.EXT',
        value: 'EXT',
      }),
    ];

    this.activeAssignmentsService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.activeAssignmentsService.getNumberOfFilters(
          ['person', 'allClosed'],
        );
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.personId && !changes.personId.firstChange) {
      this.formControl.setValue(this.personId);
    }
  }

  updateFilterNumber() {
    this.numberOfFilters = this.activeAssignmentsService.getNumberOfFilters([
      'person',
      'allClosed',
    ]);
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
