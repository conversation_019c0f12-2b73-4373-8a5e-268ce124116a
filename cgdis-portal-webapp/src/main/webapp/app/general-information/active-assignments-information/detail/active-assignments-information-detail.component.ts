import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { Assignment } from '@app/model/assignment.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-active-assignments-information-detail',
  templateUrl: './active-assignments-information-detail.component.html',
  styleUrls: ['./_active-assignments-information-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActiveAssignmentsInformationDetailComponent implements OnDestroy {
  @Input() assignment: Assignment;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  formatDate(date: DateModel) {
    return this.dateService.format(date, this.dateFormat);
  }
}
