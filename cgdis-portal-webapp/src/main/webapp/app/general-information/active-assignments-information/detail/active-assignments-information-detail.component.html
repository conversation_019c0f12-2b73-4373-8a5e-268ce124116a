<div class="row ">

    <div  *ngIf="!assignment; else assignmentInformation">

    </div>
    <ng-template #assignmentInformation>
        <div class="col-sm-12 ">
            <table>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.active_assignment.list.entityName' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{assignment.entity != null ? assignment.entity.name : '' | defaultValue: '-'}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.active_assignment.list.primaryType.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                      <span>{{'general_information.active_assignment.list.primaryType.list.'+assignment.primaryType | translate}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.active_assignment.list.type.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'general_information.active_assignment.list.type.list.'+assignment.type | translate}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.active_assignment.list.startDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span >{{formatDate(assignment.startDate)}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.active_assignment.list.endDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{formatDate(assignment.endDate) | defaultValue:'-'}}</span>
                    </td>
                </tr>
            </table>
        </div>
    </ng-template>
</div>
