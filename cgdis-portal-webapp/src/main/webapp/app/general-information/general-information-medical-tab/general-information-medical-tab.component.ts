import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';

@Component({
  selector: 'cgdis-portal-general-information-medical-tab',
  templateUrl: './general-information-medical-tab.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GeneralInformationMedicalTabComponent {
  /**
   * The person id.
   */
  @Input() personId: number;

  constructor() {}
}
