<div >
  <div id="accordion">
    <mat-accordion>
      <!-- Personal Medical Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.person_medical_information.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-medical-information [personId]="personId"></cgdis-portal-medical-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Aptitudes Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.aptitudes.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-aptitudes-information [personId]="personId"></cgdis-portal-aptitudes-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Restrictions Information -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.restrictions.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-restrictions-information [personId]="personId"></cgdis-portal-restrictions-information>
          </div>
        </ng-template>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
