import { Component, OnInit } from '@angular/core';
import { ConnectedUserService } from '../security/connected-user.service';
import { LandscapeBlockerModes } from '@app/model/landscape-blocker-modes.model';

@Component({
  selector: 'cgdis-portal-general-information-main',
  templateUrl: './general-information-main.component.html',
  styles: [],
})
export class GeneralInformationMainComponent implements OnInit {
  landscapeBlockerModes = LandscapeBlockerModes;

  personId: number;

  constructor(private userService: ConnectedUserService) {}

  ngOnInit() {
    if (this.personId == null) {
      this.personId = this.userService.getConnectedUserId();
    }
  }
}
