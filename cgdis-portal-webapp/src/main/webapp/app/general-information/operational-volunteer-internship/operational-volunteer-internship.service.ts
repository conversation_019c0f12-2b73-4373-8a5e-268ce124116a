import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonGeneralInformation } from '../../model/person/person-general-information.model';
import { OperationalVolunteerInternship } from '@app/model/operational-volunteer-internship.model';

@Injectable()
export class OperationalVolunteerInternshipService {
  /**
   * The base url to access person services
   * @type {string[]}
   *
   */
  baseUrl = ['person'];

  constructor(private _restService: RestService) {}

  /**
   * Get general information of the connected user
   * @returns {Observable<PersonGeneralInformation>}
   */
  get(personId: number): Observable<OperationalVolunteerInternship> {
    return this._restService
      .one<OperationalVolunteerInternship>(
        ...this.baseUrl,
        'operationalvolunteerinternship',
        String(personId),
      )
      .get()
      .pipe(
        map((value: OperationalVolunteerInternship) => {
          return value;
        }),
      );
  }

  checkIfExists(personId: number): Observable<boolean> {
    return this._restService
      .one<boolean>(...this.baseUrl, 'existsinternship', String(personId))
      .get()
      .pipe(
        map((value: boolean) => {
          return value;
        }),
      );
  }
}
