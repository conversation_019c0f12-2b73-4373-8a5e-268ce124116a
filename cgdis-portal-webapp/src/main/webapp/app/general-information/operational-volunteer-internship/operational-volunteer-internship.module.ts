import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule } from '@angular/forms';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { OperationalVolunteerInternshipComponent } from '@app/general-information/operational-volunteer-internship/operational-volunteer-internship.component';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    FormModule,
    SimplePopupModule,
  ],
  declarations: [OperationalVolunteerInternshipComponent],
  exports: [OperationalVolunteerInternshipComponent],
})
export class OperationalVolunteerInternshipModule {}
