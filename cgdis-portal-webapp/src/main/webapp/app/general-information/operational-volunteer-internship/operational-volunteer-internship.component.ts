import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { UpdateOperationalContactService } from '@app/general-information/operational-contact/update-operational-contact.service';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { OperationalVolunteerInternshipService } from '@app/general-information/operational-volunteer-internship/operational-volunteer-internship.service';
import { OperationalVolunteerInternship } from '@app/model/operational-volunteer-internship.model';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { DateModel, DateService } from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-operational-volunteer-internship',
  templateUrl: './operational-volunteer-internship.component.html',
  styleUrls: ['./_operational-volunteer-internship.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    OperationalVolunteerInternshipService,
    {
      provide: FORM_SERVICE,
      useExisting: UpdateOperationalContactService,
    },
  ],
})
export class OperationalVolunteerInternshipComponent
  implements OnInit, OnDestroy
{
  /**
   * The person id.
   */
  @Input() personId: number;

  initialData: OperationalVolunteerInternship;
  loading = true;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private operationalVolunteerInternshipService: OperationalVolunteerInternshipService,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    if (!this.personId) {
      this.personId = this.connectedUserService.getConnectedUserId();
    }

    this.operationalVolunteerInternshipService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((value: OperationalVolunteerInternship) => {
        this.initialData = value;
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
