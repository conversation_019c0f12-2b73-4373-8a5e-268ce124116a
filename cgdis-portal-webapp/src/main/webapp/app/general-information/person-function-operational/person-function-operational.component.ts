import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { TranslateService } from '@ngx-translate/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { Entity } from '@app/model/entity.model';

@Component({
  selector: 'cgdis-portal-person-function-operational',
  templateUrl: './person-function-operational.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PersonFunctionOperationalComponent implements OnInit {
  @Input() personId: number;

  @Input() logas: boolean;

  /**
   * Entity accessible
   * Note: can be null depending on user's rights
   */

  subscriptions: Subscription[] = [];

  loading = true;

  entities: Entity[];
  entityIds: number[];

  isMobile: boolean = false;

  constructor(
    private assignementService: AssignmentService,
    private connectedUserService: ConnectedUserService,
    private translateService: TranslateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.loadEntities(this.personId);
  }

  loadEntities(personId: number): void {
    this.loading = true;
    this.assignementService
      .getAllAssignmentForPerson(this.personId, this.logas)
      .subscribe((result) => {
        this.entities = result.map((r) => r.entity);
        this.entityIds = this.entities.map((e) => e.tecid);
        this.loading = false;
        this.cd.markForCheck();
      });
  }
}
