<cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
  <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
  <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
</cgdis-portal-button-link>

<ng-container *ngIf="isMobile">
  <div class="row search-filter" [hidden]="!showFilter">
    <div class="col-md-2">
      <label class="form-label" [translate]="'admin.function_operational.list.header.function'"></label>
      <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [filterName]="'functionOperationalPortalLabel'"
                                          [allowClear]="true"
                                          [datatableService]="personFunctionOperationalDatatableTableService"></cgdis-portal-datatable-text-filter>
    </div>
  </div>
</ng-container>

<cgdis-portal-cgdisdatatable
  [datatableService]="personFunctionOperationalDatatableTableService"
>

  <cgdis-portal-datatable-select-filter [hidden]="true"
                                        [filterName]="'entityIds'"
                                        [datatableService]="personFunctionOperationalDatatableTableService"
                                        [customFormControl]="formControl"
                                        [multiple]="true"
  ></cgdis-portal-datatable-select-filter>

  <ng-template #template let-row="row">
    <div class="error-detail-container" *ngIf="row.assignments.length>0; else templateNoData;">
      <ul *ngFor="let assigment of row.assignments;  last as $last;">
        <div [ngStyle]="{'height': $last ? '3rem' : '2rem'}">{{assigment.entity.name}} {{assigment.type}}</div>
      </ul>

    </div>
  </ng-template>


  <!-- name (string) -->
  <ep-datatable-column [columnName]="'functionOperational'" [flexGrow]="2">
    <ng-template epDatatableHeader>
      {{'admin.function_operational.list.header.function' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-row="row">
      {{row.portalLabel}}
    </ng-template>

    <ng-template epDatatableFilter>
      <cgdis-portal-datatable-text-filter [filterName]="'functionOperationalPortalLabel'"
                                          [allowClear]="true"
                                          [datatableService]="personFunctionOperationalDatatableTableService"></cgdis-portal-datatable-text-filter>
    </ng-template>
  </ep-datatable-column>

</cgdis-portal-cgdisdatatable>

<ng-template #templateNoData>
  <ul>
    <span [translate]="'default.table.noresult'"></span>
  </ul>
</ng-template>
