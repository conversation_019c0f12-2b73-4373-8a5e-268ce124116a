<div class="accordion__panel">
  <cgdis-portal-datatable-toggle-filter
    *ngIf="!filterToggleIsHidden"
    [labelKey]="'general_information.managerial_occupations.list.header.allClosedOccupations'"
    [filterName]="'allClosed'"
    [filterConfig]="filterConfigAllPersons"
    [datatableService]="managerialOccupationsService"
    [customFormControl]="formControlToggle"
  ></cgdis-portal-datatable-toggle-filter>
  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
</div>
<ng-container *ngIf="isMobile">
  <div class="row search-filter" [hidden]="!showFilter">
    <div class="col-md-2">
      <label class="form-label" [translate]="'general_information.managerial_occupations.list.header.type'"></label>
      <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'type'"
                                          [filterConfig]="filterLike"
                                          [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-text-filter>
    </div>
    <div class="col-md-2">
      <label class="form-label" [translate]="'general_information.managerial_occupations.list.header.label'"></label>
      <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'label'"
                                          [filterConfig]="filterLike"
                                          [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-text-filter>
    </div>
    <div class="col-md-2">
      <label class="form-label" [translate]="'general_information.managerial_occupations.list.header.code'"></label>
      <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="informations-text-filter" [allowClear]="true" [filterName]="'code'"
                                          [filterConfig]="filterLike"
                                          [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-text-filter>
    </div>
    <div class="col-md-2">
      <label class="form-label" [translate]="'general_information.managerial_occupations.list.header.startDate'"></label>
      <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'startDate'"
                                                [filterConfig]="startDateFilterConfig"
                                                [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-datepicker-filter>
    </div>
    <div class="col-md-2">
      <label class="form-label" [translate]="'general_information.managerial_occupations.list.header.endDate'"></label>
      <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'endDate'"
                                                [filterConfig]="endDateFilterConfig"
                                                [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-datepicker-filter>
    </div>
  </div>
</ng-container>

<cgdis-portal-cgdisdatatable
  [pageSize]="50"
  [datatableService]="managerialOccupationsService"
  [sorts]="[{dir:'asc',prop:(isMobile?'label':'type')}]"
  [id]="'general-information-operational-occupations-table-Id'"
  [showDetails]="'MOBILE'">

  <!-- Filter for person -->
  <cgdis-portal-datatable-number-filter [hidden]="true"
                                        [filterConfig]="filterConfigPersonId"
                                        [filterName]="'person'"
                                        [customFormControl]="formControl"
                                        [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-number-filter>

  <ng-template #template let-row="row">
    <div class="error-detail-container">
      <cgdis-portal-managerial-occupations-detail [occupation]="row"></cgdis-portal-managerial-occupations-detail>
    </div>
  </ng-template>

  <ng-container *ngFor="let columnName of columns">
    <ng-container [ngSwitch]="columnName">

      <!-- type (string) -->
      <ng-container *ngSwitchCase="'type'">
        <ep-datatable-column [columnName]="'type'" [flexGrow]="2" *ngIf="!isMobile">
          <ng-template epDatatableHeader>
            {{'general_information.managerial_occupations.list.header.type' | translate}}
          </ng-template>
          <ng-template epDatatableCell let-context>
            {{context.row.managerialOccupation.type | defaultValue:'-'}}
          </ng-template>
          <ng-template epDatatableFilter>
            <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'type'" [filterConfig]="filterLike"
                                                [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-text-filter>
          </ng-template>
        </ep-datatable-column>
      </ng-container>

      <!-- volProStatut -->
      <ep-datatable-column *ngSwitchCase="'volProStatut'" [columnName]="'statut'" [flexGrow]="1">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.status.title' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          <ng-container *ngIf="context.row.volunteer && context.row.professional">VOL / PRO</ng-container>
          <ng-container *ngIf="context.row.volunteer && !context.row.professional">VOL</ng-container>
          <ng-container *ngIf="!context.row.volunteer && context.row.professional">PRO</ng-container>
          <ng-container *ngIf="!context.row.volunteer && !context.row.professional">-</ng-container>
        </ng-template>
      </ep-datatable-column>

      <!-- label (string) -->
      <ep-datatable-column *ngSwitchCase="'label'" [columnName]="'label'" [flexGrow]="isMobile?4:2">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.label' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{(('i18n.data.operational_occupations.' + split(context.row.managerialOccupation.code)) | translate) | defaultValue:'-'}}
        </ng-template>
        <ng-template epDatatableFilter *ngIf="!isMobile">
          <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'label'" [filterConfig]="filterLike"
                                              [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-text-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- code (string) -->
      <ng-container *ngSwitchCase="'code'">
        <ep-datatable-column [columnName]="'code'" [flexGrow]="2" *ngIf="!isMobile">
          <ng-template epDatatableHeader>
            {{'general_information.managerial_occupations.list.header.code' | translate}}
          </ng-template>
          <ng-template epDatatableCell let-context>
            {{(context.row.managerialOccupation.code) | defaultValue:'-'}}
          </ng-template>
          <ng-template epDatatableFilter>
            <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'code'" [filterConfig]="filterLike"
                                                [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-text-filter>
          </ng-template>
        </ep-datatable-column>
      </ng-container>

      <!-- entityName (string) -->
      <ng-container *ngSwitchCase="'entity.name'">
        <ep-datatable-column [columnName]="'entity.name'" [flexGrow]="2" *ngIf="!isMobile">
          <ng-template epDatatableHeader>
            {{'general_information.managerial_occupations.list.header.assignment' | translate}}
          </ng-template>
          <ng-template epDatatableCell let-context>
            <ng-container>
              {{(context.row.entity?.name) | defaultValue:'-'}}
            </ng-container>
          </ng-template>
        </ep-datatable-column>
      </ng-container>

      <!-- volunteer (boolean) -->
      <ep-datatable-column *ngSwitchCase="'isVolunteer'" [columnName]="'isVolunteer'" [flexGrow]="isMobile?1:1">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.volunteer'.concat(isMobile ? '_mobile' : '_desktop_reduced') | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{ (context.row.volunteer ? '✔' : '-') }}
        </ng-template>
      </ep-datatable-column>

      <!-- professional (boolean) -->
      <ep-datatable-column *ngSwitchCase="'isProfessional'" [columnName]="'isProfessional'" [flexGrow]="isMobile?1:1">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.professional'.concat(isMobile ? '_mobile' : '_desktop_reduced') | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{ (context.row.professional ? '✔' : '-') }}
        </ng-template>
      </ep-datatable-column>

      <!-- external (boolean) -->
      <ep-datatable-column *ngSwitchCase="'isExternal'" [columnName]="'isExternal'" [flexGrow]="isMobile?1:1">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.external'.concat(isMobile ? '_mobile' : '_desktop_reduced') | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{ (context.row.external ? '✔' : '-') }}
        </ng-template>
      </ep-datatable-column>

      <!-- status (string) -->
      <ng-container *ngSwitchCase="'status'">
        <ep-datatable-column [columnName]="'status'" [flexGrow]="1" *ngIf="!isMobile">
          <ng-template epDatatableHeader>
            {{'general_information.managerial_occupations.list.header.status.title' | translate}}
          </ng-template>
          <ng-template epDatatableCell let-context>
            {{('general_information.managerial_occupations.list.header.status.list.' + context.row.status | translate) | defaultValue:'-'}}
          </ng-template>
        </ep-datatable-column>
      </ng-container>

      <!-- start date (date) -->
      <ep-datatable-column *ngSwitchCase="'startDate'" [columnName]="'startDate'" [flexGrow]="2.5">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.startDate' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{getFormattedDate(context.row.startDate)}}
        </ng-template>
        <ng-template epDatatableFilter>
          <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig"
                                                    [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-datepicker-filter>
        </ng-template>
      </ep-datatable-column>

      <!-- end date (date) -->
      <ep-datatable-column *ngSwitchCase="'endDate'" [columnName]="'endDate'" [flexGrow]="2.5">
        <ng-template epDatatableHeader>
          {{'general_information.managerial_occupations.list.header.endDate' | translate}}
        </ng-template>
        <ng-template epDatatableCell let-context>
          {{getFormattedDate(context.row.endDate)}}
        </ng-template>
        <ng-template epDatatableFilter>
          <cgdis-portal-datatable-datepicker-filter [filterName]="'endDate'" [filterConfig]="endDateFilterConfig"
                                                    [datatableService]="managerialOccupationsService"></cgdis-portal-datatable-datepicker-filter>
        </ng-template>
      </ep-datatable-column>

    </ng-container>
  </ng-container>
</cgdis-portal-cgdisdatatable>
