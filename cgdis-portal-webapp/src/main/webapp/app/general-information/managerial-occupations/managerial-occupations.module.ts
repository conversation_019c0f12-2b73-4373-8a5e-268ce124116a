import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ManagerialOccupationsComponent } from '@app/general-information/managerial-occupations/managerial-occupations.component';
import { ManagerialOccupationsDetailComponent } from '@app/general-information/managerial-occupations/detail/managerial-occupations-detail.component';

@NgModule({
  imports: [SharedModule, CommonModule, DatatableModule, EpDatatableModule],
  declarations: [
    ManagerialOccupationsComponent,
    ManagerialOccupationsDetailComponent,
  ],
  exports: [
    ManagerialOccupationsComponent,
    ManagerialOccupationsDetailComponent,
  ],
})
export class ManagerialOccupationsModule {}
