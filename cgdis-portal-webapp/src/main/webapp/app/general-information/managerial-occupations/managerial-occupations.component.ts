import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { ManagerialOccupationsService } from '@app/general-information/managerial-occupations/managerial-occupations.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-managerial-occupations',
  templateUrl: './managerial-occupations.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ManagerialOccupationsService],
})
export class ManagerialOccupationsComponent
  implements OnInit, OnDestroy, OnChanges
{
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() filterToggleIsHidden = false;

  /**
   * Optional array of column names to display. If not provided, all columns are shown.
   * Also controls the order of columns.
   */
  @Input() columns: string[];

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterLike = new FilterConfig({
    operator: SearchOperator.like,
    inUrl: false,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterConfigAllPersons = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  formControlToggle = new UntypedFormControl();
  formControl = new UntypedFormControl();
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  numberOfFilters: number;

  regex = new RegExp('//|.| |\n/');

  constructor(
    public managerialOccupationsService: ManagerialOccupationsService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    if (!this.columns) {
      this.columns = [
        'type',
        'label',
        'code',
        'entity.name',
        'isVolunteer',
        'isProfessional',
        'isExternal',
        'status',
        'startDate',
        'endDate',
      ];
    }
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.managerialOccupationsService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.managerialOccupationsService.getNumberOfFilters([
            'person',
            'allClosed',
          ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.personId && !changes.personId.firstChange) {
      this.formControl.setValue(this.personId);
    }
  }

  updateFilterNumber() {
    this.numberOfFilters = this.managerialOccupationsService.getNumberOfFilters(
      ['person', 'allClosed'],
    );
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  split(code: string): string {
    return code.split('/').join('').split('.').join('').split(' ').join('');
  }
}
