import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewEncapsulation,
} from '@angular/core';
import { GeneralInformationActivityDetailService } from './general-information-activity-detail.service';
import { DateModel, DateService } from '@eportal/core';
import { Observable, Subscription } from 'rxjs';
import { PieChartData } from '@app/common/modules/highcharts/pie/pie-chart.model';
import { LegendItem } from '@app/common/modules/legend/legend-item';
import { TranslateService } from '@ngx-translate/core';
import { GeneralInformationService } from '../../general-information.service';
import { PRO_VOLUNTEER_BOTH } from '../pro-volunteer-choice/pro-volunteer-choice.component';
import { TotalsStatistics } from '@app/model/totals-statistics.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { SimpleYesNoPopupComponent } from '@app/common/modules/popup/yes-no/simple-yes-no-popup.component';
import { SimpleYesNoPopupData } from '@app/common/modules/popup/yes-no/simple-yes-no-popup-data';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { take } from 'rxjs/operators';
import { isMatch } from 'lodash';

@Component({
  selector: 'cgdis-portal-general-information-activity-detail',
  templateUrl: './general-information-activity-detail.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    GeneralInformationActivityDetailService,
    GeneralInformationService,
  ],
})
export class GeneralInformationActivityDetailComponent
  implements OnInit, OnDestroy, OnChanges
{
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];
  @Input() exportMenuId: string;
  @Input() logas: boolean;

  @Input() positionLabel: string;
  @Input() entityLabel: string;
  @Input() portalLabel: string;
  @Input() barracked: boolean;
  @Input() isPro: boolean;

  @Input() canExport = false;
  @Input() canExportPrestations = false;
  @Input() canExportPenalty = false;

  reloadChart = false;
  public chartTitle: string;
  public legendItems: LegendItem[];
  public subscriptions: Subscription[] = [];
  public pieChartData: PieChartData[] = [];
  public chartDisplayParams: any;

  availabilityValue = 0;
  interventionValue = 0;
  prestationValue = 0;

  isMobile: boolean = false;

  constructor(
    private service: GeneralInformationActivityDetailService,
    private generalInformationService: GeneralInformationService,
    private cd: ChangeDetectorRef,
    private dateService: DateService,
    private translateService: TranslateService,
    private breakpointObserver: BreakpointObserver,
    private popupService: SimplePopupService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.chartDisplayParams = {
      margin: [10, 10, 10, 10],
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0,
    };
    let prefix = '';

    // Init chart legend
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'dashboard.members.chart.' + prefix + 'fire',
        classes: ['-incomplete'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'dashboard.members.chart.' + prefix + 'ambulance',
        classes: ['-degraded'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'dashboard.members.chart.' + prefix + 'others',
        classes: ['-complete'],
      }),
    ];
    this.subscriptions.push(
      this.translateService.onLangChange.subscribe(() => {
        this.chartTitle = this.capitalizeFirstLetter(
          this.translateService.instant(
            'date.months.' +
              this.dateService.firstDayOfMonth(this.startDate).month +
              '.abr',
          ),
        );
      }),
    );
  }

  /**
   * Capitalize the first letter of a string
   * @param {string} string
   * @returns {string} String
   */
  private capitalizeFirstLetter(string: string): string {
    if (string != undefined) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    } else {
      return string;
    }
  }

  loadStatistics(): void {
    this.reloadChart = true;
    this.cd.markForCheck();

    let result = this.generalInformationService.buildProVolunteerChoice(
      this.proOrAndVolunteer,
    );

    let isPro;
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      isPro = true;
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      isPro = false;
    }
    // Init chart title

    this.service
      .getStatistics(this.personId, this.startDate, this.endDate, isPro)
      .pipe(take(1))
      .subscribe((value: TotalsStatistics) => {
        // Init chart data
        this.availabilityValue =
          (value.repartition.availability
            ? value.repartition.availability
            : 0) * 60;
        this.interventionValue =
          (value.repartition.intervention
            ? value.repartition.intervention
            : 0) * 60;
        this.prestationValue =
          (value.repartition.prestation ? value.repartition.prestation : 0) *
          60;
        this.reloadChart = false;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Convert seconds to duration
   * @param seconds
   */
  public convertSecondsToDuration(seconds: number): string {
    return this.getHours(seconds) + 'h' + this.getMinutes(seconds);
  }

  /**
   * Extract number of minutes in given number of seconds
   * @param seconds
   */
  private getMinutes(seconds: number): string {
    if (seconds <= 0 || seconds === undefined) {
      return '00';
    }

    seconds %= 3600;
    let minutes = Math.floor(seconds / 60);

    if (minutes < 10) {
      return '0' + minutes;
    }
    return '' + minutes;
  }

  /**
   * Get number of hours in given number of seconds
   * @param seconds
   */
  private getHours(seconds: number): number {
    return seconds > 0 ? Math.floor(seconds / 3600) : 0;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes.startDate &&
        changes.startDate.previousValue !== changes.startDate.currentValue) ||
      (changes.endDate &&
        changes.endDate.previousValue !== changes.endDate.currentValue) ||
      this.proOrAndVolunteerHasChanged(changes)
    ) {
      setTimeout(() => this.loadStatistics(), 300);
    }
  }

  private proOrAndVolunteerHasChanged(changes: SimpleChanges) {
    let previousKey: any = undefined;
    let previousValue: any = undefined;
    if (
      changes.proOrAndVolunteer.previousValue != undefined &&
      changes.proOrAndVolunteer.previousValue.length > 0
    ) {
      previousKey = changes.proOrAndVolunteer.previousValue[0];
      previousValue = changes.proOrAndVolunteer.previousValue[1];
    }

    let afterKey: any = undefined;
    let afterValue: any = undefined;
    if (
      changes.proOrAndVolunteer.currentValue != undefined &&
      changes.proOrAndVolunteer.currentValue.length > 0
    ) {
      afterKey = changes.proOrAndVolunteer.currentValue[0];
      afterValue = changes.proOrAndVolunteer.currentValue[1];
    }

    return previousKey !== afterKey || previousValue !== afterValue;
  }

  exportPrestations(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
  ): void {
    this.generalInformationService
      .exportPrestations(
        personId,
        startDate,
        endDate,
        this.isPro,
        this.portalLabel,
        this.positionLabel,
        this.entityLabel,
        this.barracked,
      )
      .subscribe(() => {});
  }

  exportPrestationsPenalty(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
  ): void {
    let popupDialog = this.popupService.open(SimpleYesNoPopupComponent, {
      autoFocus: false,
      data: new SimpleYesNoPopupData({
        title: 'tooltip.export.prestations-penalty',
        message: 'tooltip.export.prestations-penalty-explain',
        messageHtml: true,
        onYes: () => {
          return new Observable((subscriber) => {
            this.generalInformationService
              .exportPrestationsPenalty(personId, startDate, endDate)
              .subscribe(() => {});
            subscriber.next();
          });
        },
        onNo: () => {
          return new Observable((subscriber) => {
            subscriber.next();
          });
        },
      }),
    });
    popupDialog.afterOpened().subscribe(() => {
      document.body.style.overflow = 'hidden';
    });

    popupDialog.afterClosed().subscribe(() => {
      document.body.style.overflow = null;
    });
  }

  exportPrestationsDetail(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
  ): void {
    this.generalInformationService
      .exportPrestationsDetail(
        personId,
        startDate,
        endDate,
        this.isPro,
        this.portalLabel,
        this.positionLabel,
        this.entityLabel,
        this.barracked,
      )
      .subscribe(() => {});
  }

  protected readonly isMatch = isMatch;
}
