<div class="row justify-content-center align-items-center activity-summary pb-5" style="position: relative;">
  <cgdis-portal-spinner [loading]="reloadChart" [fullScreen]="false"></cgdis-portal-spinner>

  <div [ngClass]="{'col-3 col-sm-3 col-md-3 col-lg-2': !isMobile, 'col-4': isMobile }" >
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="isMobile ? 'general_information.activity.detail.prestations_mobile' : 'general_information.activity.detail.prestations'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value">
              <span>{{convertSecondsToDuration(this.prestationValue)}}</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
  </div>

  <div [ngClass]="{'col-3 col-sm-3 col-md-3 col-lg-2': !isMobile, 'col-4': isMobile }" >
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="'general_information.activity.detail.interventions'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value">
              <span>{{convertSecondsToDuration(this.interventionValue)}}</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
  </div>

  <div [ngClass]="{'col-3 col-sm-3 col-md-3 col-lg-2': !isMobile, 'col-4': isMobile }" >
    <div class="performance performance__left">

      <div class="performance__figure">
        <span class="performance__figure__title" [translate]="isMobile ? 'general_information.activity.detail.availabilities_mobile' : 'general_information.activity.detail.availabilities'"></span>

        <div class="performance__figure_content" >
          <ul>
            <li class="statistics-value">
              <span>{{convertSecondsToDuration(this.availabilityValue)}}</span>
            </li>

          </ul>
        </div>
      </div>

    </div>
  </div>

  <div
    [ngClass]="{'text-right': !isMobile,'text-center': isMobile,'col-3 col-sm-3 col-md-3 col-lg-2': !isMobile, 'col-4': isMobile }"
       *ngIf="canExport">
    <div [ngClass]="{'float-right': !isMobile}">
    <cgdis-portal-dropdown-button page-header-download class="export-activity"
                                  id="preferences-export-prestations-button"
                                  [ngClass]="['preferences-export-prestations']"
                                  [menuId]="exportMenuId"
                                  [tooltip]="'tooltip.export.prestations'"
                                  [icon]="'icon-print'">
      <li *ngIf="canExportPrestations" (click)="exportPrestations(personId, startDate, endDate)">
        <a title="{{'tooltip.export.prestations-summary'|translate}}"
           [translate]="'tooltip.export.prestations-summary'"></a>
      </li>
      <li *ngIf="canExportPrestations" (click)="exportPrestationsDetail(personId, startDate, endDate)">
        <a title="{{'tooltip.export.prestations-detail'|translate}}"
           [translate]="'tooltip.export.prestations-detail'"></a>
      </li>
      <li *ngIf="canExportPenalty" (click)="exportPrestationsPenalty(personId, startDate, endDate)">
        <a title="{{'tooltip.export.prestations-penalty'|translate}}"
           [translate]="'tooltip.export.prestations-penalty'"></a>
      </li>
    </cgdis-portal-dropdown-button>
    </div>
  </div>
</div>
