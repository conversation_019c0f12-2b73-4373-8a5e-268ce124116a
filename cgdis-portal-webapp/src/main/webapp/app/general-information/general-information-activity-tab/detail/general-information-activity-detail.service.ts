import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DateModel, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { TotalsStatistics } from '@app/model/totals-statistics.model';

/**
 * Access intervention types
 */
@Injectable()
export class GeneralInformationActivityDetailService {
  /**
   * Based url to access prestation services
   * @type {string}
   */
  private baseUrlPrestation = ['totals', 'summary'];

  constructor(private restService: RestService) {}

  /**
   * Get availability duration
   * @param personId: the person id
   * @param startDate: the start date
   * @param endDate: the end date
   */
  getStatistics = (
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
    isPro: Boolean,
  ): Observable<TotalsStatistics> => {
    const restResource = this.restService.one(...this.baseUrlPrestation);
    let params: any = {
      personId: personId,
      startDate: startDate,
      endDate: endDate,
    };
    if (isPro != undefined) {
      params.isPro = isPro;
    }
    return restResource.get(params).pipe(
      map((value: TotalsStatistics) => {
        return value;
      }),
    );
  };
}
