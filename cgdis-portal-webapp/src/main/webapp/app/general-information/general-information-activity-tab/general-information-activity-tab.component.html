<div class="section bottom-none">
  <div class="col-sm-12 col-md-11 col-lg-9 col-xl-7 home-nav col-md-9" [ngClass]="{'col-12' : !isMobile}">
    <div class="col-md-3 col-lg-3 col-xl-4">
      <span class="accordion__title" [translate]="'general_information.activity.type'"></span>
      <cgdis-portal-pro-volunteer-choice (itemValueEmitter)="proVolunteerEventHandler($event)" [personId]="personId"
                                         [from]="startDate" [to]="endDate"
                                         [logas]="logas"></cgdis-portal-pro-volunteer-choice>
    </div>
    <div class="col-md-10 col-lg-9 col-xl-8">
      <span class="accordion__title" [translate]="'general_information.activity.periode'"></span>
      <cgdis-portal-custom-range (datesChangeEmitter)="datesChangeEventHandler($event)"></cgdis-portal-custom-range>
    </div>
  </div>
</div>

<div class="section bottom-none" [ngClass]="{'pt-5': !isMobile}">
  <div >
    <cgdis-portal-general-information-activity-detail
      [personId]="personId"
      [startDate]="startDate"
      [endDate]="endDate"
      [proOrAndVolunteer]="proVolunteerChoice"
      [exportMenuId]="exportMenuId"
      [logas]="logas"
      [isPro]="isPro"
      [portalLabel]="portalLabel"
      [positionLabel]="positionLabel"
      [entityLabel]="entityLabel"
      [barracked]="barracked"
      [canExport]="canExport"
      [canExportPrestations]="canExportPrestations"
      [canExportPenalty]="canExportPenalty">
    </cgdis-portal-general-information-activity-detail>
  </div>
</div>


<div >
  <div id="accordion">
    <mat-accordion>

      <!-- Prestations -->
      <mat-expansion-panel class="accordion__group" [expanded]="true">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0 d-block">
            <span class="accordion__title" [translate]="'general_information.activity.prestations.title'"></span>
            <i [matTooltip]="'warning_allowances' | translate" [matTooltipShowDelay]="200" cgdis-portal-icon class="fa fa-info-circle px-3"
               #tooltip="matTooltip" (click)="tooltip.show()" type="button">
            </i>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-prestation-information
              [personId]="personId"
              [startDate]="startDate"
              [endDate]="endDate"
              [proOrAndVolunteer]="proVolunteerChoice"
              (portalLabelUpdated)="updatedPortalLabel($event)"
              (positionLabelUpdated)="updatedPositionLabel($event)"
              (entityLabelUpdated)="updatedEntityLabel($event)"
              (barrackedLabelUpdated)="updatedBarracked($event)">
            </cgdis-portal-prestation-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Interventions -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">

        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.activity.interventions.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <ng-template matExpansionPanelContent>
          <div class="collapse show">
            <div class="card-body">
              <cgdis-portal-interventions-information [personId]="personId" [startDate]="startDate" [endDate]="endDate"
                                                      [proOrAndVolunteer]="proVolunteerChoice"></cgdis-portal-interventions-information>
            </div>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Availabilities -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.activity.availabilities.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-availabilities-information [personId]="personId" [startDate]="startDate" [endDate]="endDate"
                                                     [proOrAndVolunteer]="proVolunteerChoice"></cgdis-portal-availabilities-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <!-- Total -->
      <mat-expansion-panel class="accordion__group" [expanded]="false">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0 d-block">
            <span class="accordion__title" [translate]="'general_information.activity.totals.title'"></span>
            <i [matTooltip]="'warning_allowances' | translate" [matTooltipShowDelay]="200" cgdis-portal-icon class="fa fa-info-circle px-3"
               #tooltip="matTooltip" (click)="tooltip.show()" type="button">
            </i>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-totals-information [personId]="personId" [startDate]="startDate" [endDate]="endDate"
                                             [proOrAndVolunteer]="proVolunteerChoice"></cgdis-portal-totals-information>
          </div>
        </ng-template>
      </mat-expansion-panel>

    </mat-accordion>
  </div>
</div>
