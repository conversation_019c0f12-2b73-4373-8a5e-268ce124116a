import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { DateModel } from '@eportal/core';
import { PRO_VOLUNTEER_BOTH } from './pro-volunteer-choice/pro-volunteer-choice.component';
import { GeneralInformationService } from '@app/general-information/general-information.service';
import * as _ from 'lodash';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { ConnectedUserService } from '@app/security/connected-user.service';

@Component({
  selector: 'cgdis-portal-general-information-activity-tab',
  templateUrl: './general-information-activity-tab.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GeneralInformationService],
})
export class GeneralInformationActivityTabComponent
  implements OnInit, OnD<PERSON>roy
{
  /**
   * The person number
   */
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() logas: boolean;
  @Input() proVolunteerChoice: [string, boolean] = [
    PRO_VOLUNTEER_BOTH.both,
    true,
  ];

  public canExport = false;
  public canExportPrestations = false;
  public canExportPenalty = false;
  public positionLabel: string;
  public entityLabel: string;
  public portalLabel: string;
  public barracked: boolean;
  public isPro: boolean;
  public exportMenuId = 'schedule-export-pdf-switcher';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
    private generalInformationService: GeneralInformationService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.canExportPenalty = this.connectedUserService.hasAnyRoles(
      this.logas
        ? [
            'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY',
          ]
        : ['ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY'],
    );
    this.canExportPrestations = this.connectedUserService.hasAnyRoles(
      this.logas
        ? ['ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS']
        : ['ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS'],
    );
    this.canExport = this.canExportPrestations || this.canExportPenalty;
    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Handles custom date range events
   * @param $event the event emitted when the either startDate or/and endDate changes, represented as a map.
   */
  datesChangeEventHandler($event: Map<string, DateModel>) {
    this.startDate = $event.get('dateFrom');
    this.endDate = $event.get('dateTo');
    this.cd.markForCheck();
  }

  proVolunteerEventHandler($event: [string, boolean]) {
    this.proVolunteerChoice = $event;
    if (!_.isEmpty(this.proVolunteerChoice)) {
      switch (this.proVolunteerChoice[0]) {
        case PRO_VOLUNTEER_BOTH.is_professional:
          this.isPro = true;
          break;
        case PRO_VOLUNTEER_BOTH.is_volunteer:
          this.isPro = false;
          break;
        case PRO_VOLUNTEER_BOTH.both:
        default:
          this.isPro = null;
          break;
      }
    }
  }

  updatedPortalLabel(event: string) {
    this.portalLabel = event;
  }

  updatedEntityLabel(event: string) {
    this.entityLabel = event;
  }

  updatedPositionLabel(event: string) {
    this.positionLabel = event;
  }

  updatedBarracked(event: boolean) {
    this.barracked = event;
  }
}
