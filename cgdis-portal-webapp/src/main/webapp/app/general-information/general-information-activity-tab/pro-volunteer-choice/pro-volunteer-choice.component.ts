import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ConnectedUserService } from '../../../security/connected-user.service';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { Assignment } from '@app/model/assignment.model';
import { DateModel } from '@eportal/core';

export enum PRO_VOLUNTEER_BOTH {
  is_professional = 'is_professional',
  is_volunteer = 'is_volunteer',
  both = 'both',
}
export const PREFIX = 'general_information.personal_information.';
@Component({
  selector: 'cgdis-portal-pro-volunteer-choice',
  templateUrl: './pro-volunteer-choice.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styles: [],
})
export class ProVolunteerChoiceComponent implements OnInit, OnChanges {
  @Input() personId: number;
  @Input() logas: boolean;
  @Input() from: DateModel;
  @Input() to: DateModel;
  @Output() itemValueEmitter: EventEmitter<[string, boolean]> =
    new EventEmitter();
  labelKey: string;
  items: Map<string, boolean> = new Map<string, boolean>();
  itemSelected: [string, boolean];
  isProfessional = false;
  isVolunteer = false;

  constructor(
    private cd: ChangeDetectorRef,
    private userService: ConnectedUserService,
    private assignmentService: AssignmentService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      this.personId != undefined &&
      this.logas != undefined &&
      this.from != undefined &&
      this.to != undefined
    ) {
      this.loadAvailableStatuses();
    }
  }

  ngOnInit() {}

  private loadAvailableStatuses() {
    this.assignmentService
      .getAllAssignmentForPerson(this.personId, this.logas, this.from, this.to)
      .subscribe((assignments: Assignment[]) => {
        this.isProfessional = false;
        this.isVolunteer = false;
        for (let assignment of assignments) {
          if (assignment.type === 'PRO' || assignment.type === 'EXT') {
            this.isProfessional = true;
          }
          if (assignment.type === 'VOL') {
            this.isVolunteer = true;
          }
        }
        this.initChoice();
      });
  }

  selectFilter(item: [string, boolean]) {
    this.labelKey = this.buildLabelKey(item);
    this.itemSelected = item;
    this.onItemChange();
  }

  buildLabelKey(item: [string, boolean]): string {
    return PREFIX + item[0];
  }

  onItemChange(): void {
    this.itemValueEmitter.emit(this.itemSelected);
  }

  initChoice(): void {
    this.items = new Map<string, boolean>();
    if (this.isProfessional && !this.isVolunteer) {
      this.items.set(PRO_VOLUNTEER_BOTH.is_professional, true);
    }
    if (!this.isProfessional && this.isVolunteer) {
      this.items.set(PRO_VOLUNTEER_BOTH.is_volunteer, true);
    }
    if (this.isProfessional && this.isVolunteer) {
      this.items.set(PRO_VOLUNTEER_BOTH.both, true);
      this.items.set(PRO_VOLUNTEER_BOTH.is_professional, true);
      this.items.set(PRO_VOLUNTEER_BOTH.is_volunteer, true);
    }

    if (this.itemSelected && this.itemSelected.length > 0) {
      // compare la valeur courante avec la nouvelle valeur
      const currentSelectedKey = this.itemSelected[0];
      const currentSelectedValue = this.itemSelected[1];

      if (
        this.items.get(currentSelectedKey) &&
        this.items.get(currentSelectedKey) !== currentSelectedValue
      ) {
        this.itemSelected = this.items.entries().next().value;
      }
    } else {
      this.itemSelected = this.items.entries().next().value;
    }

    if (this.itemSelected != undefined) {
      this.selectFilter(this.itemSelected);
    }
  }
}
