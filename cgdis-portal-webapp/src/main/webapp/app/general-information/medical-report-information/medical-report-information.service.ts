import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonMedicalReport } from '@app/model/person-medical-report.model';

@Injectable()
export class MedicalReportInformationService {
  /**
   * Based url to access one service plan
   * @type {string}
   */
  baseUrl = ['person-medical-report'];

  constructor(
    private httpClient: HttpClient,
    private _restService: RestService,
  ) {}

  /**
   * Get one service plan
   * @param {number} tecid: the service plan id
   * @return the service plan
   */
  getForPerson(tecid: number): Observable<PersonMedicalReport[]> {
    return this._restService
      .one<PersonMedicalReport[]>(...this.baseUrl, 'all', String(tecid))
      .get()
      .pipe(
        map((value: PersonMedicalReport[]) => {
          return value;
        }),
      );
  }
}
