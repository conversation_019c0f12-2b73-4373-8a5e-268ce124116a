import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import { PersonMedicalReport } from '@app/model/person-medical-report.model';
import { MedicalReportInformationService } from '@app/general-information/medical-report-information/medical-report-information.service';
import { Subscription } from 'rxjs';
import { DateModel, DateService } from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-medical-report-information',
  templateUrl: './medical-report-information.component.html',
  providers: [MedicalReportInformationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MedicalReportInformationComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  medicalReports: PersonMedicalReport[];

  subscritptions: Subscription[] = [];

  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private service: MedicalReportInformationService,
    private cd: ChangeDetectorRef,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.subscritptions.push(
      this.service
        .getForPerson(this.personId)
        .subscribe((reports: PersonMedicalReport[]) => {
          this.medicalReports = reports;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
