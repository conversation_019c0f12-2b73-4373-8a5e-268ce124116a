
<mat-expansion-panel class="accordion__group" *ngFor="let report of medicalReports; first as $first;" [expanded]="$first">
    <mat-expansion-panel-header class="general-information-header" role="heading">
        <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'general_information.medical-report.title'" [translateParams]="{publicationDate : getFormattedDate(report.publicationDate)}"></span>
        </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
        <div class="card-body accordion__panel">
            <cgdis-portal-medical-report-information-detail [report]="report"></cgdis-portal-medical-report-information-detail>
        </div>
    </ng-template>
</mat-expansion-panel>
<mat-expansion-panel *ngIf="!medicalReports || medicalReports.length == 0" class="accordion__group" >
  <mat-expansion-panel-header class="general-information-header" role="heading">
    <mat-panel-title class="mb-0">
      <span class="accordion__title" [translate]="'general_information.medical-report.empty'" ></span>
    </mat-panel-title>
  </mat-expansion-panel-header>
</mat-expansion-panel>
