import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { PersonMedicalReport } from '@app/model/person-medical-report.model';
import { DateModel, DateService } from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-medical-report-information-detail',
  templateUrl: './medical-report-information-detail.component.html',
  providers: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MedicalReportInformationDetailComponent
  implements OnInit, OnDestroy
{
  @Input() report: PersonMedicalReport;

  aptitudesNotEmpty = false;

  restrictionsNotEmpty = false;

  startDateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.startDateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    if (this.report.aptitudes != null && this.report.aptitudes.length > 0) {
      this.aptitudesNotEmpty = true;
    }

    if (
      this.report.restrictions != null &&
      this.report.restrictions.length > 0
    ) {
      this.restrictionsNotEmpty = true;
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedStartDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.startDateFormat);
    } else {
      return '-';
    }
  }
}
