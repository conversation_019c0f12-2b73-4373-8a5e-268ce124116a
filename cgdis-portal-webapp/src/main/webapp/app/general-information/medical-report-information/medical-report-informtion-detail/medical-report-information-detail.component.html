<h3 *ngIf="!aptitudesNotEmpty">{{'general_information.medical-report.no-aptitude' | translate}}</h3>
<h3 *ngIf="aptitudesNotEmpty">{{'general_information.medical-report.new-aptitudes' | translate}}</h3>
<table *ngIf="aptitudesNotEmpty" class="medico_table">
    <tbody><tr>
        <th style="width:40%">{{'general_information.medical-report.aptitude' | translate}}</th>
        <th style="width:30%">{{'general_information.medical-report.start' | translate}}</th>
        <th style="width:30%">{{'general_information.medical-report.statut' | translate}}</th>
    </tr>
    <tr *ngFor="let aptitude of report.aptitudes;">
        <td style="width:40%"><span >{{('i18n.data.aptitudes.' + aptitude.aptitude.externalId.split(' ').join('')) | translate}}</span></td>
        <td style="width:30%"><span >{{getFormattedStartDate(aptitude.startDate)}}</span></td>
        <td style="width:30%"><span >{{aptitude.statut.name}}</span></td>
    </tr>

    </tbody></table>
<br>
<h3 *ngIf="!restrictionsNotEmpty">{{'general_information.medical-report.no-restriction' | translate}}</h3>
<h3 *ngIf="restrictionsNotEmpty">{{'general_information.medical-report.new-restrictions' | translate}}</h3>
<table *ngIf="restrictionsNotEmpty" class="medico_table">
    <tbody><tr>
        <th style="width:70%">{{'general_information.medical-report.restriction' | translate}}</th>
        <th style="width:30%">{{'general_information.medical-report.start' | translate}}</th>
    </tr>
    <tr *ngFor="let restriction of report.restrictions;">
        <td style="width:70%"><span >{{('i18n.data.restrictions.' + restriction.restriction.externalId.split(' ').join('')) | translate}}</span></td>
        <td style="width:30%"><span >{{getFormattedStartDate(restriction.startDate)}}</span></td>
    </tr>
    </tbody></table>
<br>
<p *ngIf="report.remark != null" >
    <b>{{'general_information.medical-report.remark' | translate}}</b> <span >{{report.remark.remark}}</span>
</p>
<p *ngIf="report.remark == null" >
    <b>{{'general_information.medical-report.remark' | translate}}</b> -
</p>
