import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
} from '@angular/core';
import { RolesInformationService } from './roles-information.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-roles-information',
  templateUrl: './roles-information.component.html',
  providers: [RolesInformationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RolesInformationComponent implements OnDestroy {
  excluedDomain = [
    '_AdmGroups',
    'CGDISPool',
    ' AdmGroups',
    'CGDIS POOL',
    'PortailCGDISTest',
    'PortailCGDISProd',
  ];

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public rolesService: RolesInformationService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  isExclued(domain: any): boolean {
    return this.excluedDomain.indexOf(domain) > -1;
  }
}
