import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { RolesInformationComponent } from './roles-information.component';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { RolesInformationDetailComponent } from '@app/general-information/roles-information/detail/roles-information-detail.component';

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    FormModule,
    DatatableModule,
    EpDatatableModule,
  ],
  declarations: [RolesInformationComponent, RolesInformationDetailComponent],
  exports: [RolesInformationComponent, RolesInformationDetailComponent],
})
export class RolesInformationModule {}
