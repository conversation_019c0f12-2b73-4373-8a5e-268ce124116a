import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { SecurityRole } from '@app/model/security-role.model';

@Component({
  selector: 'cgdis-portal-roles-information-detail',
  templateUrl: './roles-information-detail.component.html',
  styleUrls: ['./_roles-information-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RolesInformationDetailComponent {
  @Input() role: SecurityRole;

  constructor() {}
}
