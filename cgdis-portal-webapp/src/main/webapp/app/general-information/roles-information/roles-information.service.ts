import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { Location } from '@angular/common';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { SecurityRole } from '../../model/security-role.model';

@Injectable()
export class RolesInformationService extends CgdisDatatableService<SecurityRole> {
  constructor(
    restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(restService.all('person', 'roles'));
  }
}
