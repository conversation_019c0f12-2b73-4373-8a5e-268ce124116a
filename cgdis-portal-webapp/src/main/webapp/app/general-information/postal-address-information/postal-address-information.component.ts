import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { PersonGeneralInformation } from '../../model/person/person-general-information.model';
import { PostalAddressInformationService } from './postal-address-information.service';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-postal-address-information',
  templateUrl: './postal-address-information.component.html',
  styleUrls: ['./_postal-address-information.scss'],
  providers: [PostalAddressInformationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostalAddressInformationComponent implements OnInit {
  /**
   * The person id.
   */
  @Input() personId: number;

  initialData: PersonGeneralInformation;
  loading = true;

  constructor(
    private service: PostalAddressInformationService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.service
      .get(this.personId)
      .pipe(take(1))
      .subscribe((value: PersonGeneralInformation) => {
        this.initialData = value;
        this.loading = false;
        this.cd.markForCheck();
      });
  }
}
