<div style="height:200px" *ngIf="!initialData; else address">
  <cgdis-portal-spinner [loading]="loading" ></cgdis-portal-spinner>
</div>
<ng-template #address>
  <div id="accordion1panel-address" aria-hidden="false" role="region" aria-labelledby="accordion1" class="accordion__panel">
    <div *ngFor="let address of initialData.addresses; index as $index">

      <div class="row" *ngIf="initialData.addresses.length > 1">
        <div class="col-sm-3 col-lg-2">
          <span [translate]="'general_information.address.subtitle'" [translateParams]="{number: $index + 1}"></span>
        </div>
      </div>

      <table>
        <tr>
          <td class="list-label">
            <span>{{'general_information.address.number' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{address.streetNumber | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.address.street' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{address.street | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.address.zip_code' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{address.zipCode | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.address.city' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{address.city | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.address.country' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{address.countryCode | defaultValue:"-"}}</span>
          </td>
        </tr>

      </table>



    </div>
  </div>
</ng-template>
