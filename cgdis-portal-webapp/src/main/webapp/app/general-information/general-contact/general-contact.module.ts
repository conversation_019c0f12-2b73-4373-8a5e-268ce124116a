import { NgModule } from '@angular/core';
import { SharedModule } from '../../common/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimplePopupModule } from '../../common/modules/popup/simple-popup.module';
import { GeneralContactComponent } from './general-contact.component';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    FormModule,
    SimplePopupModule,
  ],
  declarations: [GeneralContactComponent],
  exports: [GeneralContactComponent],
})
export class GeneralContactModule {}
