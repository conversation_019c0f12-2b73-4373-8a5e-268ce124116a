import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { UpdateGeneralContactService } from './update-general-contact.service';
import { GeneralContactInformation } from '@app/model/general-contact-information.model';
import { ValidatorFn } from '@angular/forms';
import { CustomValidatorsService } from '@app/common/modules/form-module/validator/custom-validators.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { Subject, Subscription } from 'rxjs';
import { GeneralContactInformationService } from './general-contact-information.service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-general-contact-form',
  templateUrl: './general-contact.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    UpdateGeneralContactService,
    GeneralContactInformationService,
    {
      provide: FORM_SERVICE,
      useExisting: UpdateGeneralContactService,
    },
  ],
})
export class GeneralContactComponent implements OnInit, OnDestroy {
  /**
   * The initial data
   */
  @Input() personId: number;

  /**
   * The form ID
   */
  @Input() formId: string;

  @Input() updateRoles: string[];

  initialData: GeneralContactInformation;
  canEdit = true;
  canEditGSMPager = false;
  canViewGSMPager = false;

  private rolesSubscription: Subscription;
  buttonBlockClasses = {};
  phoneValidator: ValidatorFn[];
  emailValidator: ValidatorFn[];
  pagerValidator: ValidatorFn[];
  pagerMobileValidator: ValidatorFn[];
  readOnlyToogleMail = false;
  readOnlyToogleMobile = false;
  valueMobilePrivate: string;
  valueMobilePro: string;
  valueMailPrivate: string;
  valueMailPro: string;
  public sendEmailValues: FieldOption<string>[];
  public sendEmailNotificationValues: FieldOption<string>[];
  public sendErrorEmailNotificationValues: FieldOption<boolean>[];
  public sendMobileValues: FieldOption<string>[];
  public sendEmailInitialValue = 'PRIVATE';
  public sendEmailNotificationInitialValue = 'PRIVATE';
  public sendErrorEmailNotificationInitialValue = false;
  public sendMobileInitialValue = 'PRIVATE';
  public noMail = false;
  @Output() noMailEvent = new EventEmitter<boolean>();
  public noMailPro = false;
  public noMailPrivate = false;
  public showErrorEmailNotification = false;

  private _unsubscribe$ = new Subject<void>();

  constructor(
    public service: UpdateGeneralContactService,
    private cd: ChangeDetectorRef,
    private generalContactInformationService: GeneralContactInformationService,
    private validatorService: CustomValidatorsService,
    private connectedUserService: ConnectedUserService,
  ) {}

  ngOnInit(): void {
    // get initial data
    if (!this.personId) {
      this.personId = this.connectedUserService.getConnectedUserId();
    }

    this.generalContactInformationService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((initialData) => {
        this.initialData = initialData.generalContactInformation;
        this.initialData.sendPrivateMobile
          ? (this.sendMobileInitialValue = 'PRIVATE')
          : (this.sendMobileInitialValue = 'PROFESSIONAL');
        this.initialData.sendPrivateEmail
          ? (this.sendEmailInitialValue = 'PRIVATE')
          : (this.sendEmailInitialValue = 'PROFESSIONAL');
        this.initialData.sendPrivateEmailNotification
          ? (this.sendEmailNotificationInitialValue = 'PRIVATE')
          : (this.sendEmailNotificationInitialValue = 'PROFESSIONAL');
        this.sendErrorEmailNotificationInitialValue =
          this.initialData.sendErrorEmailNotification;
        this.showErrorEmailNotification =
          this.sendErrorEmailNotificationInitialValue;
        this.valueMailPro = this.initialData.professionalEmail;
        this.valueMailPrivate = this.initialData.privateEmail;
        this.valueMobilePro = this.initialData.professionalMobileNumber;
        this.valueMobilePrivate = this.initialData.privateMobileNumber;
        this.changeMailPro(this.valueMailPro);
        this.changeMobilePro(this.valueMobilePro);
        this.cd.markForCheck();
      });

    this.buttonBlockClasses = { 'col-md-4': true, 'width-80': true };

    this.service
      .onModelUpdated()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((model: GeneralContactInformation) => {
        this.initialData = model;

        this.initialData.sendPrivateMobile
          ? (this.sendMobileInitialValue = 'PRIVATE')
          : (this.sendMobileInitialValue = 'PROFESSIONAL');
        this.initialData.sendPrivateEmail
          ? (this.sendEmailInitialValue = 'PRIVATE')
          : (this.sendEmailInitialValue = 'PROFESSIONAL');
        this.initialData.sendPrivateEmailNotification
          ? (this.sendEmailNotificationInitialValue = 'PRIVATE')
          : (this.sendEmailNotificationInitialValue = 'PROFESSIONAL');
        this.sendErrorEmailNotificationInitialValue =
          this.initialData.sendErrorEmailNotification;
        this.showErrorEmailNotification =
          this.sendErrorEmailNotificationInitialValue;
        this.valueMailPro = this.initialData.professionalEmail;
        this.valueMailPrivate = this.initialData.privateEmail;
        this.valueMobilePro = this.initialData.professionalMobileNumber;
        this.valueMobilePrivate = this.initialData.privateMobileNumber;
        this.changeMailPro(this.valueMailPro);
        this.changeMobilePro(this.valueMobilePro);
      });

    this.phoneValidator = [
      this.validatorService.validatePhoneNumberWithoutRequired(),
    ];
    this.emailValidator = [
      this.validatorService.validateEmailWithoutRequired(),
    ];
    this.pagerValidator = [
      this.validatorService.validatePagerWithoutRequired(),
    ];
    this.pagerMobileValidator = [
      this.validatorService.validatePagerMobileWithoutRequired(),
    ];

    if (this.updateRoles != undefined) {
      this.rolesSubscription = this.connectedUserService
        .hasAnyRolesObservable(this.updateRoles)
        .subscribe((newValue) => {
          this.canEdit = !newValue;
          this.cd.markForCheck();
        });
    }

    this.rolesSubscription = this.connectedUserService
      .hasAnyRolesObservable(['ROLE_PERMISSION_PAGER_UPDATE'])
      .subscribe((newValue) => {
        this.canEditGSMPager = newValue;
        this.cd.markForCheck();
      });

    this.rolesSubscription = this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PAGER_VIEW',
        'ROLE_PERMISSION_PAGER_UPDATE',
      ])
      .subscribe((newValue) => {
        this.canViewGSMPager = newValue;
        this.cd.markForCheck();
      });

    this.initSendEmailValues();
    this.initSendMobileValues();
    this.initSendEmailNotificationValues();
    this.initSendErrorEmailNotificationValues();
  }

  ngOnDestroy(): void {
    if (this.rolesSubscription != undefined) {
      this.rolesSubscription.unsubscribe();
    }
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  private initSendEmailValues(): void {
    this.sendEmailValues = [
      new FieldOption({
        I18NLabel: 'general_information.contact_information.professional.email',
        value: 'PROFESSIONAL',
      }),
      new FieldOption({
        I18NLabel: 'general_information.contact_information.private.email',
        value: 'PRIVATE',
      }),
    ];
  }

  private initSendEmailNotificationValues(): void {
    this.sendEmailNotificationValues = [
      new FieldOption({
        I18NLabel: 'general_information.contact_information.professional.email',
        value: 'PROFESSIONAL',
      }),
      new FieldOption({
        I18NLabel: 'general_information.contact_information.private.email',
        value: 'PRIVATE',
      }),
    ];
  }

  private initSendErrorEmailNotificationValues(): void {
    this.sendErrorEmailNotificationValues = [
      new FieldOption({
        I18NLabel: 'default.yes',
        value: true,
      }),
      new FieldOption({
        I18NLabel: 'default.no',
        value: false,
      }),
    ];
  }

  private initSendMobileValues(): void {
    this.sendMobileValues = [
      new FieldOption({
        I18NLabel:
          'general_information.contact_information.professional.mobile',
        value: 'PROFESSIONAL',
      }),
      new FieldOption({
        I18NLabel: 'general_information.contact_information.private.mobile',
        value: 'PRIVATE',
      }),
    ];
  }

  public change(field: string, event: any) {
    switch (field) {
      case 'mailPro': {
        this.changeMailPro(event);
        this.cd.markForCheck();
        break;
      }
      case 'mailPrivate': {
        this.changeMailPrivate(event);
        this.cd.markForCheck();
        break;
      }
      case 'mobilePro': {
        this.changeMobilePro(event);
        this.cd.markForCheck();
        break;
      }
      case 'mobilePrivate': {
        this.changeMobilePrivate(event);
        this.cd.markForCheck();
        break;
      }
      default: {
        break;
      }
    }
  }

  public submit() {
    this.noMail = this.noMailPrivate && this.noMailPro;

    this.noMailEvent.emit(this.noMail);
  }

  public cancel() {
    this.noMail = false;
  }

  private changeMailPro(value: string) {
    this.valueMailPro = value;
    if (this.valueMailPro == undefined || this.valueMailPro.length === 0) {
      if (
        this.valueMailPrivate == undefined ||
        this.valueMailPrivate.length === 0
      ) {
        this.readOnlyToogleMail = true;
        this.noMailPro = true;
        this.noMailPrivate = true;
      } else {
        this.sendEmailInitialValue = 'PRIVATE';
        this.sendEmailNotificationInitialValue = 'PRIVATE';
        this.readOnlyToogleMail = true;
        this.noMailPro = true;
        this.noMailPrivate = false;
      }
    } else {
      if (
        this.valueMailPrivate == undefined ||
        this.valueMailPrivate.length === 0
      ) {
        this.sendEmailInitialValue = 'PROFESSIONAL';
        this.sendEmailNotificationInitialValue = 'PROFESSIONAL';
        this.readOnlyToogleMail = true;
        this.noMailPro = false;
        this.noMailPrivate = true;
      } else {
        this.readOnlyToogleMail = false;
        this.noMailPro = false;
        this.noMailPrivate = false;
      }
    }
    this.cd.markForCheck();
  }

  private changeMailPrivate(value: string) {
    this.valueMailPrivate = value;
    if (
      this.valueMailPrivate == undefined ||
      this.valueMailPrivate.length === 0
    ) {
      if (this.valueMailPro == undefined || this.valueMailPro.length === 0) {
        this.readOnlyToogleMail = true;
        this.noMailPro = true;
        this.noMailPrivate = true;
      } else {
        this.sendEmailInitialValue = 'PROFESSIONAL';
        this.sendEmailNotificationInitialValue = 'PROFESSIONAL';
        this.readOnlyToogleMail = true;
        this.noMailPro = false;
        this.noMailPrivate = true;
      }
    } else {
      if (this.valueMailPro == undefined || this.valueMailPro.length === 0) {
        this.sendEmailInitialValue = 'PRIVATE';
        this.sendEmailNotificationInitialValue = 'PRIVATE';
        this.readOnlyToogleMail = true;
        this.noMailPro = true;
        this.noMailPrivate = false;
      } else {
        this.readOnlyToogleMail = false;
        this.noMailPro = false;
        this.noMailPrivate = false;
      }
    }
    this.cd.markForCheck();
  }

  private changeMobilePro(value: string) {
    this.valueMobilePro = value;
    if (this.valueMobilePro == undefined || this.valueMobilePro.length === 0) {
      if (
        this.valueMobilePrivate == undefined ||
        this.valueMobilePrivate.length === 0
      ) {
        this.readOnlyToogleMobile = true;
      } else {
        this.sendMobileInitialValue = 'PRIVATE';
        this.readOnlyToogleMobile = true;
      }
    } else {
      if (
        this.valueMobilePrivate == undefined ||
        this.valueMobilePrivate.length === 0
      ) {
        this.sendMobileInitialValue = 'PROFESSIONAL';
        this.readOnlyToogleMobile = true;
      } else {
        this.readOnlyToogleMobile = false;
      }
    }
    this.cd.markForCheck();
  }

  private changeMobilePrivate(value: string) {
    this.valueMobilePrivate = value;
    if (
      this.valueMobilePrivate == undefined ||
      this.valueMobilePrivate.length === 0
    ) {
      if (
        this.valueMobilePro == undefined ||
        this.valueMobilePro.length === 0
      ) {
        this.readOnlyToogleMobile = true;
      } else {
        this.sendMobileInitialValue = 'PROFESSIONAL';
        this.readOnlyToogleMobile = true;
      }
    } else {
      if (
        this.valueMobilePro == undefined ||
        this.valueMobilePro.length === 0
      ) {
        this.sendMobileInitialValue = 'PRIVATE';
        this.readOnlyToogleMobile = true;
      } else {
        this.readOnlyToogleMobile = false;
      }
    }
    this.cd.markForCheck();
  }

  isValid(info: string): boolean {
    return info != null && info != undefined && info.length >= 1;
  }
}
