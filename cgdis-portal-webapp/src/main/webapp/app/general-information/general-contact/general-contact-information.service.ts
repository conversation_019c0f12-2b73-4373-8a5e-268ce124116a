import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonGeneralInformation } from '../../model/person/person-general-information.model';

@Injectable()
export class GeneralContactInformationService {
  /**
   * The base url to access person services
   * @type {string[]}
   *
   */
  baseUrl = ['person'];

  constructor(private _restService: RestService) {}

  /**
   * Get general information of the connected user
   * @returns {Observable<PersonGeneralInformation>}
   */
  get(personId: number): Observable<PersonGeneralInformation> {
    return this._restService
      .one<PersonGeneralInformation>(
        ...this.baseUrl,
        'generalcontactinformation',
        String(personId),
      )
      .get()
      .pipe(
        map((value: PersonGeneralInformation) => {
          return value;
        }),
      );
  }
}
