<ng-container *ngIf="initialData">
  <cgdis-portal-form [formId]="'general-contact-id'" [formClasses]="['row']" [buttonBlockClasses]="buttonBlockClasses" [hideFormActions]="canEdit" [formReadonly]="canEdit"
  (onCancel)="cancel()" (onSubmitEmitter)="submit()">
    <cgdis-portal-number-field
      [name]="'tecid'"
      [initialValue]="initialData.tecid"
      [visible]="false"
      [disableIfNotVisible]="false">
    </cgdis-portal-number-field>

    <cgdis-portal-number-field
      [name]="'teclock'"
      [initialValue]="initialData.teclock"
      [visible]="false"
      [disableIfNotVisible]="false">
    </cgdis-portal-number-field>

    <!--<div *ngIf="noMail" class="row general-contact-mobile-padding">
      <div *ngIf="noMail" class="col-sm-9 col-lg-9 mb-5 mb-sm-5 mb-lg-5 alert-mail-profil">
        <span [translate]="'general_information.contact_information.warning_no_mail'"> ></span>
      </div>
    </div> -->

    <div class="row general-contact-mobile-padding">
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'privatePhoneNumber'"
          [customValidators]="phoneValidator"
          [fieldMaxLength]="20"
          [labelKey]="'general_information.contact_information.private.phone'"
          [placeholder]="'-'"
          [initialValue]="initialData.privatePhoneNumber" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'privateMobileNumber'"
          [customValidators]="phoneValidator"
          [fieldMaxLength]="20"
          (onValueChange)="change('mobilePrivate', $event)"
          [labelKey]="'general_information.contact_information.private.mobile'"
          [placeholder]="'-'"
          [initialValue]="initialData.privateMobileNumber" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'privateEmail'"
          [customValidators]="emailValidator"
          [fieldMaxLength]="150"
          (onValueChange)="change('mailPrivate', $event)"
          [labelKey]="'general_information.contact_information.private.email'"
          [placeholder]="'-'"
          [initialValue]="initialData.privateEmail" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
    </div>

    <div class="row general-contact-mobile-padding">
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'professionalPhoneNumber'"
          [customValidators]="phoneValidator"
          [fieldMaxLength]="20"
          [labelKey]="'general_information.contact_information.professional.phone'"
          [placeholder]="'-'"
          [initialValue]="initialData.professionalPhoneNumber" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'professionalMobileNumber'"
          [customValidators]="phoneValidator"
          [fieldMaxLength]="20"
          (onValueChange)="change('mobilePro', $event)"
          [labelKey]="'general_information.contact_information.professional.mobile'"
          [placeholder]="'-'"
          [initialValue]="initialData.professionalMobileNumber" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'professionalEmail'"
          [customValidators]="emailValidator"
          [fieldMaxLength]="150"
          (onValueChange)="change('mailPro', $event)"
          [labelKey]="'general_information.contact_information.professional.email'"
          [placeholder]="'-'"
          [initialValue]="initialData.professionalEmail" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
    </div>


    <div class="row general-contact-mobile-padding">
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field
          [fieldReadonly]="false"
          [name]="'ric'"
          [customValidators]="pagerValidator"
          [fieldMinLength]="7"
          [fieldMaxLength]="7"
          [labelKey]="'general_information.contact_information.ric'"
          [placeholder]="'-'"
          [initialValue]="initialData.ric"
          class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>

      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-input-field *ngIf="canViewGSMPager"
                [fieldReadonly]="!canEditGSMPager"
                [name]="'ricMobile'"
                [customValidators]="pagerMobileValidator"
                [fieldMinLength]="16"
                [fieldMaxLength]="16"
                [labelKey]="'general_information.contact_information.ricMobile'"
                [placeholder]="'-'"
                [initialValue]="!canEditGSMPager? (isValid(initialData.ricMobile) ? initialData.ricMobile : '-') : initialData.ricMobile"
                                  class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>

      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
      </div>

    </div>

    <div class="row general-contact-mobile-padding">

      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-radio-field
                class="col-md-3 col-lg-3"
                [fieldReadonly]="readOnlyToogleMobile"
                [name]="'sendMobile'"
                [labelKey]="'general_information.contact_information.alert_mobile'"
                [possibleValues]="sendMobileValues"
                [placeholder]="'-'"
                [initialValue]="sendMobileInitialValue" style="display: contents;">
        </cgdis-portal-radio-field>
      </div>

      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-radio-field
                class="col-md-3 col-lg-3"
                [fieldReadonly]="readOnlyToogleMail"
                [name]="'sendEmail'"
                [labelKey]="'general_information.contact_information.alert_email'"
                [possibleValues]="sendEmailValues"
                [placeholder]="'-'"
                [initialValue]="sendEmailInitialValue" style="display: contents;">
        </cgdis-portal-radio-field>
      </div>

      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-radio-field
                class="col-md-3 col-lg-3"
                [fieldReadonly]="readOnlyToogleMail"
                [name]="'sendEmailNotification'"
                [labelKey]="'general_information.contact_information.alert_email_notification'"
                [possibleValues]="sendEmailNotificationValues"
                [placeholder]="'-'"
                [initialValue]="sendEmailNotificationInitialValue" style="display: contents;">
        </cgdis-portal-radio-field>
      </div>


    </div>
    <div class="row general-contact-mobile-padding" *cgdisPortalAuthRoles="['ROLE_ADMIN','ROLE_ADMIN_OPERATIONAL']">
      <div class="d-none d-xl-block col-xl-3 mb-sm-5 mb-lg-5">
      </div>
      <div class="d-none d-xl-block col-xl-3 mb-sm-5 mb-lg-5">
      </div>
      <div class="col-md-3 col-lg-3 mb-sm-5 mb-lg-5">
        <cgdis-portal-radio-field
          [name]="'sendErrorEmailNotification'"
          [labelKey]="'general_information.contact_information.alert_email_error_notification'"
          [possibleValues]="sendErrorEmailNotificationValues"
          (onValueChange)="showErrorEmailNotification=$event"
          [placeholder]="'-'"
          [initialValue]="sendErrorEmailNotificationInitialValue" style="display: contents;">
        </cgdis-portal-radio-field>

        <cgdis-portal-input-field
          [visible]="showErrorEmailNotification"
          [fieldReadonly]="false"
          [name]="'errorNotificationEmail'"
          [customValidators]="emailValidator"
          [fieldMaxLength]="150"
          [labelKey]="'general_information.contact_information.alert_email_error_notification_email'"
          [placeholder]="'-'"
          [initialValue]="initialData.errorNotificationEmail" class="col-md-6" style="display: contents;">
        </cgdis-portal-input-field>
      </div>
      <div class="col-md-3 col-lg-3 d-xl-none mb-sm-5 mb-lg-5">
      </div>
      <div class="col-md-3 col-lg-3 d-xl-none mb-sm-5 mb-lg-5">
      </div>

    </div>
  </cgdis-portal-form>
</ng-container>

