import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { DefaultFormService } from '../../common/modules/form-module/service/default-form.service';
import { ToastService } from '../../common/shared/toasts/CGDISToastService';
import { FormErrorService } from '../../common/modules/form-module/service/form-error.service';
import { FormError } from '../../common/modules/error-management/model/form-error.model';
import { GeneralContactInformation } from '../../model/general-contact-information.model';
import { UpdateGeneralContactInformationForm } from './update-general-contact-information-forms.model';

@Injectable()
export class UpdateGeneralContactService extends DefaultFormService<
  UpdateGeneralContactInformationForm,
  GeneralContactInformation
> {
  private _baseUrl: string[] = ['generalcontact'];

  constructor(
    private _restService: RestService,
    toastr: ToastService,
    formErrorService: FormErrorService,
  ) {
    super(
      toastr,
      'people_management.general_contact.update.success',
      formErrorService,
    );
  }

  submit(
    form: UpdateGeneralContactInformationForm,
  ): Observable<GeneralContactInformation> {
    return this._restService
      .all<GeneralContactInformation>(...this._baseUrl)
      .update(form)
      .pipe(
        map((value) => {
          return value;
        }),
      );
  }

  submitSuccess(result: GeneralContactInformation): void {}

  submitError(formError: FormError): void {}
}
