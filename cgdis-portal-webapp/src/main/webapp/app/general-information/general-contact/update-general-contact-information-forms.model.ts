import { BaseModel } from "../../model/base-model.model";

/**
 * Form for general contact information form
 */
export class UpdateGeneralContactInformationForm extends BaseModel {
  privatePhoneNumber: string;
  professionalPhoneNumber: string;
  privateMobileNumber: string;
  professionalMobileNumber: string;
  privateEmail: string;
  professionalEmail: string;
  errorNotificationEmail: string;
  sendErrorEmailNotification: boolean;

  constructor(args: UpdateGeneralContactInformationForm) {
    super(args);
    this.privatePhoneNumber = args.privatePhoneNumber;
    this.professionalPhoneNumber = args.professionalPhoneNumber;
    this.privateMobileNumber = args.privateMobileNumber;
    this.professionalMobileNumber = args.professionalMobileNumber;
    this.privateEmail = args.privateEmail;
    this.professionalEmail = args.professionalEmail;
    this.errorNotificationEmail = args.errorNotificationEmail;
    this.sendErrorEmailNotification = args.sendErrorEmailNotification;
  }
}
