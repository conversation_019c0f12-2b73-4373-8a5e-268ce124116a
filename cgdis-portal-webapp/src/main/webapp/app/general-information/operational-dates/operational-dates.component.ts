import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { OperationalDatesService } from '@app/general-information/operational-dates/operational-dates.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-operational-dates',
  templateUrl: './operational-dates.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OperationalDatesService],
})
export class OperationalDatesComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterLike = new FilterConfig({
    operator: SearchOperator.like,
    inUrl: false,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  numberOfFilters: number;

  constructor(
    public operationalDatesService: OperationalDatesService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.operationalDatesService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.operationalDatesService.getNumberOfFilters([
          'person',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.operationalDatesService.getNumberOfFilters([
      'person',
    ]);
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
