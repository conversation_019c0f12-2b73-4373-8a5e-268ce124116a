<div class="accordion__panel">

    <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
        <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
        <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
    </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.operational_dates.list.header.startDate'"></label>
            <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="operationalDatesService"></cgdis-portal-datatable-datepicker-filter>
        </div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.operational_dates.list.header.endDate'"></label>
            <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter" [filterName]="'endDate'" [filterConfig]="endDateFilterConfig" [datatableService]="operationalDatesService"></cgdis-portal-datatable-datepicker-filter>
        </div>
    </div>
  </ng-container>

    <cgdis-portal-cgdisdatatable
            [pageSize]="50"
            [datatableService]="operationalDatesService"
            [sorts]="[{dir:'asc',prop:'type'}]"
            [id]="'general-information-operational-dates-table-Id'"
            [showDetails]="'MOBILE'">

        <!-- Filter for person -->
        <cgdis-portal-datatable-number-filter [hidden]="true"
                                              [filterConfig]="filterConfigPersonId"
                                              [filterName]="'person'"
                                              [customFormControl]="formControl"
                                              [datatableService]="operationalDatesService"></cgdis-portal-datatable-number-filter>


        <ng-template #template let-row="row">
            <div class="error-detail-container">
                <cgdis-portal-operational-dates-detail [date]="row"></cgdis-portal-operational-dates-detail>
            </div>
        </ng-template>

        <!-- start date (date) -->
        <ep-datatable-column [columnName]="'startDate'" [flexGrow]="2">
            <ng-template epDatatableHeader>
                {{'general_information.operational_dates.list.header.startDate' | translate}}
            </ng-template>
            <ng-template epDatatableCell let-context>
                {{getFormattedDate(context.row.startDate)}}
            </ng-template>
            <ng-template epDatatableFilter>
                <cgdis-portal-datatable-datepicker-filter [filterName]="'startDate'" [filterConfig]="startDateFilterConfig" [datatableService]="operationalDatesService"></cgdis-portal-datatable-datepicker-filter>
            </ng-template>
        </ep-datatable-column>

        <!-- end date (date) -->
        <ep-datatable-column [columnName]="'endDate'" [flexGrow]="2">
            <ng-template epDatatableHeader>
                {{'general_information.operational_dates.list.header.endDate' | translate}}
            </ng-template>
            <ng-template epDatatableCell let-context>
                {{getFormattedDate(context.row.endDate)}}
            </ng-template>
            <ng-template epDatatableFilter>
                <cgdis-portal-datatable-datepicker-filter [filterName]="'endDate'" [filterConfig]="endDateFilterConfig" [datatableService]="operationalDatesService"></cgdis-portal-datatable-datepicker-filter>
            </ng-template>
        </ep-datatable-column>

        <ep-datatable-column [columnName]="'type'" [flexGrow]="2">
            <ng-template epDatatableHeader>
                {{'general_information.operational_dates.list.header.type.title' | translate}}
            </ng-template>
            <ng-template epDatatableCell let-context>
                {{('general_information.operational_dates.list.header.type.list.' + context.row.type | translate) | defaultValue:'-'}}
            </ng-template>
        </ep-datatable-column>


    </cgdis-portal-cgdisdatatable>
</div>
