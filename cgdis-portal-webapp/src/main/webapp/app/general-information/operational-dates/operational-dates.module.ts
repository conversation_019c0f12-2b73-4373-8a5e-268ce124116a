import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { OperationalDatesComponent } from '@app/general-information/operational-dates/operational-dates.component';
import { OperationalDatesDetailComponent } from '@app/general-information/operational-dates/detail/operational-dates-detail.component';

@NgModule({
  imports: [SharedModule, CommonModule, DatatableModule, EpDatatableModule],
  declarations: [OperationalDatesComponent, OperationalDatesDetailComponent],
  exports: [OperationalDatesComponent, OperationalDatesDetailComponent],
})
export class OperationalDatesModule {}
