import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { MedicalInformationService } from './medical-information.service';
import { MedicalRemark } from '../../model/person/person-medical-remark.model';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-medical-information',
  templateUrl: './medical-information.component.html',
  styleUrls: ['./_medical-information.scss'],
  providers: [MedicalInformationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MedicalInformationComponent implements OnInit {
  /**
   * The person id.
   */
  @Input() personId: number;

  public medicalRemark: string;

  constructor(
    private service: MedicalInformationService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.service
      .getMedicalRemark(this.personId)
      .pipe(take(1))
      .subscribe((result: MedicalRemark) => {
        if (result == undefined) {
          this.medicalRemark = '';
        } else {
          this.medicalRemark = result.remark;
        }
        this.cd.markForCheck();
      });
  }
}
