import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { Entity } from '../../model/entity.model';
import { MedicalRemark } from '../../model/person/person-medical-remark.model';

@Injectable()
export class MedicalInformationService {
  /**
   * Based url to access person medical remark
   * @type {string}
   */
  private baseUrl = ['person-medical-remark'];

  constructor(private restService: RestService) {}

  /**
   * Get the medical remark for the given person
   * @return {Observable<Entity>}
   */
  getMedicalRemark(personId: number): Observable<MedicalRemark> {
    const restResource = this.restService.one(
      ...this.baseUrl,
      'all',
      String(personId),
    );
    return restResource.get().pipe(
      map((value) => {
        return value;
      }),
    );
  }
}
