import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { RolesLogasInformationDetailComponent } from '@app/general-information/roles-logas-information/detail/roles-logas-information-detail.component';
import { RolesLogasInformationComponent } from '@app/general-information/roles-logas-information/roles-logas-information.component';

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    FormModule,
    DatatableModule,
    EpDatatableModule,
  ],
  declarations: [
    RolesLogasInformationComponent,
    RolesLogasInformationDetailComponent,
  ],
  exports: [
    RolesLogasInformationComponent,
    RolesLogasInformationDetailComponent,
  ],
})
export class RolesLogasInformationModule {}
