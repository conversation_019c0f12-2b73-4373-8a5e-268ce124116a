import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { SecurityRole } from '@app/model/security-role.model';

@Component({
  selector: 'cgdis-portal-roles-logas-information-detail',
  templateUrl: './roles-logas-information-detail.component.html',
  styleUrls: ['./_roles-logas-information-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RolesLogasInformationDetailComponent {
  @Input() role: SecurityRole;

  constructor() {}
}
