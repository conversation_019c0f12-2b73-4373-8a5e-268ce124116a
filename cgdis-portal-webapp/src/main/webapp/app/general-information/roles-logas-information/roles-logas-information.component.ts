import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { RolesLogasInformationService } from '@app/general-information/roles-logas-information/roles-logas-information.service';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-roles-logas-information',
  templateUrl: './roles-logas-information.component.html',
  providers: [RolesLogasInformationService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RolesLogasInformationComponent implements OnInit, OnDestroy {
  @Input() personId: number;

  excluedDomain = [
    '_AdmGroups',
    'CGDISPool',
    ' AdmGroups',
    'CGDIS POOL',
    'PortailCGDISTest',
    'PortailCGDISProd',
  ];

  filterConfigPersonId = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public rolesService: RolesLogasInformationService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.formControl.setValue(this.personId);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  isExclued(domain: any): boolean {
    return this.excluedDomain.indexOf(domain) > -1;
  }
}
