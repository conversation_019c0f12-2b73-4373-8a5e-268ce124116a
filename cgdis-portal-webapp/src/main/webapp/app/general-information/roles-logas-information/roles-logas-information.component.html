<div aria-hidden="false" role="region" aria-labelledby="accordion1" class="accordion__panel">
    <cgdis-portal-cgdisdatatable
            [id]="'user-rights-roles-list'"
            [datatableService]="rolesService"
            [showDetails]="'MOBILE'">

        <cgdis-portal-datatable-number-filter [hidden]="true"
                                              [filterConfig]="filterConfigPersonId"
                                              [filterName]="'tecid'"
                                              [customFormControl]="formControl"
                                              [datatableService]="rolesService"></cgdis-portal-datatable-number-filter>

        <ng-template #template let-row="row">
            <div class="error-detail-container">
                <cgdis-portal-roles-logas-information-detail [role]="row"></cgdis-portal-roles-logas-information-detail>
            </div>
        </ng-template>

        <!-- role name -->
        <ep-datatable-column [columnName]="'role'" [flexGrow]="45" [sortable]="false">
            <ng-template epDatatableHeader>
                <span [translate]="'user-rights.role.column'"></span>
            </ng-template>
            <ng-template let-context epDatatableCell>
                {{ ('user-rights.role.' + context.row.role+'.name' | translate) | defaultValue:'-' }}
            </ng-template>
        </ep-datatable-column>

        <!-- domains -->
        <ep-datatable-column [columnName]="'domains'" [flexGrow]="45" [sortable]="false">
            <ng-template epDatatableHeader>
                <span [translate]="'user-rights.role.entity'"></span>
            </ng-template>
            <ng-template let-context epDatatableCell>
                <p *ngFor="let domain of context.row.domains"><span *ngIf="!isExclued(domain)">{{domain | defaultValue:'-'}}</span></p>
            </ng-template>
        </ep-datatable-column>

    </cgdis-portal-cgdisdatatable>
</div>
