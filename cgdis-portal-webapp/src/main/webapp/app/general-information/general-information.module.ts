import { NgModule } from '@angular/core';
import { SharedModule } from '../common/shared/shared.module';
import { GeneralInformationComponent } from './general-information.component';
import { RouterModule } from '@angular/router';
import { GENERAL_INFORMATION_ROUTE } from './general-information.route';
import { FormModule } from '../common/modules/form-module/form.module';
import { DatepickerModule } from '../common/modules/datepicker/datepicker.module';
import { OperationalContactModule } from './operational-contact/operational-contact.module';
import { GeneralContactModule } from './general-contact/general-contact.module';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { PersonalInformationModule } from './personal-information/personal-information.module';
import { GeneralInformationService } from './general-information.service';
import { PostalAddressInformationModule } from './postal-address-information/postal-address-information.module';
import { SuspensionsInformationModule } from './suspensions-information/suspensions-information.module';
import { DriverLicenseInformationModule } from './driver-license-information/driver-license-information.module';
import { ActiveAssignmentsInformationModule } from './active-assignments-information/active-assignments-information.module';
import { OperationalFunctionsModule } from './operational-functions/operational-functions.module';
import { OperationalOccupationsModule } from './operational-occupations/operational-occupations.module';
import { GeneralInformationGeneralTabComponent } from './general-information-general-tab/general-information-general-tab.component';
import { GeneralInformationOperationalTabComponent } from './general-information-operational-tab/general-information-operational-tab.component';
import { RolesInformationModule } from './roles-information/roles-information.module';
import { AptitudesInformationModule } from './aptitudes-information/aptitudes-information.module';
import { RestrictionsInformationModule } from './restrictions-information/restrictions-information.module';
import { MedicalInformationModule } from './medical-information/medical-information.module';
import { GeneralInformationActivityTabComponent } from './general-information-activity-tab/general-information-activity-tab.component';
import { GeneralInformationMedicalTabComponent } from './general-information-medical-tab/general-information-medical-tab.component';
import { DevelopmentModule } from '../development/development.module';
import { InterventionsInformationModule } from './interventions-information/interventions-information.module';
import { PrestationInformationModule } from './prestation-information/prestation-information.module';
import { AvailabilitiesInformationModule } from './availabilities-information/availabilities-information.module';
import { GeneralInformationActivityDetailComponent } from './general-information-activity-tab/detail/general-information-activity-detail.component';
import { DatetimeModule } from '@eportal/core';
import { GeneralInformationMainComponent } from './general-information-main.component';
import { FormsModule } from '@angular/forms';
import { ProVolunteerChoiceComponent } from './general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { DaySelectorModule } from '../common/modules/day-selector/day-selector.module';
import { WeekSelectorModule } from '../common/modules/week-selector/week-selector.module';
import { MonthSelectorModule } from '../common/modules/month-selector/month-selector.module';
import { HighchartsModule } from '../common/modules/highcharts/highcharts.module';
import { LegendModule } from '../common/modules/legend/legend.module';
import { TotalsInformationModule } from './totals-information/totals-information.module';
import { YearSelectorModule } from '@app/common/modules/year-selector/year-selector.module';
import { SemesterSelectorModule } from '@app/common/modules/semester-selector/semester-selector.module';
import { OperationalGradesModule } from '@app/general-information/operational-grades/operational-grades.module';
import { ManagerialOccupationsModule } from '@app/general-information/managerial-occupations/managerial-occupations.module';
import { OperationalDatesModule } from '@app/general-information/operational-dates/operational-dates.module';
import { OperationalVolunteerInternshipModule } from '@app/general-information/operational-volunteer-internship/operational-volunteer-internship.module';
import { OperationalYoungFirefighterModule } from '@app/general-information/operational-young-firefighter/operational-young-firefighter.module';
import { BankAccountModule } from '@app/general-information/bank-account/bank-account.module';
import { MedicalReportInformationModule } from '@app/general-information/medical-report-information/medical-report-information.module';
import { RolesLogasInformationModule } from '@app/general-information/roles-logas-information/roles-logas-information.module';
import { DiplomasModule } from '@app/general-information/diplomas/diplomas.module';
import { CustomRangeModule } from '@app/common/modules/custom-range/custom-range.module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PersonFunctionOperationalModule } from '@app/general-information/person-function-operational/person-function-operational.module';
import { CarouselModule } from 'ngx-owl-carousel-o';

@NgModule({
  imports: [
    SharedModule,
    FormModule,
    DatepickerModule,
    OperationalContactModule,
    PersonalInformationModule,
    PostalAddressInformationModule,
    SuspensionsInformationModule,
    AptitudesInformationModule,
    RestrictionsInformationModule,
    MedicalInformationModule,
    MedicalReportInformationModule,
    DriverLicenseInformationModule,
    ActiveAssignmentsInformationModule,
    OperationalFunctionsModule,
    OperationalOccupationsModule,
    OperationalGradesModule,
    OperationalDatesModule,
    OperationalVolunteerInternshipModule,
    OperationalYoungFirefighterModule,
    PersonFunctionOperationalModule,
    BankAccountModule,
    ManagerialOccupationsModule,
    GeneralContactModule,
    RolesInformationModule,
    RolesLogasInformationModule,
    DiplomasModule,
    InterventionsInformationModule,
    PrestationInformationModule,
    AvailabilitiesInformationModule,
    TotalsInformationModule,
    MatExpansionModule,
    MatTabsModule,
    DevelopmentModule,
    RouterModule.forChild(GENERAL_INFORMATION_ROUTE),
    DatetimeModule,
    FormsModule,
    DaySelectorModule,
    WeekSelectorModule,
    MonthSelectorModule,
    SemesterSelectorModule,
    YearSelectorModule,
    HighchartsModule,
    LegendModule,
    CustomRangeModule,
    MatTooltipModule,
    CarouselModule,
  ],
  exports: [
    MatExpansionModule,
    MatTabsModule,
    GeneralInformationComponent,
    GeneralInformationGeneralTabComponent,
    GeneralInformationOperationalTabComponent,
    GeneralInformationActivityTabComponent,
    GeneralInformationActivityDetailComponent,
    GeneralInformationMedicalTabComponent,
  ],
  declarations: [
    GeneralInformationComponent,
    GeneralInformationGeneralTabComponent,
    GeneralInformationOperationalTabComponent,
    GeneralInformationActivityTabComponent,
    GeneralInformationActivityDetailComponent,
    GeneralInformationMedicalTabComponent,
    GeneralInformationMainComponent,
    ProVolunteerChoiceComponent,
  ],
  providers: [GeneralInformationService],
})
export class GeneralInformationModule {}
