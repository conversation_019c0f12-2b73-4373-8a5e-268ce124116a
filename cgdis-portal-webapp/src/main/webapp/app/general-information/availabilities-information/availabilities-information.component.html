<div class="accordion__panel top-none">
  <div >
    <cgdis-portal-availabilities-information-summary
      [personId]="personId"
      [startDate]="startDate"
      [endDate]="endDate"
      [proOrAndVolunteer]="proOrAndVolunteer">
    </cgdis-portal-availabilities-information-summary>
  </div>

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.availabilities.entities_availability'"></label>
        <cgdis-portal-datatable-text-filter class="activity-text-filter"
                                            (onValueChanged)="updateFilterNumber()"
                                            [filterName]="'entitiesAvailability'"
                                            [placeholder]="''"
                                            [datatableService]="availabilitiesService">
        </cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'service_plan.versions.type.barracked'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [filterName]="'acceptBarracked'"
                                              [possibleValues]="acceptBarrackedPossibleValues"
                                              [allowClear]="true"
                                              [datatableService]="availabilitiesService"></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.availabilities.start'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="activity-datepicker-filter" [readOnly]="true"  [customFormControl]="startDateFormControl" [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig" [datatableService]="availabilitiesService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.availabilities.intervention_types'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [possibleValues]="interventionTypesPossibleValues"
                                              [filterName]="'interventionTypes'"
                                              [flexGrow]="2"
                                              [datatableService]="availabilitiesService" [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.activity.availabilities.end'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="activity-datepicker-filter" [readOnly]="true" [customFormControl]="endDateFormControl" [filterName]="'endDateTime'" [filterConfig]="endDateFilterConfig" [datatableService]="availabilitiesService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>



  <cgdis-portal-cgdisdatatable
    [datatableService]="availabilitiesService"
    [sorts]="[{dir:'asc',prop:'startDateTime'}]"
    [id]="'general-information-availabilities-table-Id'"
    [showDetails]="'MOBILE'">

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'personTecid'"
                                          [customFormControl]="formControl"
                                          [datatableService]="availabilitiesService"></cgdis-portal-datatable-number-filter>

    <cgdis-portal-datatable-datepicker-filter *ngIf="isMobile" [hidden]="true" [readOnly]="true"  [customFormControl]="startDateFormControl" [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig" [datatableService]="availabilitiesService"></cgdis-portal-datatable-datepicker-filter>

    <cgdis-portal-datatable-datepicker-filter *ngIf="isMobile" [hidden]="true" [readOnly]="true" [customFormControl]="endDateFormControl" [filterName]="'endDateTime'" [filterConfig]="endDateFilterConfig" [datatableService]="availabilitiesService"></cgdis-portal-datatable-datepicker-filter>



    <cgdis-portal-datatable-text-with-null-filter [hidden]="true"  [customFormControl]="isProfessionalFormControl" [filterName]="'isProfessional'" [filterConfig]="filterConfigIsProfessional" [datatableService]="availabilitiesService"> </cgdis-portal-datatable-text-with-null-filter>
    <!-- The availability entities -->
    <ep-datatable-column [columnName]="'entitiesAvailability'" [sortable]="false" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.availabilities.entities_availability' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter
          [filterName]="'entitiesAvailability'"
          [placeholder]="''"
          [datatableService]="availabilitiesService">
        </cgdis-portal-datatable-text-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>

        <ul class="unstyled-list">
          <li *ngFor="let entity of castRow(context.row).entitiesAvailability">
            {{entity.entity.name | defaultValue:'-'}}
          </li>
        </ul>
      </ng-template>
    </ep-datatable-column>

    <!-- The barracked label -->
    <ep-datatable-column [columnName]="'acceptBarracked'" [flexGrow]="1" [sortable]="false">
      <ng-template epDatatableHeader>
        {{'service_plan.versions.type.barracked' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [filterName]="'acceptBarracked'"
                                              [possibleValues]="acceptBarrackedPossibleValues"
                                              [allowClear]="true"
                                              [datatableService]="availabilitiesService"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <ul class="unstyled-list">
          <li *ngFor="let entity of castRow(context.row).entitiesAvailability">
            {{entity.acceptBarracked ? '✔' : '✘' }}
          </li>
        </ul>
      </ng-template>
    </ep-datatable-column>

    <!-- The availability type-->
    <ep-datatable-column [columnName]="'interventionTypes'" [sortable]="false" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.availabilities.intervention_types' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [possibleValues]="interventionTypesPossibleValues"
                                              [filterName]="'interventionTypes'"
                                              [flexGrow]="2"
                                              [datatableService]="availabilitiesService" [allowClear]="true">
        </cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>

        <ul class="unstyled-list">
          <li *ngFor="let type of castRow(context.row).interventionTypes" class="block-intervention-types">
            <ng-container [ngSwitch]="type.label">
              <span *ngSwitchCase="'ambulance'" [translate]="'general_information.availability.type.ambulance'"></span>
              <span *ngSwitchCase="'fire'" [translate]="'general_information.availability.type.fire'"></span>
              <span *ngSwitchCase="'commandment'" [translate]="'general_information.availability.type.commandment'"></span>
              <span *ngSwitchCase="'dms'" [translate]="'general_information.availability.type.dms'"></span>
              <span *ngSwitchCase="'gis'" [translate]="'general_information.availability.type.gis'"></span>
              <span *ngSwitchCase="'samu'" [translate]="'general_information.availability.type.samu'"></span>
              <span *ngSwitchCase="'others'" [translate]="'general_information.availability.type.others'"></span>
            </ng-container>
          </li>
        </ul>
      </ng-template>
    </ep-datatable-column>



    <!-- The availability start date time -->
    <ep-datatable-column [columnName]="'startDateTime'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.availabilities.start'.concat(isMobile ? '_mobile' : '') | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [readOnly]="true"  [customFormControl]="startDateFormControl" [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig" [datatableService]="availabilitiesService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{castRow(context.row).startDateTime | dateFormat}}
      </ng-template>
    </ep-datatable-column>

    <!-- The availability end date time -->
    <ep-datatable-column [columnName]="'endDateTime'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.availabilities.end' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [readOnly]="true" [customFormControl]="endDateFormControl" [filterName]="'endDateTime'" [filterConfig]="endDateFilterConfig" [datatableService]="availabilitiesService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{castRow(context.row).endDateTime | dateFormat}}
      </ng-template>
    </ep-datatable-column>

    <!-- The service plan slot time label -->
    <ep-datatable-column [columnName]="'slotTimeLabel'" [flexGrow]="2" [sortable]="false">
      <ng-template epDatatableHeader>
        {{'admin.service_plan.version.form.totalSlots' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ {startTime:castRow(context.row).startDateTime, endTime: castRow(context.row).endDateTime} | slotFormat}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'durationLabel'" [flexGrow]="1" [sortable]="false">
      <ng-template epDatatableHeader>
        {{'general_information.activity.availabilities.duration' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
         {{ castRow(context.row).duration | duration}}
      </ng-template>
    </ep-datatable-column>

    <ng-template #template let-row="row">
      <div class="error-detail-container">
        <cgdis-portal-availabilities-information-detail [availability]="row"></cgdis-portal-availabilities-information-detail>
      </div>
    </ng-template>

  </cgdis-portal-cgdisdatatable>
</div>
