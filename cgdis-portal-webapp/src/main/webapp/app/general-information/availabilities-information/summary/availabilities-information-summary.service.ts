import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DateModel, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PrestationStatistics } from '../../../model/prestation-statistics.model';

/**
 * Access intervention types
 */
@Injectable()
export class AvailabilitiesInformationSummaryService {
  /**
   * Based url to access prestation services
   * @type {string}
   */
  private baseUrlPrestation = ['availabilitiessummary'];

  constructor(private restService: RestService) {}

  /**
   * Get availability duration
   * @param personId: the person id
   * @param startDate: the start date
   * @param endDate: the end date
   */
  getStatistics = (
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
    isPro: Boolean,
  ): Observable<PrestationStatistics> => {
    const restResource = this.restService.one(
      ...this.baseUrlPrestation,
      String(personId),
    );
    let params: any = { from: startDate, to: endDate };
    if (isPro != undefined) {
      params.isPro = isPro;
    }
    return restResource.get(params).pipe(
      map((value: PrestationStatistics) => {
        return value;
      }),
    );
  };
}
