
<cgdis-portal-two-columns-row [leftColumnHideable]="false" class="summary-mobile-columns">

  <ng-container left-column >
    <div class="row align-items-center" [ngClass]="{'summary-mobile': isMobile}">

      <div class="col-md-12" *ngIf="!isMobile">
        <div class="chart" *ngIf="!reloadChart">
          <div id="performance-chart_" class="pie-chart-activity">
            <cgdis-portal-pie-chart
              [data]="pieChartData"
              [chartDisplayParam]="chartDisplayParams"
              [styleClass]="['inherit-parent-height']"
              [middleText]="chartTitle"
              [innerSize]="'90%'"
              [textPosition]="5"
              [unitText]="'dashboard.members.chart.available-hours'"
              [unitFormat]="'%'"></cgdis-portal-pie-chart>
          </div>
        </div>
      </div>

    </div>

  </ng-container>

  <ng-container right-column >
    <div class="summary-mobile-performances row align-items-center" [ngClass]="{'summary-mobile': isMobile, 'pt-4': true}">

      <div *ngIf="fireValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.fire-mobile' : 'general_information.availability.type.fire'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.fireValue)}}</span>
                </li>

              </ul>
            </div>
          </div>

        </div>
      </div>

      <div *ngIf="ambulanceValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.ambulance-mobile' : 'general_information.availability.type.ambulance'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.ambulanceValue)}}</span>
                </li>

              </ul>
            </div>
          </div>

        </div>
      </div>

      <div *ngIf="commandmentValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.commandment-mobile' : 'general_information.availability.type.commandment'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.commandmentValue)}}</span>
                </li>

              </ul>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="samuValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.samu-mobile' : 'general_information.availability.type.samu'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.samuValue)}}</span>
                </li>

              </ul>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="gisValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.gis-mobile' : 'general_information.availability.type.gis'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.gisValue)}}</span>
                </li>

              </ul>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="dmsValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.dms-mobile' : 'general_information.availability.type.dms'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.dmsValue)}}</span>
                </li>

              </ul>
            </div>
          </div>
        </div>
      </div>


      <div *ngIf="otherValue>0" [ngClass]="{'col-6' : isMobile, 'col-2' : !isMobile}">
        <div class="performance performance__left">

          <div class="performance__figure">
            <span class="performance__figure__title" [translate]="isMobile ? 'dashboard.members.chart.others-mobile' : 'general_information.availability.type.others'"></span>

            <div class="performance__figure_content" >
              <ul>
                <li class="statistics-value margin-top">
                  <span>{{convertSecondsToDuration(this.otherValue)}}</span>
                </li>

              </ul>
            </div>
          </div>

        </div>
      </div>
    </div>
  </ng-container>
</cgdis-portal-two-columns-row>
