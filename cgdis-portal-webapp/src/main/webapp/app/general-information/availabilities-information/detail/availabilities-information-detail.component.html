<div class="row ">

  <div *ngIf="!availability; else PrestationInforamtionDetail">

  </div>
  <ng-template #PrestationInforamtionDetail>
    <div class="col-sm-12 ">
      <table>

        <tr>
          <td class="list-label">
            <span>{{'general_information.activity.availabilities.entities_availability_with_barracked' | translate}}</span>
          </td>
          <td class="list-value">
            <ul class="unstyled-list">
              <li *ngFor="let entity of availability.entitiesAvailability">
                {{entity.entity.name}} / <span style="font-size: 2rem;"
                                               [innerHTML]="entity.acceptBarracked ? '&#x2713;' : '&#x2717;'"></span>
              </li>
            </ul>
          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span>{{'general_information.activity.availabilities.intervention_types' | translate}}</span>
          </td>
          <td class="list-value">
            <ul class="unstyled-list">
              <li *ngFor="let type of availability.interventionTypes" class="block-intervention-types">
                <ng-container [ngSwitch]="type.label">
                  <span *ngSwitchCase="'ambulance'" [translate]="'general_information.availability.type.ambulance'"></span>
                  <span *ngSwitchCase="'fire'" [translate]="'general_information.availability.type.fire'"></span>
                  <span *ngSwitchCase="'commandment'" [translate]="'general_information.availability.type.commandment'"></span>
                  <span *ngSwitchCase="'samu'" [translate]="'general_information.availability.type.samu'"></span>
                  <span *ngSwitchCase="'gis'" [translate]="'general_information.availability.type.gis'"></span>
                  <span *ngSwitchCase="'dms'" [translate]="'general_information.availability.type.dms'"></span>
                  <span *ngSwitchCase="'others'" [translate]="'general_information.availability.type.others'"></span>
                </ng-container>
              </li>
            </ul>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{'general_information.activity.availabilities.start' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{getFormattedDate(availability.startDateTime)}}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{'general_information.activity.availabilities.end' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{getFormattedDate(availability.endDateTime)}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'admin.service_plan.version.form.totalSlots' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{getFormattedTime(availability.startDateTime) + ' - ' + getFormattedTime(availability.endDateTime)}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.activity.availabilities.duration' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{getFormattedSlotTime(availability.endDateTime, availability.startDateTime)}}</span>
          </td>
        </tr>


      </table>
    </div>
  </ng-template>
</div>
