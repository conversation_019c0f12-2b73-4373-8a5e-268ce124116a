import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { Availability } from '@app/model/planning/volunteer-availability.model';

@Component({
  selector: 'cgdis-portal-availabilities-information-detail',
  templateUrl: './availabilities-information-detail.component.html',
  styleUrls: ['./_availabilities-information-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvailabilitiesInformationDetailComponent implements OnInit {
  @Input() availability: Availability;

  constructor(private dateTimeService: DatetimeService) {}

  ngOnInit(): void {}

  public getFormattedDuration(duration: number): string {
    if (duration != null) {
      let dayDuration = Math.trunc(duration / 1440);
      let hoursDuration = Math.trunc((duration - dayDuration * 1440) / 60);
      let minutesDuration = duration - dayDuration * 1440 - hoursDuration * 60;
      let time = new DatetimeModel({
        minute: minutesDuration,
        hours: hoursDuration,
        second: 0,
        millisecond: 0,
        day: dayDuration,
        month: 1,
        year: 2000,
      });
      if (time.day !== 0) {
        return (
          this.dateTimeService.format(time, 'DD') +
          'j ' +
          this.dateTimeService.format(time, 'HH') +
          'h' +
          this.dateTimeService.format(time, 'mm')
        );
      } else {
        let time = new DatetimeModel({
          minute: minutesDuration,
          hours: hoursDuration,
          second: 0,
          millisecond: 0,
          day: 1,
          month: 1,
          year: 2000,
        });
        return (
          this.dateTimeService.format(time, 'HH') +
          'h' +
          this.dateTimeService.format(time, 'mm')
        );
      }
    } else {
      return '-';
    }
  }

  public getFormattedTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'HH');
    } else {
      return '-';
    }
  }

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY');
    } else {
      return '-';
    }
  }

  public getFormattedSlotTime(
    endDate: DatetimeModel,
    startDate: DatetimeModel,
  ): string {
    if (endDate != null && startDate != null) {
      let diff = this.dateTimeService
        .toMoment(endDate)
        .diff(this.dateTimeService.toMoment(startDate), 'minutes');
      return this.getFormattedDuration(diff);
    } else {
      return '-';
    }
  }
}
