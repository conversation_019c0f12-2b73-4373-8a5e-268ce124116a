import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { AvailabilitiesInformationService } from './availabilities-information.service';
import { DateModel, DatetimeService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { GeneralInformationService } from '../general-information.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { TranslateService } from '@ngx-translate/core';
import _ from 'lodash';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';
import { PRO_VOLUNTEER_BOTH } from '@app/general-information/general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { Availability } from '@app/model/planning/volunteer-availability.model';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-availabilities-information',
  templateUrl: './availabilities-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AvailabilitiesInformationService, GeneralInformationService],
})
export class AvailabilitiesInformationComponent
  implements OnInit, OnChanges, OnDestroy
{
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];

  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterConfigIsProfessional = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  startDateFormControl = new UntypedFormControl();
  endDateFormControl = new UntypedFormControl();
  isProfessionalFormControl = new UntypedFormControl();
  interventionTypesPossibleValues: FieldGroupOption<string, any>[] = [];
  acceptBarrackedPossibleValues: FieldGroupOption<string, any>[] = [];
  showFilter = false;

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public availabilitiesService: AvailabilitiesInformationService,
    private generalInformationService: GeneralInformationService,
    private translateService: TranslateService,
    private interventionTypeService: InterventionTypesService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);
    this.startDateFormControl.setValue(this.startDate);
    this.endDateFormControl.setValue(this.endDate);
    this.loadProVolunteerChoice();
    this.acceptBarrackedPossibleValues =
      this.generalInformationService.loadBarrackedPossibleValues(
        this.translateService,
      );
    this.interventionTypeService.getAll().subscribe((types) => {
      this.interventionTypesPossibleValues = _.map(
        types,
        (oneType) =>
          new FieldGroupOption({
            value: oneType.label,
            I18NLabel: 'intervention.types.' + oneType.label,
          }),
      );
    });

    this.availabilitiesService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.availabilitiesService.getNumberOfFilters([
          'personTecid',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.availabilitiesService.getNumberOfFilters([
      'personTecid',
    ]);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.startDate || changes.endDate) {
      if (
        changes.startDate.previousValue === undefined &&
        changes.endDate.previousValue === undefined
      ) {
        this.startDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.startDate,
          );
        this.endDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.endDate,
          );
      } else if (
        (changes.startDate &&
          changes.startDate.previousValue !== changes.startDate.currentValue) ||
        (changes.endDate &&
          changes.endDate.previousValue !== changes.endDate.currentValue)
      ) {
        this.startDateFormControl.setValue(this.startDate);
        this.endDateFormControl.setValue(this.endDate);
      }
    }

    if (
      changes.proOrAndVolunteer &&
      changes.proOrAndVolunteer.previousValue !==
        changes.proOrAndVolunteer.currentValue
    ) {
      this.loadProVolunteerChoice();
    }
  }

  castRow(row: any): Availability {
    return row as Availability;
  }

  private loadProVolunteerChoice(): void {
    let result: Map<string, boolean> =
      this.generalInformationService.buildProVolunteerChoice(
        this.proOrAndVolunteer,
      );
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessionalFormControl.setValue(true);
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessionalFormControl.setValue(false);
    } else if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessionalFormControl.setValue(null);
    }
  }
}
