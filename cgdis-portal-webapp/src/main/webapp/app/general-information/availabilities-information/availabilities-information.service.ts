import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { ActivatedRoute, Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { RestService } from '@eportal/core';
import { Location } from '@angular/common';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { Availability } from '../../model/planning/volunteer-availability.model';

@Injectable()
export class AvailabilitiesInformationService extends CgdisDatatableService<Availability> {
  constructor(
    restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(
      restService.all('volunteerAvailabilities', 'all-information'),
    );
  }
}
