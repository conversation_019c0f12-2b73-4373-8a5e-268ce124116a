import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimpleTableModule } from '../../common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '../../common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '../../common/modules/tile-group/tile-group.module';
import { InputModule } from '../../common/modules/input/input.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { AvailabilitiesInformationComponent } from './availabilities-information.component';
import { AvailabilitiesInformationSummaryComponent } from '@app/general-information/availabilities-information/summary/availabilities-information-summary.component';
import { HighchartsModule } from '@app/common/modules/highcharts/highcharts.module';
import { LegendModule } from '@app/common/modules/legend/legend.module';
import { AvailabilitiesInformationDetailComponent } from '@app/general-information/availabilities-information/detail/availabilities-information-detail.component';
import { TwoColumnsRowModule } from '@app/common/template/two-columns-row/two-columns-row.module';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
    HighchartsModule,
    LegendModule,
    TwoColumnsRowModule,
  ],
  declarations: [
    AvailabilitiesInformationComponent,
    AvailabilitiesInformationDetailComponent,
    AvailabilitiesInformationSummaryComponent,
  ],
  exports: [
    AvailabilitiesInformationComponent,
    AvailabilitiesInformationDetailComponent,
    AvailabilitiesInformationSummaryComponent,
  ],
})
export class AvailabilitiesInformationModule {}
