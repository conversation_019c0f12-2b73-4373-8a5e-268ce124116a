import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { PrestationInformationService } from './prestation-information.service';
import { DateModel, DatetimeService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { GeneralInformationService } from '../general-information.service';
import { PRO_VOLUNTEER_BOTH } from '../general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { TranslateService } from '@ngx-translate/core';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { PrestationInformation } from '@app/model/prestation/prestation-information.model';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-prestation-information',
  templateUrl: './prestation-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PrestationInformationService],
})
export class PrestationInformationComponent
  implements OnInit, OnChanges, OnDestroy
{
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];

  @Output() positionLabelUpdated = new EventEmitter<string>();
  @Output() entityLabelUpdated = new EventEmitter<string>();
  @Output() portalLabelUpdated = new EventEmitter<string>();
  @Output() barrackedLabelUpdated = new EventEmitter<boolean>();

  startDateFilterConfig = new FilterConfig({});
  endDateFilterConfig = new FilterConfig({});
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });
  filterConfigIsProfessional = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterConfigAllowance = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  startDateFormControl = new UntypedFormControl();
  endDateFormControl = new UntypedFormControl();

  isProfessionalFormControl = new UntypedFormControl();
  enableAllowance: boolean;
  barrackedPossibleValues: FieldGroupOption<string, any>[] = [];
  showFilter = false;
  dateFormat = 'DD/MM/YYYY';

  positionLabel: string;
  entityLabel: string;
  portalLabel: string;
  barracked: boolean;

  numberOfFilters: number;

  dayDuration = 1440;
  hoursDuration = 60;

  isMobile: boolean = false;
  isMobileIcon: boolean = false;

  subscriptions: Subscription[] = [];
  startSearch = false;

  constructor(
    public prestationService: PrestationInformationService,
    private generalInformationService: GeneralInformationService,
    private translateService: TranslateService,
    private dateTimeService: DatetimeService,
    private cd: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 768px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
      this.breakpointObserver
        .observe(['(max-width: 1200px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobileIcon = result.matches;
        }),
    );
  }

  ngOnInit(): void {
    this.formControl.setValue(this.personId);
    this.startDateFormControl.setValue(this.startDate);
    this.endDateFormControl.setValue(this.endDate);
    // this.loadProVolunteerChoice();
    this.barrackedPossibleValues =
      this.generalInformationService.loadBarrackedPossibleValues(
        this.translateService,
      );

    this.prestationService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.prestationService.getNumberOfFilters([
          'personId',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.prestationService.getNumberOfFilters([
      'personId',
    ]);
  }
  ngOnChanges(changes: SimpleChanges): void {
    let filterUpdated = false;
    let dateFilterUpdated = false;
    if (changes.startDate || changes.endDate) {
      if (
        changes.startDate.previousValue === undefined &&
        changes.endDate.previousValue === undefined
      ) {
        this.startDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.startDate,
          );
        this.endDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.endDate,
          );
      } else if (
        (changes.startDate &&
          changes.startDate.previousValue !== changes.startDate.currentValue) ||
        (changes.endDate &&
          changes.endDate.previousValue !== changes.endDate.currentValue)
      ) {
        this.startDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.startDate,
          );
        this.endDateFormControl.setValue(this.endDate);
        this.startDateFormControl.setValue(this.startDate);
        dateFilterUpdated = true;
        filterUpdated = true;
      }
    }

    if (
      changes.proOrAndVolunteer &&
      changes.proOrAndVolunteer.previousValue !==
        changes.proOrAndVolunteer.currentValue
    ) {
      this.loadProVolunteerChoice();
      filterUpdated =
        filterUpdated || this.proOrAndVolunteerHasChanged(changes);
    }

    if (filterUpdated) {
      this.startSearch = true;
      if (dateFilterUpdated) {
        // updateing dates has already sent an event to the service
      } else {
        this.prestationService.search();
      }
    }
  }

  private proOrAndVolunteerHasChanged(changes: SimpleChanges) {
    let previousKey: any = undefined;
    let previousValue: any = undefined;
    if (
      changes.proOrAndVolunteer.previousValue != undefined &&
      changes.proOrAndVolunteer.previousValue.length > 0
    ) {
      previousKey = changes.proOrAndVolunteer.previousValue[0];
      previousValue = changes.proOrAndVolunteer.previousValue[1];
    }

    let afterKey: any = undefined;
    let afterValue: any = undefined;
    if (
      changes.proOrAndVolunteer.currentValue != undefined &&
      changes.proOrAndVolunteer.currentValue.length > 0
    ) {
      afterKey = changes.proOrAndVolunteer.currentValue[0];
      afterValue = changes.proOrAndVolunteer.currentValue[1];
    }

    return previousKey !== afterKey || previousValue !== afterValue;
  }

  private loadProVolunteerChoice(): void {
    let newFilterValue;
    let result: Map<string, boolean> =
      this.generalInformationService.buildProVolunteerChoice(
        this.proOrAndVolunteer,
      );
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      newFilterValue = true;
      this.isProfessionalFormControl.setValue(newFilterValue, {
        emitEvent: false,
      });
      this.enableAllowance = false;
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      newFilterValue = false;
      this.isProfessionalFormControl.setValue(newFilterValue, {
        emitEvent: false,
      });
      this.enableAllowance = true;
    } else if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      newFilterValue = null;
      this.isProfessionalFormControl.setValue(newFilterValue, {
        emitEvent: false,
      });
      this.enableAllowance = true;
    }
  }

  cast(row: any): PrestationInformation {
    return row as PrestationInformation;
  }

  changePortalLabel(event: string) {
    this.portalLabel = event;
    this.portalLabelUpdated.emit(event);
    this.cd.markForCheck();
  }

  changePositionLabel(event: string) {
    this.positionLabel = event;
    this.positionLabelUpdated.emit(event);
    this.cd.markForCheck();
  }

  changeEntityLabel(event: string) {
    this.entityLabel = event;
    this.entityLabelUpdated.emit(event);
    this.cd.markForCheck();
  }

  changeBarracked(event: boolean) {
    this.barracked = event;
    this.barrackedLabelUpdated.emit(event);
    this.cd.markForCheck();
  }
}
