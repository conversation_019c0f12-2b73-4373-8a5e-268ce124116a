<div class="row ">

    <div  *ngIf="!prestation; else PrestationInforamtionDetail">

    </div>
    <ng-template #PrestationInforamtionDetail>
        <div class="col-sm-12 ">
            <table>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.activity.prestations.start' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.startDateTime|dateFormat}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'admin.service_plan.version.form.totalSlots' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.timeSlot | slotFormat}} ({{prestation.timeSlot.startTime | slotDuration: prestation.timeSlot.endTime }})</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.activity.prestations.portal_label' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.portalLabel}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.activity.prestations.position_label' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.positionLabel}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.activity.availabilities.entities_availability' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.entity}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'service_plan.versions.type.barracked' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.isBarracked ? '✔' : '✘'}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.activity.prestations.duration' | translate}}</span>
                    </td>
                    <td class="list-value">
                      {{prestation.professional ? "N/A" : prestation.duration | duration}}
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.activity.allowance' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{prestation.professional ? "N/A" : prestation | allowance}}</span>
                    </td>
                </tr>

              <tr>
                <td class="list-label">
                  <span>{{'general_information.activity.prestations.realDuration' | translate}}</span>
                </td>
                <td class="list-value">
                  <span>{{ prestation.startDateTime | prestationDuration: prestation.endDateTime }}</span>
                </td>
              </tr>

            </table>
        </div>
    </ng-template>
</div>
