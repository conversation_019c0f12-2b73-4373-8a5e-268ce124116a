import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DateModel, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PrestationStatistics } from '../../../model/prestation-statistics.model';

/**
 * Access intervention types
 */
@Injectable()
export class PrestationInformationSummaryService {
  /**
   * Based url to access prestation services
   * @type {string}
   */
  private baseUrlPrestation = ['prestationssummaries'];

  constructor(private restService: RestService) {}

  /**
   * Get availability duration
   * @param personId: the person id
   * @param startDate: the start date
   * @param endDate: the end date
   */
  getStatistics = (
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
    isPro: Boolean,
    portalLabel: string,
    positionLabel: string,
    entityLabel: string,
    barracked: boolean,
  ): Observable<PrestationStatistics> => {
    const restResource = this.restService.one(
      ...this.baseUrlPrestation,
      String(personId),
    );
    let params: any = { from: startDate, to: endDate };
    if (isPro != undefined) {
      params.isPro = isPro;
    }
    if (portalLabel != undefined) {
      params.portalLabel = portalLabel;
    }
    if (positionLabel != undefined) {
      params.position = positionLabel;
    }
    if (entityLabel != undefined) {
      params.entityLabel = entityLabel;
    }
    if (barracked != undefined) {
      params.barracked = barracked;
    }
    return restResource.get(params).pipe(
      map((value: PrestationStatistics) => {
        return value;
      }),
    );
  };
}
