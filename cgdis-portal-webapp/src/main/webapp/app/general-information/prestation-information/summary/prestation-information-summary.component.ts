import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewEncapsulation,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { Subscription } from 'rxjs';
import { PrestationStatistics } from '../../../model/prestation-statistics.model';
import { PieChartData } from '../../../common/modules/highcharts/pie/pie-chart.model';
import { LegendItem } from '../../../common/modules/legend/legend-item';
import { TranslateService } from '@ngx-translate/core';
import { PrestationInformationSummaryService } from '@app/general-information/prestation-information/summary/prestation-information-summary.service';
import { PRO_VOLUNTEER_BOTH } from '@app/general-information/general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { GeneralInformationService } from '@app/general-information/general-information.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { take } from 'rxjs/operators';
import * as _ from 'lodash';

@Component({
  selector: 'cgdis-portal-prestation-information-summary',
  templateUrl: './prestation-information-summary.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PrestationInformationSummaryService, GeneralInformationService],
})
export class PrestationInformationSummaryComponent
  implements OnInit, OnDestroy, OnChanges
{
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];
  @Input() portalLabel: string;
  @Input() entityLabel: string;
  @Input() positionLabel: string;
  @Input() barracked: boolean;

  reloadChart = false;
  public chartTitle: string;
  public legendItems: LegendItem[];
  public subscriptions: Subscription[] = [];
  public pieChartData: PieChartData[] = [];
  public chartDisplayParams: any;

  fireValue = 0;
  ambulanceValue = 0;
  commandmentValue = 0;
  samuValue = 0;
  gisValue = 0;
  dmsValue = 0;
  otherValue = 0;
  isMobile: boolean = false;

  constructor(
    private service: PrestationInformationSummaryService,
    private generalInformationService: GeneralInformationService,
    private cd: ChangeDetectorRef,
    private dateService: DateService,
    private translateService: TranslateService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.chartDisplayParams = {
      margin: [10, 10, 10, 10],
      spacingTop: 0,
      spacingBottom: 0,
      spacingLeft: 0,
      spacingRight: 0,
    };
    let prefix = '';

    // Init chart legend
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'dashboard.members.chart.' + prefix + 'fire',
        classes: ['-incomplete'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'dashboard.members.chart.' + prefix + 'ambulance',
        classes: ['-degraded'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'dashboard.members.chart.' + prefix + 'others',
        classes: ['-complete'],
      }),
    ];
    this.subscriptions.push(
      this.translateService.onLangChange.subscribe(() => {
        this.chartTitle = this.capitalizeFirstLetter(
          this.translateService.instant(
            'date.months.' +
              this.dateService.firstDayOfMonth(this.startDate).month +
              '.abr',
          ),
        );
      }),
    );
  }

  /**
   * Capitalize the first letter of a string
   * @param {string} string
   * @returns {string} String
   */
  private capitalizeFirstLetter(string: string): string {
    if (string != undefined) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    } else {
      return string;
    }
  }

  loadStatistics(): void {
    this.pieChartData = [];
    this.reloadChart = true;

    let result = this.generalInformationService.buildProVolunteerChoice(
      this.proOrAndVolunteer,
    );

    let isPro;
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      isPro = true;
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      isPro = false;
    }

    // Init chart title
    this.chartTitle = this.capitalizeFirstLetter(
      this.translateService.instant(
        'date.months.' +
          this.dateService.firstDayOfMonth(this.startDate).month +
          '.abr',
      ),
    );

    this.service
      .getStatistics(
        this.personId,
        this.startDate,
        this.endDate,
        isPro,
        this.portalLabel,
        this.positionLabel,
        this.entityLabel,
        this.barracked,
      )
      .pipe(take(1))
      .subscribe((value: PrestationStatistics) => {
        // Init chart data
        this.fireValue = value.repartition.fire ? value.repartition.fire : 0;
        this.ambulanceValue = value.repartition.ambulance
          ? value.repartition.ambulance
          : 0;
        this.commandmentValue = value.repartition.commandment
          ? value.repartition.commandment
          : 0;
        this.gisValue = value.repartition.gis ? value.repartition.gis : 0;
        this.dmsValue = value.repartition.dms ? value.repartition.dms : 0;
        this.samuValue = value.repartition.samu ? value.repartition.samu : 0;
        this.otherValue = value.repartition.others
          ? value.repartition.others
          : 0;

        let total =
          this.fireValue +
          this.ambulanceValue +
          this.commandmentValue +
          this.gisValue +
          this.samuValue +
          this.dmsValue +
          this.otherValue;

        let firePercentage =
          this.fireValue > 0 ? _.round((100 * this.fireValue) / total, 0) : 0;
        let ambulancePercentage =
          this.ambulanceValue > 0
            ? _.round((100 * this.ambulanceValue) / total, 0)
            : 0;
        let commandmentPercentage =
          this.commandmentValue > 0
            ? _.round((100 * this.commandmentValue) / total, 0)
            : 0;
        let samuPercentage =
          this.samuValue > 0 ? _.round((100 * this.samuValue) / total, 0) : 0;
        let gisPercentage =
          this.gisValue > 0 ? _.round((100 * this.gisValue) / total, 0) : 0;
        let dmsPercentage =
          this.dmsValue > 0 ? _.round((100 * this.dmsValue) / total, 0) : 0;
        let otherPercentage =
          this.otherValue > 0 ? _.round((100 * this.otherValue) / total, 0) : 0;

        this.pieChartData.push(new PieChartData({ type: '', value: 0 }));
        this.pieChartData.push(
          new PieChartData({ type: 'fire', value: firePercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'ambulance', value: ambulancePercentage }),
        );
        this.pieChartData.push(
          new PieChartData({
            type: 'commandment',
            value: commandmentPercentage,
          }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'samu', value: samuPercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'gis', value: gisPercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'dms', value: dmsPercentage }),
        );
        this.pieChartData.push(
          new PieChartData({ type: 'others', value: otherPercentage }),
        );
        // adding of a fourth value because pie chart have 4 colors before the 'no-data' one
        this.pieChartData.push(new PieChartData({ type: '', value: 0 }));

        this.reloadChart = false;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   * Convert seconds to duration
   * @param seconds
   */
  public convertSecondsToDuration(seconds: number): string {
    return this.getHours(seconds) + 'h' + this.getMinutes(seconds);
  }

  /**
   * Extract number of minutes in given number of seconds
   * @param seconds
   */
  private getMinutes(seconds: number): string {
    if (seconds <= 0 || seconds === undefined) {
      return '00';
    }

    seconds %= 3600;
    let minutes = Math.floor(seconds / 60);

    if (minutes < 10) {
      return '0' + minutes;
    }
    return '' + minutes;
  }

  /**
   * Get number of hours in given number of seconds
   * @param seconds
   */
  private getHours(seconds: number): number {
    return seconds > 0 ? Math.floor(seconds / 3600) : 0;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes.startDate &&
        changes.startDate.previousValue !== changes.startDate.currentValue) ||
      (changes.endDate &&
        changes.endDate.previousValue !== changes.endDate.currentValue) ||
      this.proOrAndVolunteerHasChanged(changes) ||
      (changes.positionLabel &&
        changes.positionLabel.previousValue !==
          changes.positionLabel.currentValue) ||
      (changes.portalLabel &&
        changes.portalLabel.previousValue !==
          changes.portalLabel.currentValue) ||
      (changes.entityLabel &&
        changes.entityLabel.previousValue !==
          changes.entityLabel.currentValue) ||
      (changes.barracked &&
        changes.barracked.previousValue !== changes.barracked.currentValue)
    ) {
      this.loadStatistics();
    }
  }

  private proOrAndVolunteerHasChanged(changes: SimpleChanges) {
    let previousKey: any = undefined;
    let previousValue: any = undefined;
    if (
      changes.proOrAndVolunteer.previousValue != undefined &&
      changes.proOrAndVolunteer.previousValue.length > 0
    ) {
      previousKey = changes.proOrAndVolunteer.previousValue[0];
      previousValue = changes.proOrAndVolunteer.previousValue[1];
    }

    let afterKey: any = undefined;
    let afterValue: any = undefined;
    if (
      changes.proOrAndVolunteer.currentValue != undefined &&
      changes.proOrAndVolunteer.currentValue.length > 0
    ) {
      afterKey = changes.proOrAndVolunteer.currentValue[0];
      afterValue = changes.proOrAndVolunteer.currentValue[1];
    }

    return previousKey !== afterKey || previousValue !== afterValue;
  }
  getStatOffset(): string {
    let count = 0;
    let offset = '';
    if (this.fireValue > 0) {
      count++;
    }
    if (this.ambulanceValue > 0) {
      count++;
    }
    if (this.commandmentValue > 0) {
      count++;
    }
    if (this.samuValue > 0) {
      count++;
    }
    if (this.gisValue > 0) {
      count++;
      if (count === 4) {
        offset = 'gis';
      }
    }
    if (this.dmsValue > 0) {
      count++;
      if (count === 4) {
        offset = 'dms';
      }
    }
    if (this.otherValue > 0) {
      count++;
      if (count === 4) {
        offset = 'others';
      }
    }
    if (count > 4) {
      return offset;
    } else {
      return '';
    }
  }
}
