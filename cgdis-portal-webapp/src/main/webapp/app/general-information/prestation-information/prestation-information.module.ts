import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PrestationInformationComponent } from './prestation-information.component';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { SimpleTableModule } from '../../common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '../../common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '../../common/modules/tile-group/tile-group.module';
import { InputModule } from '../../common/modules/input/input.module';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { PrestationInformationSummaryComponent } from '@app/general-information/prestation-information/summary/prestation-information-summary.component';
import { HighchartsModule } from '@app/common/modules/highcharts/highcharts.module';
import { LegendModule } from '@app/common/modules/legend/legend.module';
import { PrestationInformationDetailComponent } from '@app/general-information/prestation-information/detail/prestation-information-detail.component';
import { TwoColumnsRowModule } from '@app/common/template/two-columns-row/two-columns-row.module';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
    HighchartsModule,
    LegendModule,
    TwoColumnsRowModule,
  ],
  declarations: [
    PrestationInformationComponent,
    PrestationInformationDetailComponent,
    PrestationInformationSummaryComponent,
  ],
  exports: [
    PrestationInformationComponent,
    PrestationInformationDetailComponent,
    PrestationInformationSummaryComponent,
  ],
})
export class PrestationInformationModule {}
