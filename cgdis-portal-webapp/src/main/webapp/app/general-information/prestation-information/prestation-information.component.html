<div class="accordion__panel top-none">
    <div >
  <cgdis-portal-prestation-information-summary
    [personId]="personId"
    [startDate]="startDate"
    [endDate]="endDate"
    [proOrAndVolunteer]="proOrAndVolunteer"
    [portalLabel]="portalLabel"
    [positionLabel]="positionLabel"
    [entityLabel]="entityLabel"
    [barracked]="barracked">
  </cgdis-portal-prestation-information-summary>
    </div>

    <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
        <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
        <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
    </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.activity.prestations.start'"></label>
            <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="activity-datepicker-filter" [readOnly]="true" [customFormControl]="startDateFormControl" [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig" [datatableService]="prestationService"></cgdis-portal-datatable-datepicker-filter>
        </div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.activity.prestations.end'"></label>
            <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="activity-datepicker-filter" [readOnly]="true" [customFormControl]="endDateFormControl" [filterName]="'endDateTime'" [filterConfig]="endDateFilterConfig" [datatableService]="prestationService"></cgdis-portal-datatable-datepicker-filter>
        </div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.activity.prestations.portal_label'"></label>
            <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="activity-text-filter" [filterName]="'portalLabel'"
                                                [allowClear]="true"
                                                [datatableService]="prestationService"></cgdis-portal-datatable-text-filter></div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.activity.prestations.position_label'"></label>
            <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="activity-text-filter" [filterName]="'positionLabel'"
                                                [allowClear]="true"
                                                [datatableService]="prestationService"></cgdis-portal-datatable-text-filter>
        </div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'general_information.activity.availabilities.entities_availability'"></label>
            <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" class="activity-text-filter" [filterName]="'entityLabel'"
                                                [filterConfig]="filterLike"
                                                [allowClear]="true"
                                                [datatableService]="prestationService"></cgdis-portal-datatable-text-filter>
        </div>
    </div>
  </ng-container>


  <cgdis-portal-cgdisdatatable
    [datatableService]="prestationService"
    [startSearch]="startSearch"
    [sorts]="[{dir:'asc',prop:'startDateTime'}]"
    [id]="'general-information-interventions-table-Id'"
    [showDetails]="'MOBILE'">

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'personId'"
                                          [customFormControl]="formControl"
                                          [datatableService]="prestationService"></cgdis-portal-datatable-number-filter>

    <cgdis-portal-datatable-datepicker-filter *ngIf="isMobile" [hidden]="true" [readOnly]="true" [customFormControl]="startDateFormControl" [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig" [datatableService]="prestationService"></cgdis-portal-datatable-datepicker-filter>


    <!--     The prestation start date time -->
    <ep-datatable-column [columnName]="'startDateTime'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.prestations.start' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [readOnly]="true" [customFormControl]="startDateFormControl" [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig" [datatableService]="prestationService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).startDateTime | dateFormat }}
      </ng-template>
    </ep-datatable-column>


    <cgdis-portal-datatable-datepicker-filter [hidden]="true" [customFormControl]="endDateFormControl" [filterName]="'endDateTime'" [filterConfig]="endDateFilterConfig" [datatableService]="prestationService"></cgdis-portal-datatable-datepicker-filter>
    <cgdis-portal-datatable-text-with-null-filter [hidden]="true" [customFormControl]="isProfessionalFormControl" [filterName]="'isProfessional'" [filterConfig]="filterConfigIsProfessional" [datatableService]="prestationService"></cgdis-portal-datatable-text-with-null-filter>

    <!-- The service plan slot time label -->
    <ep-datatable-column [columnName]="'slotTimeLabel'" [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        {{'admin.service_plan.version.form.totalSlots' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).timeSlot | slotFormat}} ({{cast(context.row).timeSlot.startTime | slotDuration: cast(context.row).timeSlot.endTime }})
      </ng-template>
    </ep-datatable-column>

    <!-- The service plan portal label -->
    <ep-datatable-column [columnName]="'timeSlot.version.servicePlan.portalLabel'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.prestations.portal_label' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [filterName]="'portalLabel'"
                                            [allowClear]="true"
                                            [datatableService]="prestationService" (onValueChanged)="changePortalLabel($event)"></cgdis-portal-datatable-text-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).portalLabel | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>

    <!-- The position label -->
    <ep-datatable-column [columnName]="'positionLabel'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.prestations.position_label' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [filterName]="'positionLabel'"
                                            [allowClear]="true"
                                            [datatableService]="prestationService" (onValueChanged)="changePositionLabel($event)"></cgdis-portal-datatable-text-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).positionLabel | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>

    <!-- The entity label -->
    <ep-datatable-column [columnName]="'timeSlot.version.servicePlan.entity.name'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.activity.availabilities.entities_availability' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter  [filterName]="'entityLabel'"
                                            [filterConfig]="filterLike"
                                            [allowClear]="true"
                                            [datatableService]="prestationService" (onValueChanged)="changeEntityLabel($event)"></cgdis-portal-datatable-text-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).entity | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>

    <!-- The barracked label -->
    <ep-datatable-column [columnName]="'isBarracked'" [flexGrow]="2" [sortable]="false">
      <ng-template epDatatableHeader>
        {{'service_plan.versions.type.barracked' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [filterName]="'isBarracked'"
                                              [possibleValues]="barrackedPossibleValues"
                                              [allowClear]="true"
                                              [datatableService]="prestationService" (onValueChanged)="changeBarracked($event)"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).isBarracked ? '✔' : '✘'}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        <span *ngIf="!isMobileIcon; else durationIcon">
          {{'general_information.activity.prestations.realDuration'.concat(isMobileIcon ? '_mobile' : '') | translate}}
        </span>
        <ng-template #durationIcon>
          <svg class="icon-time-real -small ng-star-inserted">
            <use xlink:href="#icon-time-real"></use>
          </svg>
        </ng-template>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).startDateTime | prestationDuration: cast(context.row).endDateTime }}
      </ng-template>
    </ep-datatable-column>

    <!-- The position label -->
    <ep-datatable-column [columnName]="'durationLabel'" [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        <span *ngIf="!isMobileIcon; else durationIcon">
          {{'general_information.activity.prestations.duration' | translate}}
        </span>
        <ng-template #durationIcon>
          <svg class="icon-time -small ng-star-inserted">
            <use xlink:href="#icon-time"></use>
          </svg>
        </ng-template>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).professional ? "N/A" : cast(context.row).duration | allowanceDuration}}
      </ng-template>
    </ep-datatable-column>


    <ep-datatable-column [columnName]="'allowance'" [flexGrow]="1.2" [sortable]="false">
      <ng-template epDatatableHeader>
        <span *ngIf="!isMobileIcon; else euroIcon">
          {{'general_information.activity.allowance'.concat(isMobileIcon ? '_mobile' : '') | translate}}
        </span>
        <ng-template #euroIcon>
          <svg class="icon-euro -small ng-star-inserted">
            <use xlink:href="#icon-euro"></use>
          </svg>
        </ng-template>
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-number-filter [hidden]="true" [filterName]="'allowance'" [filterConfig]="filterConfigAllowance" [datatableService]="prestationService"></cgdis-portal-datatable-number-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{cast(context.row).professional ? "N/A" : ((cast(context.row))| allowance)}}
      </ng-template>
    </ep-datatable-column>

    <ng-template #template let-row="row">
      <div class="error-detail-container">
        <cgdis-portal-prestation-information-detail [prestation]="cast(row)"></cgdis-portal-prestation-information-detail>
      </div>
    </ng-template>

  </cgdis-portal-cgdisdatatable>
</div>
