import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { OperationalYoungFirefighterService } from '@app/general-information/operational-young-firefighter/operational-young-firefighter.service';
import { OperationalYoungFirefighter } from '@app/model/operational-young-firefighter.model';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-operational-young-firefighter',
  templateUrl: './operational-young-firefighter.component.html',
  styleUrls: ['./_operational-young-firefighter.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OperationalYoungFirefighterService],
})
export class OperationalYoungFirefighterComponent implements OnInit {
  /**
   * The person id.
   */
  @Input() personId: number;

  initialData: OperationalYoungFirefighter;
  loading = true;

  constructor(
    private operationalYoungFirefighterService: OperationalYoungFirefighterService,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
  ) {}

  ngOnInit(): void {
    if (!this.personId) {
      this.personId = this.connectedUserService.getConnectedUserId();
    }

    this.operationalYoungFirefighterService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((value: OperationalYoungFirefighter) => {
        this.initialData = value;
        this.loading = false;
        this.cd.markForCheck();
      });
  }
}
