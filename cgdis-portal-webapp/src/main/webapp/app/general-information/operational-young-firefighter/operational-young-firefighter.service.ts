import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { PersonGeneralInformation } from '@app/model/person/person-general-information.model';
import { OperationalYoungFirefighter } from '@app/model/operational-young-firefighter.model';

@Injectable()
export class OperationalYoungFirefighterService {
  /**
   * The base url to access person services
   * @type {string[]}
   *
   */
  baseUrl = ['person'];

  constructor(private _restService: RestService) {}

  /**
   * Get general information of the connected user
   * @returns {Observable<PersonGeneralInformation>}
   */
  get(personId: number): Observable<OperationalYoungFirefighter> {
    return this._restService
      .one<OperationalYoungFirefighter>(
        ...this.baseUrl,
        'operationalyoungfirefighter',
        String(personId),
      )
      .get()
      .pipe(
        map((value: OperationalYoungFirefighter) => {
          return value;
        }),
      );
  }
}
