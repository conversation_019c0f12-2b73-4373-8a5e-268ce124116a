import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule } from '@angular/forms';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { OperationalYoungFirefighterComponent } from '@app/general-information/operational-young-firefighter/operational-young-firefighter.component';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    FormModule,
    SimplePopupModule,
  ],
  declarations: [OperationalYoungFirefighterComponent],
  exports: [OperationalYoungFirefighterComponent],
})
export class OperationalYoungFirefighterModule {}
