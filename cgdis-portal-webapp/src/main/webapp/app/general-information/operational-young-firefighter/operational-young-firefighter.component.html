<div class="row">

    <div style="height:200px" *ngIf="!initialData; else personContactInformation">
        <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
    </div>
    <ng-template #personContactInformation>
        <div class="col-sm-4">
            <table>
                <tr class="list-title">
                    <span>{{'general_information.operational_young_firefighter.first_tutor' | translate}}</span>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.lastName' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <div *ngIf="initialData.tutors !=null && initialData.tutors[0]!=null">
                        <span>{{initialData.tutors[0].lastName | defaultValue:"-"}}</span>
                        </div>
                      <div *ngIf="initialData.tutors == null || initialData.tutors[0]==null">
                        <span>-</span>
                      </div>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.firstName' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <div *ngIf="initialData.tutors !=null && initialData.tutors[0]!=null">
                        <span>{{initialData.tutors[0].firstName | defaultValue:"-"}}</span>
                        </div>
                      <div *ngIf="initialData.tutors == null || initialData.tutors[0]==null">
                        <span>-</span>
                      </div>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.phoneNumber' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <div *ngIf="initialData.tutors !=null && initialData.tutors[0]!=null">
                            <span>{{initialData.tutors[0].mobilePhone  | defaultValue:"-"}}</span>
                        </div>
                      <div *ngIf="initialData.tutors == null || initialData.tutors[0]==null">
                        <span>-</span>
                      </div>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.parental_consent' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{initialData.parentalConsent ? '✔' : '✘'}}</span>
                    </td>
                </tr>


            </table>
        </div>

        <div class="col-sm-4">
            <table>
                <tr class="list-title">
                    <span>{{'general_information.operational_young_firefighter.second_tutor' | translate}}</span>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.lastName' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <div *ngIf="initialData.tutors !=null && initialData.tutors[1]!=null">
                            <span>{{initialData.tutors[1].lastName  | defaultValue:"-"}}</span>
                        </div>
                      <div *ngIf="initialData.tutors == null || initialData.tutors[1]==null">
                        <span>-</span>
                      </div>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.firstName' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <div *ngIf="initialData.tutors !=null && initialData.tutors[1]!=null">
                            <span>{{initialData.tutors[1].firstName  | defaultValue:"-"}}</span>
                        </div>
                      <div *ngIf="initialData.tutors == null || initialData.tutors[1]==null">
                        <span>-</span>
                      </div>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'general_information.operational_young_firefighter.phoneNumber' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <div *ngIf="initialData.tutors !=null && initialData.tutors[1]!=null">
                            <span>{{initialData.tutors[1].mobilePhone  | defaultValue:"-"}}</span>
                        </div>
                      <div *ngIf="initialData.tutors == null || initialData.tutors[1]==null">
                        <span>-</span>
                      </div>
                    </td>
                </tr>

            </table>

        </div>

    </ng-template>

</div>
