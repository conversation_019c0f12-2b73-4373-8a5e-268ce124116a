import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { PRO_VOLUNTEER_BOTH } from '@app/general-information/general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { GeneralInformationService } from '@app/general-information/general-information.service';
import { TotalsInformationPrestationDetailsService } from '@app/general-information/totals-information/details/total-information-prestation-details.service';
import { takeUntil } from 'rxjs/operators';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subject } from 'rxjs';

@Component({
  selector: 'cgdis-portal-total-information-prestation-details',
  templateUrl: './total-information-prestation-details.component.html',
  styleUrls: ['./_total-information-prestation-detail.scss'],
  providers: [
    TotalsInformationPrestationDetailsService,
    GeneralInformationService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TotalInformationPrestationDetailsComponent
  implements OnInit, OnDestroy
{
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];

  isProfessional: boolean;

  detailsPrestation: any;

  loading = false;

  isMobile: boolean = false;

  private _unsubscribe$ = new Subject<void>();

  constructor(
    public totalsPrestationService: TotalsInformationPrestationDetailsService,
    private dateService: DateService,
    private generalInformationService: GeneralInformationService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.breakpointObserver
      .observe(['(max-width: 768px)'])
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((result: BreakpointState) => {
        this.isMobile = result.matches;
        this.cd.markForCheck();
      });
  }

  ngOnInit() {
    this.loading = true;
    this.loadProVolunteerChoice();
    this.loadData();
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  private loadProVolunteerChoice(): void {
    let result: Map<string, boolean> =
      this.generalInformationService.buildProVolunteerChoice(
        this.proOrAndVolunteer,
      );
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessional = true;
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessional = false;
    } else if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessional = null;
    }
  }

  loadData(): void {
    let susbscition = this.totalsPrestationService
      .getTotalPrestationDetail(
        this.personId,
        this.startDate,
        this.endDate,
        this.isProfessional,
      )
      .subscribe((value) => {
        this.detailsPrestation = value;
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  public convertSecondsToDuration(seconds: number): string {
    return this.getHours(seconds) + 'h' + this.getMinutes(seconds);
  }

  /**
   * Extract number of minutes in given number of seconds
   * @param seconds
   */
  private getMinutes(seconds: number): string {
    if (seconds <= 0 || seconds === undefined) {
      return '00';
    }

    seconds %= 3600;
    let minutes = Math.floor(seconds / 60);

    if (minutes < 10) {
      return '0' + minutes;
    }
    return '' + minutes;
  }

  /**
   * Get number of hours in given number of seconds
   * @param seconds
   */
  private getHours(seconds: number): number {
    return seconds > 0 ? Math.floor(seconds / 3600) : 0;
  }
}
