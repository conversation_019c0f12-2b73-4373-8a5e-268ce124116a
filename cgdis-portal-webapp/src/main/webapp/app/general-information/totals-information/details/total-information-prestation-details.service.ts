import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DateModel, RestService } from '@eportal/core';
import { Observable } from 'rxjs';

@Injectable()
export class TotalsInformationPrestationDetailsService {
  private baseUrl = ['totals', 'prestations'];

  constructor(private restService: RestService) {}

  /**
   * Get all existing intervention types
   * @return {Observable<InterventionType[]>}
   */
  getTotalPrestationDetail(
    personId: number,
    startDate: DateModel,
    endDate: DateModel,
    isPro: boolean,
  ): Observable<any> {
    const restResource = this.restService.one(...this.baseUrl);
    let params: any = {
      personId: personId,
      startDate: startDate,
      endDate: endDate,
    };
    if (isPro != undefined) {
      params.isPro = isPro;
    }
    return restResource.get(params).pipe(
      map((value: any) => {
        return value;
      }),
    );
  }
}
