<cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
<div *ngIf="!loading && detailsPrestation.length>0; else noData">
  <div *ngIf="isMobile; else desktopView">
    <div class="col-sm-12">
      <table *ngFor="let oneDetails of detailsPrestation" class="mobile-table">
        <tr>
          <td>
            <b>{{'general_information.activity.totals.details.entity' | translate}}</b>
          </td>
          <td>
            {{oneDetails.entityName}}
          </td>
        </tr>
        <tr *ngIf="isProfessional != null && isProfessional">
          <td>
            <b>{{'general_information.activity.totals.details.realDuration' | translate}}</b>
          </td>
          <td>
            {{convertSecondsToDuration(oneDetails.duration)}}
          </td>
          <td>
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </td>
          <td>
            <span>{{'general_information.activity.totals.details.sum' | translate}}: N/A</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}: N/A</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}: N/A</span>
          </td>
        </tr>
        <tr *ngIf="isProfessional != null && !isProfessional">
          <td>
            <b>{{'general_information.activity.totals.details.realDuration' | translate}}</b>
          </td>
          <td>
            {{convertSecondsToDuration(oneDetails.duration)}}
          </td>
          <td>
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </td>
          <td>
          <span>{{'general_information.activity.totals.details.sum' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowance, allowances:oneDetails.allowance} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceBarracked, allowances:oneDetails.allowanceBarracked} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceNoBarracked, allowances:oneDetails.allowanceNoBarracked} | allowance }}</span><br/>
          </td>
        </tr>
        <tr *ngIf="isProfessional == null">
          <td>
            <b
              *ngIf="isMobile; else desktopProDuration">{{'general_information.activity.totals.details.proDuration_mobile' | translate}}</b>
            <ng-template #desktopProDuration>
              <b>{{'general_information.activity.totals.details.proDuration' | translate}}</b>
            </ng-template>
          </td>
          <td>
            {{convertSecondsToDuration(oneDetails.durationPro)}}
          </td>

          <td>
            <b
              *ngIf="isMobile; else desktopVolDuration">{{'general_information.activity.totals.details.volDuration_mobile' | translate}}</b>
            <ng-template #desktopVolDuration>
              <b>{{'general_information.activity.totals.details.volDuration' | translate}}</b>
            </ng-template>
          </td>
          <td>
            {{convertSecondsToDuration(oneDetails.durationVol)}}
          </td>
          <td>
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </td>
          <td>
          <span>{{'general_information.activity.totals.details.sum' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowance, allowances:oneDetails.allowance} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceBarracked, allowances:oneDetails.allowanceBarracked} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceNoBarracked, allowances:oneDetails.allowanceNoBarracked} | allowance }}</span><br/>
          </td>
        </tr>
      </table>
    </div>
  </div>


  <ng-template #desktopView>
    <table class="col-12">
      <tbody>
      <tr class="col-12" style="display: inline-table">
        <td class="col-3">
          <b>{{'general_information.activity.totals.details.entity' | translate}}</b>
        </td>
        <ng-container *ngIf="isProfessional != null && isProfessional">

          <td class="col-3">
            <b>{{'general_information.activity.totals.details.realDuration' | translate}}</b>
          </td>
          <td class="col-3">
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </td>
        </ng-container>
        <ng-container *ngIf="isProfessional != null && !isProfessional">

          <td class="col-3">
            <b>{{'general_information.activity.totals.details.realDuration' | translate}}</b>
          </td>
          <td class="col-3">
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </td>
        </ng-container>
        <ng-container *ngIf="isProfessional==null">
          <td class="col-3">
            <b
              *ngIf="isMobile; else desktopProDuration">{{'general_information.activity.totals.details.proDuration_mobile' | translate}}</b>
            <ng-template #desktopProDuration>
              <b>{{'general_information.activity.totals.details.proDuration' | translate}}</b>
            </ng-template>
          </td>
          <td class="col-3">
            <b
              *ngIf="isMobile; else desktopVolDuration">{{'general_information.activity.totals.details.volDuration_mobile' | translate}}</b>
            <ng-template #desktopVolDuration>
              <b>{{'general_information.activity.totals.details.volDuration' | translate}}</b>
            </ng-template>
          </td>
          <td class="col-3">
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </td>
        </ng-container>
      </tr>
      <tr class="col-12" style="display: inline-table; border-bottom: 1px solid #e9eef5"
          *ngFor="let oneDetails of detailsPrestation">
        <td class="col-3">{{oneDetails.entityName}}</td>
        <ng-container *ngIf="isProfessional != null && isProfessional">

          <td class="col-3">{{convertSecondsToDuration(oneDetails.duration)}}</td>
          <td class="col-3">
            <span>{{'general_information.activity.totals.details.sum' | translate}}: N/A</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}: N/A</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}: N/A</span>
          </td>
        </ng-container>
        <ng-container *ngIf="isProfessional != null && !isProfessional">


          <td class="col-3">{{convertSecondsToDuration(oneDetails.duration)}}</td>
          <td class="col-3">
          <span>{{'general_information.activity.totals.details.sum' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowance, allowances:oneDetails.allowance} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceBarracked, allowances:oneDetails.allowanceBarracked} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceNoBarracked, allowances:oneDetails.allowanceNoBarracked} | allowance }}</span><br/>
          </td>
        </ng-container>
        <ng-container *ngIf="isProfessional==null">

          <td class="col-3">{{convertSecondsToDuration(oneDetails.durationPro)}}</td>
          <td class="col-3">{{convertSecondsToDuration(oneDetails.durationVol)}}</td>
          <td class="col-3">
          <span>{{'general_information.activity.totals.details.sum' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowance, allowances:oneDetails.allowance} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowanceBarracked, allowances:oneDetails.allowanceBarracked} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowanceNoBarracked, allowances:oneDetails.allowanceNoBarracked} | allowance }}</span><br/>
          </td>
        </ng-container>
      </tr>
      </tbody>
    </table>
  </ng-template>
</div>

<ng-template #noData>
  <span [translate]="'no_data'" class="p-5 d-flex"></span>
</ng-template>
