import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { GeneralInformationService } from '../general-information.service';
import { TotalsInformationService } from './totals-information.service';
import { Subscription } from 'rxjs';
import { Total } from '@app/model/total.model';
import { PRO_VOLUNTEER_BOTH } from '@app/general-information/general-information-activity-tab/pro-volunteer-choice/pro-volunteer-choice.component';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-totals-information',
  templateUrl: './totals-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [TotalsInformationService, GeneralInformationService],
})
export class TotalsInformationComponent
  implements OnInit, OnChanges, OnDestroy
{
  /**
   * The person id.
   */
  @Input() personId: number;
  @Input() startDate: DateModel;
  @Input() endDate: DateModel;
  @Input() proOrAndVolunteer: [string, boolean];

  startDateFilterConfig: FilterConfig;
  endDateFilterConfig: FilterConfig;
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  filterConfigIsProfessional = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  startDateFormControl: UntypedFormControl = new UntypedFormControl();
  endDateFormControl: UntypedFormControl = new UntypedFormControl();
  isProfessionalFormControl = new UntypedFormControl();
  subscription: Subscription;
  totals: Total[];
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  isProfessional: boolean;

  constructor(
    public totalsService: TotalsInformationService,
    private dateService: DateService,
    private generalInformationService: GeneralInformationService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    if (this.startDate === undefined) {
      this.startDateFilterConfig = new FilterConfig({
        operator: SearchOperator.eq,
        inUrl: false,
        defaultValue: this.startDate,
      });
    }
    if (this.endDateFilterConfig === undefined) {
      this.endDateFilterConfig = new FilterConfig({
        operator: SearchOperator.eq,
        inUrl: false,
        defaultValue: this.endDate,
      });
    }
    this.formControl.setValue(this.personId);
    this.startDateFormControl.setValue(this.startDate);
    this.endDateFormControl.setValue(this.endDate);
    this.loadProVolunteerChoice();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  public convertSecondsToDuration(seconds: number): string {
    return this.getHours(seconds) + 'h' + this.getMinutes(seconds);
  }

  /**
   * Extract number of minutes in given number of seconds
   * @param seconds
   */
  private getMinutes(seconds: number): string {
    if (seconds <= 0 || seconds === undefined) {
      return '00';
    }

    seconds %= 3600;
    let minutes = Math.floor(seconds / 60);

    if (minutes < 10) {
      return '0' + minutes;
    }
    return '' + minutes;
  }

  /**
   * Get number of hours in given number of seconds
   * @param seconds
   */
  private getHours(seconds: number): number {
    return seconds > 0 ? Math.floor(seconds / 3600) : 0;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.startDate || changes.endDate) {
      if (
        changes.startDate.previousValue === undefined &&
        changes.endDate.previousValue === undefined
      ) {
        this.startDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.startDate,
          );
        this.endDateFilterConfig =
          this.generalInformationService.loadDateTimeFilterConfig(
            SearchOperator.eq,
            false,
            this.endDate,
          );
      } else if (
        (changes.startDate &&
          changes.startDate.previousValue !== changes.startDate.currentValue) ||
        (changes.endDate &&
          changes.endDate.previousValue !== changes.endDate.currentValue)
      ) {
        this.startDateFormControl.setValue(this.startDate);
        this.endDateFormControl.setValue(this.endDate);
      }
    }

    if (
      changes.proOrAndVolunteer &&
      changes.proOrAndVolunteer.previousValue !==
        changes.proOrAndVolunteer.currentValue
    ) {
      this.loadProVolunteerChoice();
    }
  }

  private loadProVolunteerChoice(): void {
    let result: Map<string, boolean> =
      this.generalInformationService.buildProVolunteerChoice(
        this.proOrAndVolunteer,
      );
    if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      !result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessionalFormControl.setValue(true);
      this.isProfessional = true;
    } else if (
      !result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessionalFormControl.setValue(false);
      this.isProfessional = false;
    } else if (
      result.get(PRO_VOLUNTEER_BOTH.is_professional) &&
      result.get(PRO_VOLUNTEER_BOTH.is_volunteer)
    ) {
      this.isProfessionalFormControl.setValue(null);
      this.isProfessional = null;
    }
  }
}
