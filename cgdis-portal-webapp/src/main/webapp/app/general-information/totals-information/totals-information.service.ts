import { Injectable } from '@angular/core';

import { RestService } from '@eportal/core';
import { Total } from '@app/model/total.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';

@Injectable()
export class TotalsInformationService extends CgdisDatatableService<Total> {
  private baseUrl = ['totals', 'all'];

  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(restService.all('totals', 'all'));
  }
}
