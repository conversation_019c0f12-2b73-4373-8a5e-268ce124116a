<div class="accordion__panel">
  <cgdis-portal-cgdisdatatable
    [datatableService]="totalsService"
            [id]="'general-information-totals-table-Id'"
    [showDetails]="'ALWAYS'">

    <ng-template #template let-row="row">
      <div *ngIf="row.label == 'prestations'; else noData" class="error-detail-container">
        <cgdis-portal-total-information-prestation-details [personId]="personId" [startDate]="startDate"
                                                           [endDate]="endDate"
                                                           [proOrAndVolunteer]="proOrAndVolunteer"></cgdis-portal-total-information-prestation-details>
      </div>

      <ng-template #noData>
        <span [translate]="'no_data'" class="p-5 d-flex"></span>
      </ng-template>
    </ng-template>

    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'personId'"
                                          [customFormControl]="formControl"
                                          [datatableService]="totalsService"
    ></cgdis-portal-datatable-number-filter>

    <cgdis-portal-datatable-text-with-null-filter [hidden]="true" [customFormControl]="isProfessionalFormControl"
                                                  [filterName]="'isProfessional'"
                                                  [filterConfig]="filterConfigIsProfessional"
                                                  [datatableService]="totalsService"></cgdis-portal-datatable-text-with-null-filter>
    <cgdis-portal-datatable-datepicker-filter [hidden]="true" [customFormControl]="startDateFormControl"
                                              [filterName]="'startDateTime'" [filterConfig]="startDateFilterConfig"
                                              [datatableService]="totalsService"></cgdis-portal-datatable-datepicker-filter>
    <cgdis-portal-datatable-datepicker-filter [hidden]="true" [customFormControl]="endDateFormControl"
                                              [filterName]="'endDateTime'" [filterConfig]="endDateFilterConfig"
                                              [datatableService]="totalsService"></cgdis-portal-datatable-datepicker-filter>
    <ep-datatable-column [columnName]="'label'" [sortable]="false" [flexGrow]="3">
      <ng-template epDatatableHeader>
        {{'general_information.activity.totals.label' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{('general_information.activity.detail.' + context.row.label.concat(isMobile?'_mobile':'') | translate) | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'Duration'" [sortable]="false" [flexGrow]="2">
      <ng-template epDatatableHeader>
            <span *ngIf="!isMobile; else durationIcon">
              {{'general_information.activity.totals.duration' | translate}}
            </span>
        <ng-template #durationIcon>
          <svg class="icon-time -small ng-star-inserted datatable-header-icon__notabsolute">
            <use xlink:href="#icon-time"></use>
          </svg>
        </ng-template>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{convertSecondsToDuration(context.row.duration) | defaultValue:'-'}}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'Allowance'" [sortable]="false" [flexGrow]="2">
      <ng-template epDatatableHeader>
              <span *ngIf="!isMobile; else euroIcon">
                {{'general_information.activity.totals.allowance'.concat(isMobile ? '_mobile' : '') | translate}}
              </span>
        <ng-template #euroIcon>
          <svg class="icon-euro -small ng-star-inserted datatable-header-icon__notabsolute">
            <use xlink:href="#icon-euro"></use>
          </svg>
        </ng-template>
      </ng-template>
      <ng-template let-context epDatatableCell>
        <div *ngIf="(isProfessional != null && isProfessional) || context.row.label=='interventions'">
          N/A
        </div>

        <div *ngIf="(isProfessional == null || !isProfessional) &&  context.row.label!='interventions'">
          {{ {hasAllowances: context.row.hasAllowance, allowances: context.row.allowance } | allowance}}
        </div>
      </ng-template>
    </ep-datatable-column>
  </cgdis-portal-cgdisdatatable>
</div>
