import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PersonalInformationComponent } from './personal-information.component';
import { SharedModule } from '../../common/shared/shared.module';
import { FormModule } from '../../common/modules/form-module/form.module';
import { DatepickerModule } from '../../common/modules/datepicker/datepicker.module';
import { MatExpansionModule } from '@angular/material/expansion';

@NgModule({
  imports: [
    SharedModule,
    FormModule,
    DatepickerModule,
    CommonModule,
    MatExpansionModule,
  ],
  exports: [PersonalInformationComponent],
  declarations: [PersonalInformationComponent],
})
export class PersonalInformationModule {}
