import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { DateService } from '@eportal/core';
import { PersonalInformationService } from './personal-information.service';
import { PersonGeneralInformation } from '@app/model/person/person-general-information.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-personal-information',
  templateUrl: './personal-information.component.html',
  styleUrls: ['./_personal-information.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PersonalInformationService],
})
export class PersonalInformationComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  @Input() logas = false;

  initialData: PersonGeneralInformation;
  loading = true;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public dateService: DateService,
    private personalInformationService: PersonalInformationService,
    private cd: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.personalInformationService
      .get(this.personId)
      .pipe(take(1))
      .subscribe((pi) => {
        this.initialData = pi;
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
