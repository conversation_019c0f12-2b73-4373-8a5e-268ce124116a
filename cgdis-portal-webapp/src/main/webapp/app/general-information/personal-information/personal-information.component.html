<div class="row">

  <div style="height:200px" *ngIf="!initialData; else personContactInformation">
    <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
  </div>
  <ng-template #personContactInformation>
    <div class="col-sm-4">
      <table>
        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.sex' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{'general_information.personal_information.gender_' + initialData.gender.toLowerCase() | translate}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.person_title' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.title | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.firstname' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.firstName | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.lastname' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.lastName | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.birthdate' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{this.dateService.format(initialData.birthDate)}}</span> <!--//Format-->
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.birthplace' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.birthPlace | defaultValue:"-"}}</span>
          </td>
        </tr>
      </table>
    </div>
    <div class="col-sm-4">
      <table>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.cgdisrn' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.cgdisRegistrationNumber | defaultValue:"-"}}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.nationalRegistrationNumber' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.nationalRegistrationNumber | defaultValue:"-"}}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.iam' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.iamNumber | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.hiring' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{this.dateService.format(initialData.hiringDate, dateFormat)}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.vacation' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{this.dateService.format(initialData.vacationDate, dateFormat) | defaultValue:"-"}}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.status' | translate}}</span>
          </td>
          <td class="list-value">
            <ul>
              <li *ngIf="initialData.volunteer">
                {{'general_information.personal_information.is_volunteer' | translate}}
              </li>
              <li *ngIf="initialData.veteran">
                {{'general_information.personal_information.is_veteran' | translate}}
              </li>
              <li *ngIf="initialData.retired">
                {{'general_information.personal_information.is_retired' | translate}}
              </li>
              <li *ngIf="initialData.external">
                {{'general_information.personal_information.is_external' | translate}}
              </li>
              <li *ngIf="initialData.youngFirefighter">
                {{'general_information.personal_information.is_young_firefighter' | translate}}
              </li>
              <li *ngIf="initialData.professional">
                {{'general_information.personal_information.is_professional' | translate}}
              </li>
              <li *ngIf="initialData.professionalOperational">
                {{'general_information.personal_information.is_professional_operational' | translate}}
              </li>
              <li *ngIf="initialData.professionalAdmTech">
                {{'general_information.personal_information.is_professional_adm_tech' | translate}}
              </li>
              <li *ngIf="initialData.intern">
                {{'general_information.personal_information.is_intern' | translate}}
              </li>
              <li *ngIf="initialData.candidate">
                {{'general_information.personal_information.is_candidate' | translate}}
              </li>
              <li *ngIf="initialData.operationalFirefighter">
                {{'general_information.personal_information.is_operational_firefighter' | translate}}
              </li>
              <li *ngIf="initialData.supportFirefighter">
                {{'general_information.personal_information.is_support_firefighter' | translate}}
              </li>
            </ul>
          </td>
        </tr>

      </table>
    </div>
    <div class="col-sm-3"   *cgdisPortalAuthRoles="logas ? ['ROLE_PERMISSION_PROFILE_TIC_LOGAS_VIEW'] : ['ROLE_PERMISSION_PROFILE_TIC_VIEW']">
      <table>
        <tr>
          <td class="list-label">
            <span>{{'general_information.personal_information.tic' | translate}}</span>
          </td>
          <td class="list-value">
            <span>{{initialData.tic }}</span>
          </td>
        </tr>


      </table>
    </div>
  </ng-template>

</div>
