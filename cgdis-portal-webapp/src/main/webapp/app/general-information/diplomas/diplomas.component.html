<div class="accordion__panel">

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.diplomas.list.name'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [filterName]="'diploma.name'" [filterConfig]="filterLike" [datatableService]="diplomasService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.diplomas.list.type'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [filterName]="'type'" [filterConfig]="filterLike" [datatableService]="diplomasService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.diplomas.list.ident'"></label>
        <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [filterName]="'diploma.ident'" [filterConfig]="filterLike" [datatableService]="diplomasService"></cgdis-portal-datatable-text-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.diplomas.list.validFrom'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" [filterName]="'validFrom'" [filterConfig]="startDateFilterConfig" [datatableService]="diplomasService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'general_information.diplomas.list.validUntil'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" [filterName]="'validUntil'" [filterConfig]="startDateFilterConfig" [datatableService]="diplomasService"></cgdis-portal-datatable-datepicker-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [datatableService]="diplomasService"
    [sorts]="[{dir:'asc',prop:'diploma.name'}]"
    [id]="'general-information-driving-license-table-Id'"
    [showDetails]="'MOBILE'">

    <!-- Filter for person -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigPersonId"
                                          [filterName]="'person'"
                                          [customFormControl]="formControl"
                                          [datatableService]="diplomasService"></cgdis-portal-datatable-number-filter>

    <ng-template #template let-row="row">
      <div class="error-detail-container">
        <cgdis-portal-diplomas-detail [diploma]="row"></cgdis-portal-diplomas-detail>
      </div>
    </ng-template>

    <ep-datatable-column [columnName]="'diploma.name'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.diplomas.list.name' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.diploma.name | defaultValue:'-'}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'diploma.name'" [filterConfig]="filterLike" [datatableService]="diplomasService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'diploma.ident'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        {{'general_information.diplomas.list.ident' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.diploma.ident | defaultValue:'-'}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [allowClear]="true" [filterName]="'diploma.ident'" [filterConfig]="filterLike" [datatableService]="diplomasService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'validFrom'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        {{'general_information.diplomas.list.validFrom' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'validFrom'" [filterConfig]="startDateFilterConfig" [datatableService]="diplomasService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.validFrom)}}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'validUntil'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        {{'general_information.diplomas.list.validUntil' | translate}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [filterName]="'validUntil'" [filterConfig]="startDateFilterConfig" [datatableService]="diplomasService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.validUntil)}}
      </ng-template>
    </ep-datatable-column>



  </cgdis-portal-cgdisdatatable>
</div>
