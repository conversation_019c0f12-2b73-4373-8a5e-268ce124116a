import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { CommonModule } from '@angular/common';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SimpleTableModule } from '@app/common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '@app/common/modules/tile-group/tile-group.module';
import { InputModule } from '@app/common/modules/input/input.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { DiplomasComponent } from '@app/general-information/diplomas/diplomas.component';
import { DiplomasDetailComponent } from './detail/diplomas-detail.component';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
  ],
  declarations: [DiplomasComponent, DiplomasDetailComponent],
  exports: [DiplomasComponent, DiplomasDetailComponent],
})
export class DiplomasModule {}
