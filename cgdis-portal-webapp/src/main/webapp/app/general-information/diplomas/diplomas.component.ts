import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { DiplomasService } from '@app/general-information/diplomas/diplomas.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-diplomas',
  templateUrl: './diplomas.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DiplomasService],
})
export class DiplomasComponent implements OnInit, OnDestroy {
  /**
   * The person id.
   */
  @Input() personId: number;

  showFilter = false;
  startDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  endDateFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  filterLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });
  filterConfigPersonId = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  dateFormat = 'DD/MM/YYYY';

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    public diplomasService: DiplomasService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.formControl.setValue(this.personId);

    this.diplomasService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.diplomasService.getNumberOfFilters([
          'person',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.diplomasService.getNumberOfFilters(['person']);
  }

  /**
   * Get formatted date
   * @param date
   */
  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }
}
