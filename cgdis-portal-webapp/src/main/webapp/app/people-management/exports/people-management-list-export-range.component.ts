import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FromToPopupDataModel } from '@app/common/modules/popup/from-to-popup/from-to-popup-data-model';
import { FromToPopupComponent } from '@app/common/modules/popup/from-to-popup/from-to-popup.component';
import { PeopleManagementListExportRangeDataModelContent } from '@app/people-management/exports/people-management-list-export-range-content';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { PeopleManagementListExportRangeService } from '@app/people-management/exports/people-management-list-export-range.service';

@Component({
  selector: 'cgdis-portal-people-management-list-export-range',
  templateUrl: './people-management-list-export-range.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    PeopleManagementListExportRangeService,
    {
      provide: FORM_SERVICE,
      useExisting: PeopleManagementListExportRangeService,
    },
  ],
})
export class PeopleManagementListExportRangeComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: FromToPopupDataModel<PeopleManagementListExportRangeDataModelContent>,
    public dialogRef: MatDialogRef<FromToPopupComponent>,
    public formService: PeopleManagementListExportRangeService,
  ) {}

  ngOnInit(): void {}

  closePopup() {
    this.dialogRef.close();
  }
}
