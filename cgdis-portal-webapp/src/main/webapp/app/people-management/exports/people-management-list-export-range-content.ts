import {
  FromToPopupDataModelContent,
  FromToPopupForm,
} from '@app/common/modules/popup/from-to-popup/from-to-popup-data-model';

export class PeopleManagementListExportRangeForm extends FromToPopupForm {
  entityTecid: number;

  constructor(args: PeopleManagementListExportRangeForm) {
    super(args);
    this.entityTecid = args.entityTecid;
  }
}

export class PeopleManagementListExportRangeDataModelContent extends FromToPopupDataModelContent {
  entityTecid: number;

  constructor(args: PeopleManagementListExportRangeForm) {
    super(args);
    this.entityTecid = args.entityTecid;
  }
}
