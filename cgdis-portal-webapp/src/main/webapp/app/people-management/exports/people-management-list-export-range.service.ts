import { Injectable } from '@angular/core';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { Observable, Subscriber } from 'rxjs';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { PeopleManagementListExportRangeForm } from '@app/people-management/exports/people-management-list-export-range-content';
import { EntityService } from '@app/common/shared/services/entity.service';

@Injectable()
export class PeopleManagementListExportRangeService extends DefaultFormService<
  PeopleManagementListExportRangeForm,
  any
> {
  constructor(
    toastr: ToastService,
    formErrorService: FormErrorService,
    private entitiesService: EntityService,
  ) {
    super(toastr, 'service_plan.popup.export.pdf.success', formErrorService);
  }

  submit(parameter: PeopleManagementListExportRangeForm): Observable<any> {
    return new Observable((subscriber) => {
      this.exportGlobal(parameter, subscriber);
    });
  }

  private exportGlobal(
    parameter: PeopleManagementListExportRangeForm,
    subscriber: Subscriber<any>,
  ) {
    if (parameter.entityTecid != null) {
      this.entitiesService
        .exportPrestationsGlobal(
          parameter.entityTecid,
          parameter.from,
          parameter.to,
        )
        .subscribe(
          () => {},
          (e) => subscriber.error(e),
          () => subscriber.next(),
        );
    } else {
      this.entitiesService
        .exportPrestationsGlobalNoAssignment(parameter.from, parameter.to)
        .subscribe(
          () => {},
          (e) => subscriber.error(e),
          () => subscriber.next(),
        );
    }
  }

  submitError(formError: FormError): void {}

  submitSuccess(result: any): void {}
}
