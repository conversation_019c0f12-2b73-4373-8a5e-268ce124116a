<cgdis-portal-cgdisdatatable
  [datatableService]="peopleManagementFunctionPersonListService"
  [sorts]="[{dir:'asc',prop:'portalLabel'}]"
  [id]="'people-management-summary-table-Id'">

  <cgdis-portal-datatable-select-filter [filterName]="'tecid'"
                                        [hidden]="true"
                                        [datatableService]="peopleManagementFunctionPersonListService"
                                        [customFormControl]="formControl"
                                        [multiple]="false"
  ></cgdis-portal-datatable-select-filter>

  <cgdis-portal-datatable-select-filter [filterName]="'ids'"
                                        [hidden]="true"
                                        [datatableService]="peopleManagementFunctionPersonListService"
                                        [allowClear]="true"
                                        [customFormControl]="formControlForIds"
                                        [multiple]="true"
  ></cgdis-portal-datatable-select-filter>

  <cgdis-portal-datatable-number-filter [hidden]="true"
                                        [filterConfig]="filterConfigEntityId"
                                        [filterName]="'entityId'"
                                        [customFormControl]="entityFormControl"
                                        [datatableService]="peopleManagementFunctionPersonListService"
  ></cgdis-portal-datatable-number-filter>

  <cgdis-portal-datatable-number-filter [hidden]="true"
                                        [filterConfig]="filterConfigSubentities"
                                        [filterName]="'subentities'"
                                        [customFormControl]="subentitiesFormControl"
                                        [datatableService]="peopleManagementFunctionPersonListService"
  ></cgdis-portal-datatable-number-filter>

  <ep-datatable-column [columnName]="'firstName'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span [translate]="'people_management.functions.details.firstName'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{context.row.firstName}}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'lastName'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span [translate]="'people_management.functions.details.lastName'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{context.row.lastName}}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span [translate]="'people_management.functions.details.cgdisRegistrationNumber'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{context.row.cgdisRegistrationNumber}}
    </ng-template>
  </ep-datatable-column>

  <!-- professional (boolean) -->
  <ep-datatable-column [columnName]="'isProfessional'" [sortable]="false" [flexGrow]="parameter.pro ? 1:0">
    <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.professional_desktop_reduced' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ (context.row.isProfessional ? '✔' : '-') }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'isVolunteer'" [sortable]="false" [flexGrow]="parameter.vol ? 1:0">
    <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.volunteer_desktop_reduced' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ (context.row.isVolunteer ? '✔' : '-') }}
    </ng-template>
  </ep-datatable-column>

  <!-- samu (boolean) -->
  <ep-datatable-column [columnName]="'isSamu'" [sortable]="false" [flexGrow]="parameter.ext ? 1:0">
    <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.samu' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ (context.row.isExternal ? '✔' : '-') }}
    </ng-template>
  </ep-datatable-column>

</cgdis-portal-cgdisdatatable>
