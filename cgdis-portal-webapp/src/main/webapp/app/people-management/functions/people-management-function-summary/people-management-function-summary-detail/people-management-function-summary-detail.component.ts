import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { PeopleManagementFunctionPersonListService } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-detail/people-management-function-person-list.service';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';

@Component({
  selector: 'cgdis-portal-people-management-function-summary-detail',
  templateUrl: './people-management-function-summary-detail.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PeopleManagementFunctionPersonListService],
})
export class PeopleManagementFunctionSummaryDetailComponent
  implements OnInit, OnChanges
{
  @Input()
  public operationFunctionTecId: number;

  @Input()
  public entityId: number;

  @Input()
  parameter: PeopleManagementFunctionSummaryParameter;

  formControl = new UntypedFormControl();
  formControlForIds = new UntypedFormControl();

  entityFormControl = new UntypedFormControl();

  filterConfigEntityId = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });

  filterConfigForIds = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  filterConfigSubentities = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  subentitiesFormControl = new UntypedFormControl();

  constructor(
    public peopleManagementFunctionPersonListService: PeopleManagementFunctionPersonListService,
  ) {}

  ngOnInit() {
    this.formControl.setValue(this.operationFunctionTecId);
    this.entityFormControl.setValue(this.entityId);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.parameter) {
      this.entityFormControl.setValue(this.entityId, { emitEvent: false });
      this.formControlForIds.setValue(this.parameter.functionOperationalIds);
      this.subentitiesFormControl.setValue(this.parameter.subentities);
    }
  }
}
