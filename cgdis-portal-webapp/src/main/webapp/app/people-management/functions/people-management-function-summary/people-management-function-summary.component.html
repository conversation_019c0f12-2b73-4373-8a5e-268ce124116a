<div class="row  align-items-center justify-content-center">
  <div  class="row col-12">

    <div class="row col-12">
      <div *ngFor="let s of interventionTypes" class="col-lg-3 col-md-3 col-sm-3 col-3">
        <div class="performance performance__first">

          <div class="performance__figure">
          <span class="performance__figure__title"
                [translate]="isMobile ? 'dashboard.members.chart.' + s.label + '-mobile' : 'dashboard.members.chart.' + s.label"></span><br/><br>

            <div class="performance__figure_content">
              <ul>
                <li class="statistics-value">
                  <span>{{s.number}}</span>
                </li>

              </ul>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>

</div>

<div style="margin-top: 2rem;">
  <cgdis-portal-cgdisdatatable
    [datatableService]="peopleManagementFunctionSummaryService"
    [sorts]="[{dir:'asc',prop:'portalLabel'}]"
    [id]="'people-management-summary-table-Id'">

    <ng-template #template let-row="row">
      <div class="person-detail-row margin">
        <cgdis-portal-people-management-function-summary-detail [operationFunctionTecId]="row.tecid"
                                                                [entityId]="this.parameter.entityId"
                                                                [parameter]="parameter"
        ></cgdis-portal-people-management-function-summary-detail>
      </div>
    </ng-template>

    <cgdis-portal-datatable-select-filter [filterName]="'ids'"
                                          [hidden]="true"
                                          [datatableService]="peopleManagementFunctionSummaryService"
                                          [allowClear]="true"
                                          [customFormControl]="formControl"
                                          [multiple]="true"
    ></cgdis-portal-datatable-select-filter>

    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigEntityId"
                                          [filterName]="'entityId'"
                                          [customFormControl]="entityFormControl"
                                          [datatableService]="peopleManagementFunctionSummaryService"
    ></cgdis-portal-datatable-number-filter>

    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigSubentities"
                                          [filterName]="'subentities'"
                                          [customFormControl]="subentitiesFormControl"
                                          [datatableService]="peopleManagementFunctionSummaryService"
    ></cgdis-portal-datatable-number-filter>

    <ep-datatable-column [columnName]="'portalLabel'" [sortable]="false" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.function_operational_mobile' : 'people_management.functions.function_operational'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.portalLabel}}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'type'" [sortable]="false" [flexGrow]="1">
      <ng-template epDatatableHeader>
        {{'people_management.functions.type' | translate}}
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{'intervention.types.' + context.row.interventionType | translate}}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'number'" [sortable]="false" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_pro_mobile' : 'people_management.functions.person_number_pro'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.countPro}}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'numberVol'" [sortable]="false" [flexGrow]="parameter.vol ? 1:0">
      <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_vol_mobile' : 'people_management.functions.person_number_vol'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.countVol}}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'numberExt'" [sortable]="false" [flexGrow]="parameter.ext ? 1:0">
      <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_ext_mobile' : 'people_management.functions.person_ext_number'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{context.row.countExt}}
      </ng-template>
    </ep-datatable-column>
  </cgdis-portal-cgdisdatatable>
</div>

