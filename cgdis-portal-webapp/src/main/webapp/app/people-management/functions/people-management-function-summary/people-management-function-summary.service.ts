import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { Observable } from 'rxjs';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';

@Injectable()
export class PeopleManagementFunctionSummaryService extends CgdisDatatableService<FunctionOperationalSummary> {
  private baseUrl = ['admin', 'function-operational', 'summary'];
  private baseUrlCount = ['admin', 'function-operational', 'summaryCount'];

  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(
      restService.all('admin', 'function-operational', 'summary'),
    );
  }

  getSummaryCount(
    parameter: PeopleManagementFunctionSummaryParameter,
  ): Observable<FunctionOperationalSummary[]> {
    return this.restService
      .one<FunctionOperationalSummary[]>(...this.baseUrlCount)
      .get({
        ids: parameter.functionOperationalIds,
        entityId: parameter.entityId,
        subentities: parameter.subentities,
      });
  }
}
