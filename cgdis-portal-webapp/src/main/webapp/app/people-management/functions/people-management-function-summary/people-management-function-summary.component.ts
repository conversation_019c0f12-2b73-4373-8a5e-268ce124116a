import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { Subject, Subscription } from 'rxjs';
import * as _ from 'lodash';
import { PeopleManagementFunctionSummaryService } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary.service';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { UntypedFormControl } from '@angular/forms';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { takeUntil } from 'rxjs/operators';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { InterventionTypeWithCount } from '@app/model/intervention-type.model';

@Component({
  selector: 'cgdis-portal-people-management-function-summary',
  templateUrl: './people-management-function-summary.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PeopleManagementFunctionSummaryService],
})
export class PeopleManagementFunctionSummaryComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() parameter: PeopleManagementFunctionSummaryParameter;

  subscriptions: Subscription[] = [];
  filterConfigPersonId = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  formControl = new UntypedFormControl();
  filterConfigEntityId = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  entityFormControl = new UntypedFormControl();
  filterConfigSubentities = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });
  subentitiesFormControl = new UntypedFormControl();
  summary: FunctionOperationalSummary[];
  summaryCount: FunctionOperationalSummary[];

  interventionTypes: InterventionTypeWithCount[];

  isMobile: boolean = false;

  private _unsubscribe$ = new Subject<void>();

  constructor(
    private personManagementFunctionsService: PeopleManagementFunctionsService,
    public peopleManagementFunctionSummaryService: PeopleManagementFunctionSummaryService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 768px)'])
        .pipe(takeUntil(this._unsubscribe$))
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.peopleManagementFunctionSummaryService
      .getResults()
      .subscribe((result) => {
        this.summary = result.content;
        this.cd.markForCheck();
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.parameter) {
      this.entityFormControl.setValue(this.parameter.entityId, {
        emitEvent: false,
      });
      this.formControl.setValue(this.parameter.functionOperationalIds);
      this.subentitiesFormControl.setValue(this.parameter.subentities);
      this.initCount(this.parameter);
    }
  }

  ngOnDestroy(): void {
    _.each(this.subscriptions, (oneSubscription) =>
      oneSubscription.unsubscribe(),
    );

    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  initCount(parameter: PeopleManagementFunctionSummaryParameter) {
    if (parameter.functionOperationalIds.length !== 0) {
      this.peopleManagementFunctionSummaryService
        .getSummaryCount(parameter)
        .subscribe((result) => {
          this.summaryCount = result;
          this.initData();
          this.cd.markForCheck();
        });
    }
  }

  initData(): void {
    if (this.parameter.interventionTypes) {
      this.interventionTypes = _.chain(this.summaryCount)
        .groupBy('interventionType')
        .toPairs()
        .map((s) => {
          return new InterventionTypeWithCount({
            label: s[0],
            number: s[1].reduce(
              (sum, current) =>
                sum + current.countPro + current.countVol + current.countExt,
              0,
            ),
          });
        })
        .value();
    }
  }
}
