import { NgModule } from '@angular/core';
import { PeopleManagementFunctionsComponent } from './people-management-functions.component';
import { SharedModule } from '../../common/shared/shared.module';
import { EntityService } from '../../common/shared/services/entity.service';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { SimplePopupModule } from '../../common/modules/popup/simple-popup.module';
import { EpDatatableModule } from '@eportal/components';
import { PageTemplateModule } from '../../common/template/page-template/page-template.module';
import { PeopleManagementFunctionSummaryComponent } from './people-management-function-summary/people-management-function-summary.component';
import { PeopleManagementFunctionTableComponent } from './people-management-function-table/people-management-function-table.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { PeopleManagementFunctionTablePersonComponent } from './people-management-function-table/people-management-function-table-person/people-management-function-table-person.component';
import { PeopleManagementFunctionTableFunctionsComponent } from './people-management-function-table/people-management-function-table-functions/people-management-function-table-functions.component';
import { PeopleManagementFunctionTableResultComponent } from './people-management-function-table/people-management-function-table-result/people-management-function-table-result.component';
import { ScrollTableModule } from '@app/common/modules/scroll-table/scroll-table.module';
import { TabsListModule } from '@app/common/modules/tabs-list/tabs-list.module';
import { ScrollTableService } from '@app/common/modules/scroll-table/scroll-table.service';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { NgxTrimDirectiveModule } from 'ngx-trim-directive';
import { InputModule } from '@app/common/modules/input/input.module';
import { PeopleManagementFunctionSummaryDetailComponent } from './people-management-function-summary/people-management-function-summary-detail/people-management-function-summary-detail.component';
import { PaginationModule } from '@app/common/modules/pagination/pagination.module';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    DatatableModule,
    SimplePopupModule,
    EpDatatableModule,
    PageTemplateModule,
    MatExpansionModule,
    ScrollTableModule,
    TabsListModule,
    ReactiveFormsModule,
    FormModule,
    NgxTrimDirectiveModule,
    InputModule,
    PaginationModule,
    EntityFilterModule,
  ],
  providers: [
    EntityService,
    PeopleManagementFunctionsService,
    ScrollTableService,
    AssignmentService,
  ],
  declarations: [
    PeopleManagementFunctionsComponent,
    PeopleManagementFunctionSummaryComponent,
    PeopleManagementFunctionTableComponent,
    PeopleManagementFunctionTablePersonComponent,
    PeopleManagementFunctionTableFunctionsComponent,
    PeopleManagementFunctionTableResultComponent,
    PeopleManagementFunctionSummaryDetailComponent,
  ],
})
export class PeopleManagementFunctionsModule {}
