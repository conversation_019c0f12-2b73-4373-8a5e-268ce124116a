import { forkJoin as observableFork<PERSON>oin, Subject, Subscription } from 'rxjs';

import { first } from 'rxjs/operators';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { FieldGroupOption } from '../../common/modules/form-module/model/field-group-option.model';
import _ from 'lodash';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';
import { InterventionType } from '@app/model/intervention-type.model';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { FunctionOperational } from '@app/model/function-operational.model';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { Entity } from '@app/model/entity.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

import { PeopleManagementFunctionTableParameter } from '@app/people-management/functions/people-management-function-table/people-management-function-table-parameter';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { AssignmentStatus } from '@app/model/assignment-status.model';

@Component({
  selector: 'cgdis-portal-admin-people-management-functions',
  templateUrl: './people-management-functions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionsComponent implements OnInit, OnDestroy {
  private toggleVisibleForEntityTypes = ['NATIONAL', 'ZONE', 'GROUP'];

  selectedEntity: Entity;

  formControl = new UntypedFormControl();

  formControlToggle = new UntypedFormControl();

  interventionTypes: InterventionType[];

  functionOperationals: FunctionOperational[];
  status: AssignmentStatus;
  summaryTableParameter: PeopleManagementFunctionSummaryParameter;
  fireTableParameter: PeopleManagementFunctionTableParameter;
  otherTableParameter: PeopleManagementFunctionTableParameter;
  commandmentTableParameter: PeopleManagementFunctionTableParameter;
  ambulanceTableParameter: PeopleManagementFunctionTableParameter;
  gisTableParameter: PeopleManagementFunctionTableParameter;
  dmsTableParameter: PeopleManagementFunctionTableParameter;
  samuTableParameter: PeopleManagementFunctionTableParameter;

  /**
   * Entity accessible
   * Note: can be null depending on user's rights
   */
  entities: FieldGroupOption<string, Entity>[] = [];
  public primaryBaseModel: Entity;

  subscriptions: Subscription[] = [];

  loading = true;
  fire = false;
  other = false;
  commandment = false;
  gis = false;
  dms = false;
  samu = false;
  ambulance = false;
  hideToggle = true;
  subentities = false;

  tabCommandmentOpen = false;
  tabFireOpen = false;
  tabAmbulanceOpen = false;
  tabOtherOpen = false;
  tabGisOpen = false;
  tabDmsOpen = false;
  tabSamuOpen = false;
  tabSummaryOpen = false;

  filterConfigAllPersons = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  isMobile: boolean = false;

  private _unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private interventionTypesService: InterventionTypesService,
    private peopleManagementFunctionService: PeopleManagementFunctionsService,
    private assignmentService: AssignmentService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.loading = true;
  }

  public selectEntity(entity: Entity): void {
    this.loading = true;
    this.selectedEntity = entity;
    this.showOrHideAllpersonsToggle();
    this.interventionTypesService
      .getForEntity(entity.tecid, this.subentities)
      .subscribe((interventionTypes) => {
        this.interventionTypes = interventionTypes;
        this.cd.markForCheck();
        this.loadFunctionForEntity(entity.tecid, this.subentities);
        this.formControl.setValue(entity.tecid);
      });
    this.cd.markForCheck();
  }

  loadFunctionForEntity(tecid: number, subentities: boolean): void {
    this.tabSummaryOpen = false;
    observableForkJoin(
      this.peopleManagementFunctionService.getAllByEntityId(tecid, subentities),
      this.assignmentService.getAllStatuesForEntity(tecid, subentities, true),
    )
      .pipe(first())
      .subscribe((result: [FunctionOperational[], AssignmentStatus]) => {
        this.functionOperationals = result[0];
        this.status = result[1];
        this.initFunctionOperationalsData(this.functionOperationals);
        this.loading = false;
        this.tabSummaryOpen = true;
        this.cd.markForCheck();
      });
  }

  initFunctionOperationalsData(
    functionOperationals: FunctionOperational[],
  ): void {
    const fireFunctions = this.getFunctionByTypes(functionOperationals, 'fire');
    this.fire = !this.subentities && fireFunctions.length > 0;
    const commandmentFunctions = this.getFunctionByTypes(
      functionOperationals,
      'commandment',
    );
    this.commandment = !this.subentities && commandmentFunctions.length > 0;
    const otherFunctions = this.getFunctionByTypes(
      functionOperationals,
      'others',
    );
    this.other = !this.subentities && otherFunctions.length > 0;
    const ambulanceFunctions = this.getFunctionByTypes(
      functionOperationals,
      'ambulance',
    );
    this.ambulance = !this.subentities && ambulanceFunctions.length > 0;
    const gisFunctions = this.getFunctionByTypes(functionOperationals, 'gis');
    this.gis = !this.subentities && gisFunctions.length > 0;
    const dmsFunctions = this.getFunctionByTypes(functionOperationals, 'dms');
    this.dms = !this.subentities && dmsFunctions.length > 0;
    const samuFunctions = this.getFunctionByTypes(functionOperationals, 'samu');
    this.samu = !this.subentities && samuFunctions.length > 0;

    this.summaryTableParameter = {
      entityId: this.selectedEntity.tecid,
      pro: this.status.professional,
      vol: this.status.volunteer,
      ext: this.status.external,
      functionOperationalIds: functionOperationals.map(
        (functionOperational) => functionOperational.tecid,
      ),
      interventionTypes: this.interventionTypes,
      subentities: this.subentities,
    };
    this.fireTableParameter = this.buildTableParameter(fireFunctions);
    this.ambulanceTableParameter = this.buildTableParameter(ambulanceFunctions);
    this.otherTableParameter = this.buildTableParameter(otherFunctions);
    this.commandmentTableParameter =
      this.buildTableParameter(commandmentFunctions);
    this.gisTableParameter = this.buildTableParameter(gisFunctions);
    this.dmsTableParameter = this.buildTableParameter(dmsFunctions);
    this.samuTableParameter = this.buildTableParameter(samuFunctions);
  }

  private getFunctionByTypes(
    functionOperationals: FunctionOperational[],
    typeLabel: string,
  ): FunctionOperational[] {
    return _.filter(
      functionOperationals,
      (functionOperational: FunctionOperational) => {
        return functionOperational.interventionType.label === typeLabel;
      },
    );
  }

  private buildTableParameter(
    functions: FunctionOperational[],
  ): PeopleManagementFunctionTableParameter {
    return {
      entityId: this.selectedEntity.tecid,
      functionsOperationals: functions,
      functionOperationalIds: functions.map(
        (functionOperational) => functionOperational.tecid,
      ),
      subentities: this.subentities,
    };
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
    _.each(this.subscriptions, (oneSubscription) =>
      oneSubscription.unsubscribe(),
    );
  }

  showOrHideAllpersonsToggle() {
    if (
      this.toggleVisibleForEntityTypes.indexOf(this.selectedEntity.type) >= 0
    ) {
      if (this.hideToggle) {
        this.formControlToggle.setValue(false);
        this.subentities = false;
      }
      this.hideToggle = false;
    } else {
      this.hideToggle = true;
      this.formControlToggle.setValue(false);
      this.subentities = false;
    }
  }
}
