<ng-container class="people-management">
  <cgdis-portal-page-template>

    <cgdis-portal-page-header [titleKey]="'layout.navigation.menu.items.people_management.function'" [subtitleAlign]="true">
      <div page-header-subtitle>
        <cgdis-portal-entity-filter [entityPermission]="['ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW']"
                                    [underDisable]="false" [initPrimaryEntity]="true"
                                    (filterSelected)="selectEntity($event.entity)"></cgdis-portal-entity-filter>

      </div>
    </cgdis-portal-page-header>

    <cgdis-portal-panel>
      <div  class="localSpinner">
        <!-- Persons -->
        <ng-container *ngIf="selectedEntity && functionOperationals">
          <cgdis-portal-spinner [loading]="loading" ></cgdis-portal-spinner>
          <mat-accordion>

            <!-- Operational Contact Information -->
            <mat-expansion-panel class="accordion__group" [expanded]="true"  (closed)="tabSummaryOpen=false" (opened)="tabSummaryOpen=true">
              <mat-expansion-panel-header class="general-information-header" role="heading" collapsedHeight="48px" expandedHeight="48px">
                <mat-panel-title class="mb-0">
                  <span class="accordion__title" [translate]="'people_management.functions.summary'"></span>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div class="card-body" *ngIf="tabSummaryOpen">
                <div aria-hidden="false" role="region" class="accordion__panel">
                  <cgdis-portal-people-management-function-summary
                    [parameter]="summaryTableParameter"
                  ></cgdis-portal-people-management-function-summary>
                </div>
              </div>
            </mat-expansion-panel>

            <ng-container *ngIf="!subentities">
            <!-- Active assignments information -->
            <mat-expansion-panel *ngIf="commandment" class="accordion__group" [expanded]="false" (closed)="tabCommandmentOpen=false" (opened)="tabCommandmentOpen=true">
              <mat-expansion-panel-header class="general-information-header" role="heading">
                <mat-panel-title class="mb-0">
                  <span class="accordion__title" [translate]="'people_management.functions.commandment'"></span>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <ng-template matExpansionPanelContent>
                <div class="card-body" *ngIf="tabCommandmentOpen">
                  <cgdis-portal-people-management-function-table
                    [parameter]="commandmentTableParameter"
                  ></cgdis-portal-people-management-function-table>
                </div>
              </ng-template>
            </mat-expansion-panel>

            <!-- Operational Occupations information -->
            <mat-expansion-panel *ngIf="fire" class="accordion__group" [expanded]="false" (closed)="tabFireOpen=false" (opened)="tabFireOpen=true">
              <mat-expansion-panel-header class="general-information-header" role="heading">
                <mat-panel-title class="mb-0">
                  <span class="accordion__title" [translate]="'people_management.functions.fire'"></span>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <ng-template matExpansionPanelContent>
                <div class="card-body" *ngIf="tabFireOpen">
                  <cgdis-portal-people-management-function-table
                    [parameter]="fireTableParameter"
                  ></cgdis-portal-people-management-function-table>
                </div>
              </ng-template>
            </mat-expansion-panel>

            <!-- Managerial Occupations information -->
            <mat-expansion-panel *ngIf="ambulance" class="accordion__group" [expanded]="false" (closed)="tabAmbulanceOpen=false" (opened)="tabAmbulanceOpen=true">
              <mat-expansion-panel-header class="general-information-header" role="heading">
                <mat-panel-title class="mb-0">
                  <span class="accordion__title" [translate]="'people_management.functions.ambulance'"></span>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <ng-template matExpansionPanelContent>
                <div class="card-body" *ngIf="tabAmbulanceOpen">
                  <cgdis-portal-people-management-function-table
                    [parameter]="ambulanceTableParameter"
                  ></cgdis-portal-people-management-function-table>
                </div>
              </ng-template>
            </mat-expansion-panel>

              <mat-expansion-panel *ngIf="gis" class="accordion__group" [expanded]="false" (closed)="tabGisOpen=false" (opened)="tabGisOpen=true">
                <mat-expansion-panel-header class="general-information-header" role="heading">
                  <mat-panel-title class="mb-0">
                    <span class="accordion__title" [translate]="'people_management.functions.gis'"></span>
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <ng-template matExpansionPanelContent>
                  <div class="card-body" *ngIf="tabGisOpen">
                    <cgdis-portal-people-management-function-table
                      [parameter]="gisTableParameter"
                    ></cgdis-portal-people-management-function-table>
                  </div>
                </ng-template>
              </mat-expansion-panel>

              <mat-expansion-panel *ngIf="dms" class="accordion__group" [expanded]="false" (closed)="tabDmsOpen=false" (opened)="tabDmsOpen=true">
                <mat-expansion-panel-header class="general-information-header" role="heading">
                  <mat-panel-title class="mb-0">
                    <span class="accordion__title" [translate]="'people_management.functions.dms'"></span>
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <ng-template matExpansionPanelContent>
                  <div class="card-body" *ngIf="tabDmsOpen">
                    <cgdis-portal-people-management-function-table
                      [parameter]="dmsTableParameter"
                    ></cgdis-portal-people-management-function-table>
                  </div>
                </ng-template>
              </mat-expansion-panel>

              <mat-expansion-panel *ngIf="samu" class="accordion__group" [expanded]="false" (closed)="tabSamuOpen=false" (opened)="tabSamuOpen=true">
                <mat-expansion-panel-header class="general-information-header" role="heading">
                  <mat-panel-title class="mb-0">
                    <span class="accordion__title" [translate]="'people_management.functions.samu'"></span>
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <ng-template matExpansionPanelContent>
                  <div class="card-body" *ngIf="tabSamuOpen">
                    <cgdis-portal-people-management-function-table
                      [parameter]="samuTableParameter"
                    ></cgdis-portal-people-management-function-table>
                  </div>
                </ng-template>
              </mat-expansion-panel>


            <mat-expansion-panel *ngIf="other" class="accordion__group" [expanded]="false" (closed)="tabOtherOpen=false" (opened)="tabOtherOpen=true">
              <mat-expansion-panel-header class="general-information-header" role="heading">
                <mat-panel-title class="mb-0">
                  <span class="accordion__title" [translate]="'people_management.functions.other'"></span>
                </mat-panel-title>
              </mat-expansion-panel-header>
              <ng-template matExpansionPanelContent>
                <div class="card-body" *ngIf="tabOtherOpen">
                  <cgdis-portal-people-management-function-table
                    [parameter]="otherTableParameter"
                  ></cgdis-portal-people-management-function-table>
                </div>
              </ng-template>
            </mat-expansion-panel>

            </ng-container>
          </mat-accordion>


        </ng-container>
      </div>
    </cgdis-portal-panel>

  </cgdis-portal-page-template>
</ng-container>



