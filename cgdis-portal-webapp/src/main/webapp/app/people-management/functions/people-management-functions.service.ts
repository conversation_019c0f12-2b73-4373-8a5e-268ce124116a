import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { Entity } from '@app/model/entity.model';
import { FunctionOperational } from '@app/model/function-operational.model';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { PositionTemplateVersion } from '@app/model/position-template-version.model';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';
import { AssignmentWithFunctions } from '@app/model/assignment.model';

@Injectable()
export class PeopleManagementFunctionsService extends DefaultFormService<
  AssgnmentFunctionOperationalForm,
  AssignmentWithFunctions
> {
  /**
   * Based url to access all entities
   * @type {string}
   */
  private baseUrl = ['admin', 'function-operational'];

  private basePersonFunctionUrl: string[] = ['person-function-operational'];

  /**
   * Based url to access one entity by id
   * @type {string}
   */
  private baseItemUrl = this.baseUrl;

  constructor(
    toastService: ToastService,
    private restService: RestService,
    formErrorService: FormErrorService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    super(
      toastService,
      'admin.service_plan_model.version.edit.success',
      formErrorService,
    );
  }

  submit(
    form: AssgnmentFunctionOperationalForm,
  ): Observable<AssignmentWithFunctions> {
    return null;
  }

  submitSuccess(result: PositionTemplateVersion): void {}

  submitError(formError: FormError): void {}

  /**
   * Get all entities
   * @return {Observable<SearchResult<Entity>>}
   */
  getAllByEntityId(
    entityId: number,
    subentities: boolean,
  ): Observable<FunctionOperational[]> {
    const restResource = this.restService.all(
      ...this.baseUrl,
      'listByEntityId',
      String(entityId),
    );
    return restResource.get({ subentities: subentities }).pipe(
      map((value) => {
        return value;
      }),
    );
  }

  mapByEntity(
    personFunctionOperational: AssgnmentFunctionOperationalForm,
  ): Observable<boolean> {
    const restResource = this.restService.all(
      ...this.basePersonFunctionUrl,
      'mapByEntity',
      String(personFunctionOperational.functionId),
      String(personFunctionOperational.assignmentId),
    );
    return restResource.put(personFunctionOperational).pipe(
      map((value) => {
        return value;
      }),
    );
  }

  hasFunctionOperational(entityId: number): Observable<boolean> {
    const restResource = this.restService.one(
      ...this.baseUrl,
      'hasFunctionOperational',
      String(entityId),
    );
    return restResource.get().pipe(
      map((value: boolean) => {
        return value;
      }),
    );
  }
}
