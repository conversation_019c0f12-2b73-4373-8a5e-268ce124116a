<div class="person-detail">
  <cgdis-portal-spinner [loading]="showSpinner"></cgdis-portal-spinner>


  <ul *ngIf="data" style="list-style-type: none;" class="p-3 p-sm-0 px-sm-5">
    <li>
      <div class=" information-message">
        <div [translate]="'people_management.functions.update.information_message'"></div>
      </div>
    </li>
    <ng-container *ngFor="let oneData of data">


      <li *ngFor="let func of oneData.personOperationalFunctions; index as $index" style="padding-left: 6%;">

        <!-- CREATE FORM -->
        <ng-container *ngIf="isCreationForm(oneData.assignmentFunctions,func.personOperationalFunctionId); else updateForm">
          <cgdis-portal-person-detail-create-form
            (onFormSubmit)="onFormSubmit()"
            [functionLabel]="func.label"
            [volunteer]="oneData.volunteer"
            [personOperationalFunctionsId]="func.personOperationalFunctionId"
            [assignmentId]="oneData.assignmentId"
            [formId]="'people-management-functions-' + $index">
          </cgdis-portal-person-detail-create-form>
        </ng-container>

        <!-- UPDATE FORM -->
        <ng-template #updateForm>
          <cgdis-portal-person-detail-update-form
            [functionLabel]="func.label"
            [personOperationalFunctionsId]="func.personOperationalFunctionId"
            [assignmentId]="oneData.assignmentId"
            [volunteer]="oneData.volunteer"
            [assignmentFunctionId]="getAssignmentFunction(oneData.assignmentFunctions,func.personOperationalFunctionId)"
            [initialGranted]="getGranted(oneData.assignmentFunctions,func.personOperationalFunctionId)"
            [initialRating]="getRating(oneData.assignmentFunctions,func.personOperationalFunctionId)"
            [formId]="'people-management-functions-' + $index">
          </cgdis-portal-person-detail-update-form>
        </ng-template>
      </li>

    </ng-container>
  </ul>

</div>
