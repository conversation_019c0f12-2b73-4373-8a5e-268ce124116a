<cgdis-portal-scroll-table-top>
  <div class="planner__header-jobs">
    <div class="filter">
      <label [translate]="'people_management.functions.table.filterName'"></label>
      <div class="field">
        <input type="text"
               class="datatable-input"
               cgdisPortalEllipsis
               [ngModel]="nameSearch"
               (ngModelChange)="inputCompleted($event)"
               [oneLine]="true"/>
        <span>
          <button *ngIf="nameSearch != null && nameSearch.length > 0" class="close-icon" (click)="clear()">
            <i class="fa fa-times"></i>
          </button>
          <button class="search-icon">
            <i class="fa fa-search"></i>
          </button>
        </span>
      </div>
    </div>

    <ul class="planner__people-list -placeholder" style="margin-left: 5px">
      <li *ngFor="let function of functionOperationals; trackBy: trackById"
          [ngClass]="[ 'planner__people', 'functions_people','-degraded' ]">
        <div style="text-overflow: ellipsis; overflow: hidden;">{{function.portalLabel}}</div>
      </li>
    </ul>
  </div>
</cgdis-portal-scroll-table-top>
