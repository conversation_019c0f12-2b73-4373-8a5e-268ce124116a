<cgdis-portal-form [formId]="'people-management-function'" [hideFormActions]="true"
                   [hideSpinner]="true"
><!-- List of available positions  -->

<cgdis-portal-people-management-function-table-functions *hideItBootstrap="['sm','xs','md']" [loading]="loading"
                                                         [functionOperationals]="functionOperationals"
(nameEmitter)="filterByName($event)">
</cgdis-portal-people-management-function-table-functions>


<div class="scrollable-content" [ngClass]="scrollableContentClasses">
  <cgdis-portal-scroll-table-bottom [goToScrollPosition]="loading"
                                    [resetScroll]="resetScroll">

    <div *ngIf="!rows || rows.length === 0 " class="service-planner__no-data" [translate]="'service_plan.no_data'"></div>

    <ng-container>
      <div style="display: flex;">
        <cgdis-portal-tabs-list class="planner-timeslots ng-scrollbar__box-sizing__content" style="width: 16rem;">
          <cgdis-portal-tabs-list-item
            *ngFor="let row of rows;"
            [tabClasses]="['service-planner__schedule-selector','p-0','schedule', '-complete']">
            <span
              style="padding: 1.5rem !important; align-items: center; height: 3rem !important; color: black; font-size: 1.3rem; overflow: hidden; text-overflow: ellipsis;">
              {{row.person.firstName}} {{row.person.lastName}} {{row.person.cgdisRegistrationNumber}} {{row.type}}
            </span>

          </cgdis-portal-tabs-list-item>
        </cgdis-portal-tabs-list>


        <div style="margin-left: 1.5rem">
          <div
            *ngFor="let row of rows; trackBy: trackById; index as $index; "
            id="rows-{{$index}}-content"
            role="tabpanel"
            [attr.aria-labelledby]="'rows-'+$index+'-tab'">


            <ul class="planner__people-list" #scheduleRow>
              <ng-container
                *ngFor="let function of functionOperationals; trackBy: trackByPosition; index as currentIndex; last as $last;">
                <ul *responsive="{bootstrap:['sm','xs','md']}" class="planner__people-list -placeholder">
                  <li class="functions_people" [ngClass]="['planner__people', 'functions_people' ,'-degraded']">

                </ul>

                  <li class="planner__people functions_people" style="width: 5rem; min-height: unset !important; flex-basis: auto !important; height: 3rem !important; display: block">
                        <ng-container *ngIf="!loading">
                          <cgdis-portal-checkbox-field
                            [name]="'checkbox'+currentIndex"
                            [fieldReadonly]="!canUpdate"
                            [initialValue]="getValue(row, function)"
                            [possibleValues]="checkPossibleValue"
                            [slider]="false"
                            class="functions_checkbox -slide"
                            (onCheck)="changeFunctionOperationalForEntity(row, function, $event)"
                          ></cgdis-portal-checkbox-field>
                        </ng-container>


                  </li>

              </ng-container>
            </ul>
          </div>
        </div>
      </div>
    </ng-container>
  </cgdis-portal-scroll-table-bottom>
  <!--  </ng-scrollbar>-->
</div>

</cgdis-portal-form>
<!-- Show header without positions -->
<ng-template #showPlaceholder></ng-template>
