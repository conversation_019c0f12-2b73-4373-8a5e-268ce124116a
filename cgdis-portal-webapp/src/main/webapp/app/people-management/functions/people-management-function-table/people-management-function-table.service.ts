import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { IOneRestResource, IPage, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { ServicePlanModel } from '@app/model/service-plan-model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { AssignmentWithFunctions } from '@app/model/assignment.model';

@Injectable()
export class PeopleManagementFunctionTableService extends CgdisDatatableService<AssignmentWithFunctions> {
  private currentEntityId: number;

  /**
   * Based url to access one intervention type by id
   * @type {string}
   */
  baseAdminUrl: string[] = ['person-function-operational'];
  baseUrlServicePlan: string[] = ['service-plan'];

  // constructor(private _restService: RestService) {
  //
  // }

  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
  }

  loadDatas(entityId: number, functionOperationalsIds: number[]) {
    if (this.currentEntityId == undefined) {
      this.currentEntityId = entityId;
      super.initDataResourceList(
        this.restService.all<AssignmentWithFunctions>(
          ...this.baseAdminUrl,
          'byEntity',
          String(entityId),
        ),
      );
      super.setCanStartSearching();
      this.search();
    } else {
      super.setDataResource(
        this.restService.all<AssignmentWithFunctions>(
          ...this.baseAdminUrl,
          'byEntity',
          String(entityId),
        ),
      );
    }
  }

  /**
   * Get one service plan models
   * @param {number} tecid
   * @return {ServicePlanModel}
   */
  getAll(
    entityId: number,
    functionOperationalsIds: number[],
  ): Observable<AssignmentWithFunctions[]> {
    return this.restService
      .one<AssignmentWithFunctions[]>(
        ...this.baseAdminUrl,
        'byEntity',
        String(entityId),
      )
      .get({ ids: functionOperationalsIds })
      .pipe(
        map((value: AssignmentWithFunctions[]) => {
          return value;
        }),
      );
  }

  searchByPersonName(
    entityId: number,
    functionOperationalsIds: number[],
    searchQuery?: string,
  ): Observable<IPage<AssignmentWithFunctions>> {
    const resource: IOneRestResource<IPage<AssignmentWithFunctions>> =
      this.restService.one(...this.baseAdminUrl, 'byEntity', String(entityId));
    let map: Record<string, number[] | string> = {
      ids: functionOperationalsIds,
    };
    if (searchQuery !== undefined && searchQuery !== null) {
      map['search'] = searchQuery;
    }
    return resource.get(map);
  }
}
