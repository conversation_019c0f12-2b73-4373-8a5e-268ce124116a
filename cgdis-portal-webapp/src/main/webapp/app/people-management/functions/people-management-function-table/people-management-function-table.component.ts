import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';
import { PeopleManagementFunctionTableService } from '@app/people-management/functions/people-management-function-table/people-management-function-table.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { IPage } from '@eportal/core';
import { UntypedFormControl } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { PeopleManagementFunctionTableParameter } from '@app/people-management/functions/people-management-function-table/people-management-function-table-parameter';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { AssignmentWithFunctions } from '@app/model/assignment.model';

@Component({
  selector: 'cgdis-portal-people-management-function-table',
  templateUrl: './people-management-function-table.component.html',
  styles: [],
  providers: [PeopleManagementFunctionTableService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() parameter: PeopleManagementFunctionTableParameter;

  public loading = true;
  rows: AssignmentWithFunctions[] = [];
  page: IPage<AssignmentWithFunctions>;
  subscriptions: Subscription[] = [];
  canUpdate = false;

  isMobile: boolean = false;
  private idControl = new UntypedFormControl();
  private nameControl = new UntypedFormControl();
  private subentitiesControl = new UntypedFormControl();

  private _unsubscribe$ = new Subject<void>();

  constructor(
    private cdRef: ChangeDetectorRef,
    private userService: ConnectedUserService,
    private breakpointObserver: BreakpointObserver,
    public personFunctionOperationalTableService: PeopleManagementFunctionTableService,
    private peopleManagementFunctionService: PeopleManagementFunctionsService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cdRef.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'ids',
      this.idControl,
    );
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'subentities',
      this.subentitiesControl,
    );
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'name',
      this.nameControl,
    );
    this.canUpdate = this.userService.hasAnyRoles([
      'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE',
    ]);
    this.personFunctionOperationalTableService
      .isLoading()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((loading: boolean) => {
        this.setLoading(loading);
        this.cdRef.markForCheck();
      });
    this.cdRef.markForCheck();
    this.subscriptions.push(
      // this.personFunctionOperationalTableService.searchByPersonName(this.entityId, this.functionOperationalIds, this.name).subscribe((result: IPage<PersonFunctionOperationalMapping>) => {

      this.personFunctionOperationalTableService
        .getResults()
        .subscribe((result: IPage<AssignmentWithFunctions>) => {
          this.page = result;
          this.rows = result.content;
          this.cdRef.markForCheck();
          this.stopLoading();
        }),
    );
  }

  loadDatas(): void {
    // this.loading = true;
    if (this.parameter) {
      this.idControl.setValue(this.parameter.functionOperationalIds, {
        emitEvent: false,
      });
      this.subentitiesControl.setValue(this.parameter.subentities, {
        emitEvent: false,
      });
      setTimeout(() => {
        console.error(
          'loadDatas',
          this.parameter.entityId,
          this.idControl.value,
        );
        this.personFunctionOperationalTableService.loadDatas(
          this.parameter.entityId,
          this.parameter.functionOperationalIds,
        );
      });
    }
  }

  stopLoading() {
    setTimeout(() => {
      this.loading = false;
      this.cdRef.markForCheck();
    });
  }

  ngOnDestroy(): void {
    console.error('destory FunctionTable');
    // Unsubscribe to all subscriptions
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
    for (let subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
  }

  hasDatas() {
    return this.rows != undefined && this.rows.length > 0;
  }

  public setLoading(loading: boolean): void {
    if (loading === false) {
      this.stopLoading();
    } else {
      this.loading = loading;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.parameter) {
      this.loadDatas();
    }
  }

  updateFunctionOperational(
    personFunctionOperation: AssgnmentFunctionOperationalForm,
  ) {
    this.peopleManagementFunctionService
      .mapByEntity(personFunctionOperation)
      .subscribe((result: boolean) => {
        this.loadDatas();
      });
  }

  filterByName(name: string) {
    if (this.nameControl.value != name) {
      this.nameControl.setValue(name == '' ? undefined : name, {
        emitEvent: false,
      });
      this.loadDatas();
    }
  }

  changePage(newPage: IPage<AssignmentWithFunctions>) {
    this.personFunctionOperationalTableService.updatePage(newPage);
  }
}
