import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { ServicePlanPosition } from '@app/model/service-plan-position.model';
import { FunctionOperational } from '@app/model/function-operational.model';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-people-management-function-table-functions',
  templateUrl: './people-management-function-table-functions.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableFunctionsComponent
  implements OnInit, OnDestroy
{
  /**
   * List of all service plan positions
   */
  @Input() functionOperationals: FunctionOperational[];

  @Input() loading = false;

  @Output() nameEmitter = new EventEmitter<string>();

  public nameSearch: string;

  private nameSubject = new Subject<string>();

  private _unsubscribe$ = new Subject<void>();

  constructor(private cd: ChangeDetectorRef) {}

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  ngOnInit(): void {
    this.nameSubject
      .pipe(takeUntil(this._unsubscribe$), debounceTime(500))
      .subscribe({
        next: (name: string) => {
          this.nameEmitter.emit(name);
        },
      });
  }

  public trackById(index: number, item: ServicePlanPosition): number {
    return item.tecid;
  }

  inputCompleted($event: string) {
    console.error('inputCompleted', $event);
    this.nameSearch = $event;
    this.cd.markForCheck();
    if (this.nameSearch && this.nameSearch.length >= 1) {
      this.nameSubject.next(this.nameSearch);
    } else {
      this.clear();
    }
  }

  clear() {
    this.nameSearch = '';
    console.error('clear', this.nameSearch);
    this.nameSubject.next(undefined);
    this.cd.markForCheck();
  }
}
