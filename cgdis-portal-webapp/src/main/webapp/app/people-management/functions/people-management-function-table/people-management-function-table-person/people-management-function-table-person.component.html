<div >
  <div class="schedule -actions">

  </div>
<div class="scrollable-content" [ngClass]="scrollableContentClasses">
  <ng-container *ngIf="rows" >
    <cgdis-portal-scroll-table-left class="override-scrollbar-width"   [ngClass]="{'hiddenMobileScrollBar' : isTabletAndMobile()}">

      <cgdis-portal-tabs-list class="planner-timeslots">
        <cgdis-portal-tabs-list-item
          *ngFor="let row of rows;  index as $index; let last = last; let first = first"
          [tabClasses]="['service-planner__schedule-selector','p-0','schedule', '-complete']"
          [tabId]="'schedule-'+index+'-tab'"
          [ngStyle]="getRowStyle($index)"
        >
          <span>
          {{row.person.firstName}} {{row.person.lastName}} {{row.person.cgdisRegistrationNumber}}
      </span>

        </cgdis-portal-tabs-list-item>
      </cgdis-portal-tabs-list>
    </cgdis-portal-scroll-table-left>

  </ng-container>
</div>
</div>

