import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  ViewChildren,
} from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { ScheduleRow } from '@app/operational/service-plan/schedule/schedule-row.model';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';
import { FunctionOperational } from '@app/model/function-operational.model';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { SimpleYesNoPopupComponent } from '@app/common/modules/popup/yes-no/simple-yes-no-popup.component';
import { SimpleYesNoPopupData } from '@app/common/modules/popup/yes-no/simple-yes-no-popup-data';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { ScrollTableService } from '@app/common/modules/scroll-table/scroll-table.service';
import { AssignmentWithFunctions } from '@app/model/assignment.model';

@Component({
  selector: 'cgdis-portal-people-management-function-table-result',
  templateUrl: './people-management-function-table-result.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    PeopleManagementFunctionsService,
    ScrollTableService,
    {
      provide: FORM_SERVICE,
      useExisting: PeopleManagementFunctionsService,
    },
  ],
})
export class PeopleManagementFunctionTableResultComponent
  implements AfterViewInit, OnDestroy, OnChanges, OnInit
{
  @Input() rows: AssignmentWithFunctions[];

  @Input() loading = false;

  /**
   * List of all service plan positions
   */
  @Input() functionOperationals: FunctionOperational[];

  @Input() refresh: boolean;

  @Input() canUpdate: boolean;

  @Input() entityId: number;

  /**
   * Call an update has been done on position
   * @type {EventEmitter<void>}
   */
  @Output() onFunctionOperationalUpdated = new EventEmitter<any>();

  @Output() nameEmitter = new EventEmitter<string>();

  scrollableContentClasses = {};

  @ViewChildren('scheduleRow') components: QueryList<any>;
  public rowSizes: number[] = [];

  checkPossibleValue: FieldOption<boolean>[];

  subscriptions: Subscription[] = [];
  resetScroll = false;

  isMobile: boolean = false;

  constructor(
    private popupService: SimplePopupService,
    private translateService: TranslateService,
    private changeDetectorRef: ChangeDetectorRef,
    public service: PeopleManagementFunctionsService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.checkPossibleValue = [
      new FieldOption<boolean>({
        label: '',
        value: true,
      }),
    ];
    this.changeDetectorRef.markForCheck();
  }

  ngOnChanges(_changes: SimpleChanges) {
    this.loading = true;
    this.changeDetectorRef.markForCheck();
    this.loading = false;
    this.changeDetectorRef.markForCheck();
  }

  ngAfterViewInit() {
    this.subscriptions.push(
      this.components.changes.subscribe(() => {
        this.rowSizes = [];
        setTimeout(() => {
          this.components.forEach((component) =>
            this.rowSizes.push(component.nativeElement.clientHeight),
          );
          this.cd.markForCheck();
        }, 0);
      }),
    );
  }

  ngOnDestroy(): void {
    _.forEach(this.subscriptions, function (s: Subscription) {
      s.unsubscribe();
    });
  }

  /**
   * Retrieve all prestations persons for a specific position and time slot
   * @param personFunctionOperational
   * @param {ScheduleRow} row: the time slot
   * @returns {PrestationPerson[]}: all persons
   */
  getValue(
    rows: AssignmentWithFunctions,
    functionOperational: FunctionOperational,
  ): boolean {
    if (rows.functions != undefined) {
      let personFunctionOpertaionalEntity = _.filter(
        rows.functions,
        (p) => p.functionOperational.tecid === functionOperational.tecid,
      );
      return personFunctionOpertaionalEntity.length > 0;
    } else {
      return false;
    }
  }

  hasDatas() {
    return this.rows != undefined && this.rows.length > 0;
  }

  public trackById(index: number, item: AssignmentWithFunctions): number {
    return item.person.personId;
  }

  public trackByPosition(index: number, item: FunctionOperational): number {
    return item.tecid;
  }

  public changeFunctionOperationalForEntity(
    rows: AssignmentWithFunctions,
    functionOperational: FunctionOperational,
    _event: any,
  ): void {
    if (this.getValue(rows, functionOperational)) {
      let popupDialog = this.popupService.open(SimpleYesNoPopupComponent, {
        data: new SimpleYesNoPopupData({
          title: 'people_management.functions.deletion',
          message: 'people_management.functions.deletion_message',
          messageHtml: true,
          onYes: () => {
            return new Observable((subscriber) => {
              this.emitUpdateFunctionOperational(
                rows.tecid,
                functionOperational.tecid,
                false,
              );
              subscriber.next();
            });
          },
          onNo: () => {
            return new Observable((subscriber) => {
              this.service.resetForm();
              subscriber.next();
            });
          },
        }),

        panelClass: this.isMobile ? 'simple-popup-mobile' : ['simple-popup'],
      });
      popupDialog.afterOpened().subscribe(() => {
        document.body.style.overflow = 'hidden';
      });

      popupDialog.afterClosed().subscribe(() => {
        document.body.style.overflow = null;
      });
    } else {
      this.emitUpdateFunctionOperational(
        rows.tecid,
        functionOperational.tecid,
        true,
      );
    }
  }

  public emitUpdateFunctionOperational(
    assignmentId: number,
    functionId: number,
    event: boolean,
  ): void {
    this.onFunctionOperationalUpdated.emit(
      new AssgnmentFunctionOperationalForm({
        assignmentId: assignmentId,
        functionId: functionId,
        value: event,
      }),
    );
    this.changeDetectorRef.markForCheck();
  }

  filterByName(name: string) {
    this.nameEmitter.emit(name);
  }
}
