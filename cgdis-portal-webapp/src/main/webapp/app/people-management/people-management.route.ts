import { Routes } from '@angular/router';
import { PeopleManagementComponent } from './people-management.component';
import { RoleGuard } from '../security/guards/role.guard';
import { PeopleManagementListComponent } from './list/people-management-list.component';
import { PeopleManagementFunctionsComponent } from '@app/people-management/functions/people-management-functions.component';

export const PEOPLE_MANAGEMENT_ROUTE: Routes = [
  {
    path: '',
    component: PeopleManagementComponent,
    canActivate: [RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_USER_MANAGEMENT'],
    },
    children: [
      {
        path: 'functions',
        component: PeopleManagementFunctionsComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW'],
        },
      },
      {
        path: 'list',
        component: PeopleManagementListComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW'],
        },
      },
    ],
  },
];
