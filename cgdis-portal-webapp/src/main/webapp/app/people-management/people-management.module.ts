import { SharedModule } from '../common/shared/shared.module';
import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { PEOPLE_MANAGEMENT_ROUTE } from './people-management.route';
import { PeopleManagementComponent } from './people-management.component';
import { PeopleManagementFunctionsModule } from './functions/people-management-functions.module';
import { PeopleManagementListModule } from './list/people-management-list.module';
import { PeopleManagementListExportRangeComponent } from './exports/people-management-list-export-range.component';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { FormModule } from '@app/common/modules/form-module/form.module';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(PEOPLE_MANAGEMENT_ROUTE),
    PeopleManagementFunctionsModule,
    PeopleManagementListModule,
    SimplePopupModule,
    FormModule,
  ],
  declarations: [
    PeopleManagementComponent,
    PeopleManagementListExportRangeComponent,
  ],
})
export class PeopleManagementModule {}
