import { NgModule } from '@angular/core';
import { SharedModule } from '../../../common/shared/shared.module';
import { PageTemplateModule } from '../../../common/template/page-template/page-template.module';
import { PersonDetailEditComponent } from './person-detail-edit.component';
import { PersonDetailEditService } from './person-detail-edit.service';
import { OperationalContactModule } from '../../../general-information/operational-contact/operational-contact.module';
import { GeneralContactModule } from '../../../general-information/general-contact/general-contact.module';
import { GeneralInformationModule } from '../../../general-information/general-information.module';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    OperationalContactModule,
    GeneralContactModule,
    GeneralInformationModule,
  ],
  providers: [PersonDetailEditService],
  declarations: [PersonDetailEditComponent],
  exports: [PersonDetailEditComponent],
})
export class PersonDetailEditModule {}
