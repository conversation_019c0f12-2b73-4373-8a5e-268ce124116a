<cgdis-portal-spinner [loading]="showLoading" [fullScreen]="true"></cgdis-portal-spinner>
<ng-container class="people-management">
  <cgdis-portal-page-template>

    <cgdis-portal-page-header [titleKey]="'layout.navigation.menu.items.people_management.list.title'" [subtitleAlign]="true">
      <div page-header-subtitle class="flex-row" >
        <!-- Entity filter -->
        <cgdis-portal-entity-filter [entityPermission]="['ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW']" [initPrimaryEntity]="true" (filterSelected)="setFilter($event)"></cgdis-portal-entity-filter>

        <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
          <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
          <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
        </cgdis-portal-button-link>
      </div>
      <div page-header-icon
           class="flex-row flexbox justify-content-end people-management-list-header-icons float-right">
        <cgdis-portal-dropdown-button *ngIf="this.selectedEntity != undefined &&  isNotNational && canExportPrestation"
                                      id="preferences-export-prestations-button"
                                      [ngClass]="['preferences-export-prestations']"
                                      [menuId]="exportMenuId"
                                      [tooltip]="'layout.navigation.profile.mypreferences.export-prestations'"
                                      [icon]="'icon-print'">
          <li (click)="exportPreviousFirstSemester()">
            <a [translate]="'layout.navigation.profile.mypreferences.export-prestations-first-semester'"
               [translateParams]="{year: this.currentYear - 1}"></a>
          </li>
          <li (click)="exportPreviousSemester()">
            <a [translate]="'layout.navigation.profile.mypreferences.export-prestations-second-semester'"
               [translateParams]="{year: this.currentYear - 1}"></a>
          </li>
          <li (click)="exportFirstSemester()">
            <a [translate]="'layout.navigation.profile.mypreferences.export-prestations-first-semester'"
               [translateParams]="{year: this.currentYear}"></a>
          </li>
          <li (click)="exportSecondSemester()" *ngIf="secondSemester">
            <a [translate]="'layout.navigation.profile.mypreferences.export-prestations-second-semester'"
               [translateParams]="{year: this.currentYear}"></a>
          </li>
          <hr />
          <li (click)="exportRange()">
            <a [translate]="'layout.navigation.profile.mypreferences.export-prestations-range'"
               [translateParams]="{year: this.currentYear}"></a>
          </li>
        </cgdis-portal-dropdown-button>
        <cgdis-portal-dropdown-button
          *ngIf="this.selectedEntity != undefined && this.selectedEntity.tecid !=null &&  isNotNational && exportDownloadButtonDisplayed"
          id="preferences-export-download-button"
          [ngClass]="['preferences-export-persons']"
          [menuId]="exportMenuPersonsId"
          [tooltip]="'tooltip.export.csv'"
          [icon]="'icon-download'">
          <li (click)="exportCSV()">
            <a [translate]="'layout.navigation.profile.mypreferences.export-person-full'"></a>
          </li>
          <li (click)="exportCSVWithTechnical(true)" *ngIf="hasTechnicalAssignment">
            <a [translate]="'layout.navigation.profile.mypreferences.export-person-technical'"></a>
          </li>
          <li (click)="exportCSVWithTechnical(false)" *ngIf="hasTechnicalAssignment">
            <a [translate]="'layout.navigation.profile.mypreferences.export-person-notechnical'"></a>
          </li>
        </cgdis-portal-dropdown-button>
      </div>


    </cgdis-portal-page-header>

    <div *ngIf="!isMobile && hasTechnicalAssignment"  class="row">
      <div class="col-md-12" >
        <cgdis-portal-datatable-toggle-filter
          [labelKey]="'people_management.functions.technical'"
          [filterName]="'withTechnical'"
          [filterConfig]="filterConfigAllPersons"
          [datatableService]="peopleManagementListService"
          [customFormControl]="formControlTechnicalToggle"
          (onValueChanged)="updateFilter(false)"

        ></cgdis-portal-datatable-toggle-filter>
      </div>
    </div>

    <div *ngIf="!isMobile" class="status-filter row">
      <div *ngIf="!isMobile" class="col-md-6">
        <label [translate]="'people_management.functions.availability'"></label>
        <cgdis-portal-datatable-select-filter [possibleValues]="availabilities"
                                              [filterName]="'availability'"
                                              [flexGrow]="2"
                                              [datatableService]="peopleManagementListService"
                                              [allowClear]="true"
                                              [customFormControl]="formControlAvailability"
                                              [multiple]="true"
        ></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-6">
        <label [translate]="'people_management.functions.status'"></label>
        <cgdis-portal-datatable-select-filter
          [possibleValues]="statuses"
          [filterName]="'status'"
          [flexGrow]="2"
          [datatableService]="peopleManagementListService"
          [allowClear]="true"
          [multiple]="true"
          [customFormControl]="formControlStatus"
        ></cgdis-portal-datatable-select-filter>
      </div>
    </div>



    <cgdis-portal-panel *ngIf="selectedEntity != undefined; else noEntities">

      <div  class="localSpinner entity-list">
        <!-- Persons -->
        <ng-container>

          <ng-container *ngIf="isMobile">
            <div class="row search-filter" [hidden]="!showFilter">
              <div class="col-md-2">
                <label class="form-label" [translate]="'people_management.functions.firstname'"></label>
                <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()"
                                                    [filterName]="'firstName'"
                                                    [hidden]="!showFilter"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleManagementListService"></cgdis-portal-datatable-text-filter>
              </div>
              <div class="col-md-2">
                <label class="form-label" [translate]="'people_management.functions.lastname'"></label>
                <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()"
                                                    [filterName]="'lastName'"
                                                    [hidden]="!showFilter"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleManagementListService"></cgdis-portal-datatable-text-filter>
              </div>
              <div class="col-md-2">
                <label class="form-label" [translate]="'people_management.functions.cgdisregistrationnumber'"></label>
                <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()"
                                                    [filterName]="'cgdisRegistrationNumber'"
                                                    [placeholder]="''"
                                                    [hidden]="!showFilter"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleManagementListService"></cgdis-portal-datatable-text-filter>
              </div>
              <cgdis-portal-datatable-select-filter
                [hidden]="true"
                [possibleValues]="statuses"
                [filterName]="'status'"
                [flexGrow]="2"
                [datatableService]="peopleManagementListService"
                [allowClear]="true"
                [multiple]="true"
                [customFormControl]="formControlStatus"
              ></cgdis-portal-datatable-select-filter>
            </div>
          </ng-container>

          <cgdis-portal-cgdisdatatable
            [datatableService]="peopleManagementListService"
            [id]="'member-list'"
            [sorts]="[{dir:'asc',prop:'lastName'}]"
            [class]="'entity__table'"
            >

            <ng-template #template let-row="row">
              <div class="person-detail-row" *ngIf="selectedEntity && row.personId">
                <cgdis-portal-person-detail-edit [personId]="row.personId"></cgdis-portal-person-detail-edit>
              </div>
            </ng-template>

            <cgdis-portal-datatable-toggle-filter [hidden]="true"
              [filterName]="'allPersonsUnderEntity'"
              [filterConfig]="filterConfigAllPersons"
              [datatableService]="peopleManagementListService"
              [customFormControl]="formControlToggle"

            ></cgdis-portal-datatable-toggle-filter>

            <!-- Filter for entityId -->
            <cgdis-portal-datatable-number-filter [hidden]="true"
                                                  [filterConfig]="filterConfigEntityEqual"
                                                  [filterName]="'entityId'"
                                                  [customFormControl]="formControl"
                                                  [datatableService]="peopleManagementListService"></cgdis-portal-datatable-number-filter>

            <!-- first name -->
            <ep-datatable-column [columnName]="'firstName'" [flexGrow]="3">
              <ng-template epDatatableHeader>
                <span [translate]="'people_management.functions.firstname'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
                {{context.value | defaultValue:'-'}}
              </ng-template>

              <ng-template epDatatableFilter>
                <cgdis-portal-datatable-text-filter [filterName]="'firstName'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleManagementListService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- last name -->
            <ep-datatable-column [columnName]="'lastName'" [flexGrow]="3">
              <ng-template epDatatableHeader>
                <span [translate]="'people_management.functions.lastname'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
                {{context.value | defaultValue:'-'}}
              </ng-template>

              <ng-template epDatatableFilter>
                <cgdis-portal-datatable-text-filter [filterName]="'lastName'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleManagementListService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- CGDIS registration number -->
            <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [flexGrow]="3">
              <ng-template epDatatableHeader>
                <span [translate]="'people_management.functions.cgdisregistrationnumber'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
               {{context.value | defaultValue:'-'}}
              </ng-template>

              <ng-template epDatatableFilter>
                <cgdis-portal-datatable-text-filter [filterName]="'cgdisRegistrationNumber'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="peopleManagementListService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- Availability -->
            <ep-datatable-column [columnName]="'availability'" [flexGrow]="3" [sortable]="false">
              <ng-template epDatatableHeader>
                <span [translate]="'people_management.functions.availability'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>
                <span *ngIf="context.value.professional" [translate]="'general_information.personal_information.is_professional'"></span>
                <span *ngIf="context.value.professional && (context.value.volunteer || context.value.external)">-</span>
                <span *ngIf="context.value.volunteer" [translate]="'general_information.personal_information.is_volunteer'"></span>
                <span *ngIf="context.value.external && (context.value.volunteer || context.value.professional)">-</span>
                <span *ngIf="context.value.external" [translate]="'general_information.personal_information.is_external'"></span>
                <span *ngIf="context.value.technical" [translate]="'general_information.personal_information.is_technical'"></span>
                <span *ngIf="!context.value.technical && !context.value.professional && !context.value.volunteer && !context.value.external" >-</span>
              </ng-template>
            </ep-datatable-column>

            <!-- Status -->
            <ep-datatable-column [columnName]="'status'" [flexGrow]="4" [sortable]="false">
              <ng-template epDatatableHeader>
                <span [translate]="'people_management.functions.status'"></span>
              </ng-template>

              <ng-template epDatatableCell let-context>

                  <span [matTooltip]="getTooltipText(context.value)">{{getTooltipText(context.value)}}</span>

              </ng-template>

              <ng-template epDatatableFilter>

              </ng-template>
            </ep-datatable-column>

          </cgdis-portal-cgdisdatatable>
        </ng-container>
      </div>
    </cgdis-portal-panel>

    <!-- No entities -->
    <ng-template #noEntities>
      <div class="service-plan-list-empty">
        <span [translate]="'layout.navigation.menu.items.people_management.list.empty'"></span>
      </div>
    </ng-template>

  </cgdis-portal-page-template>
</ng-container>



