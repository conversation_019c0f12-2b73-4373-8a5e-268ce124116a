import { Location } from '@angular/common';
import { SimplePopupService } from '../../common/modules/popup/simple-popup.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PersonLight } from '../../model/person/person-light.model';
import { UntypedFormBuilder } from '@angular/forms';
import { CgdisDatatableService } from '../../common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { Injectable } from '@angular/core';

@Injectable()
export class PeopleManagementListService extends CgdisDatatableService<PersonLight> {
  constructor(
    _restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      _restService.all('person', 'assignment', 'logas'),
    );
  }
}
