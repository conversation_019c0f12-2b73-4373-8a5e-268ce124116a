import { ChangeDete<PERSON><PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { EntityService } from '@app/common/shared/services/entity.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { TranslateService } from '@ngx-translate/core';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { PeopleManagementListService } from './people-management-list.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { EntityType } from '@app/model/entity-type.enum';
import { Entity } from '@app/model/entity.model';
import { PersonLightStatus } from '@app/model/person/person-light-status.model';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { DateModel, DateService } from '@eportal/core';
import { FromToPopupDataModel } from '@app/common/modules/popup/from-to-popup/from-to-popup-data-model';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { PeopleManagementListExportRangeComponent } from '@app/people-management/exports/people-management-list-export-range.component';
import { PeopleManagementListExportRangeDataModelContent } from '@app/people-management/exports/people-management-list-export-range-content';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subject, Subscription } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-admin-people-management-list',
  templateUrl: './people-management-list.component.html',
  providers: [PeopleManagementListService],
})
export class PeopleManagementListComponent implements OnInit, OnDestroy {
  public isNotNational: boolean;

  /**
   * The selected entity
   */
  selectedEntity: Entity = null;

  formControl = new UntypedFormControl();

  formControlStatus = new UntypedFormControl();

  formControlAvailability = new UntypedFormControl();

  formControlToggle = new UntypedFormControl();

  formControlTechnicalToggle = new UntypedFormControl();

  filterConfigEntityEqual = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  filterConfigLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });

  filterConfigAllPersons = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });

  private entitiesAvailableForExport: Entity[] = [];

  public showLoading = false;
  showFilter = false;

  public onlyYoungFirefighter = false;
  public hasTechnicalAssignment = false;
  public canExportPrestation = false;
  availabilities: FieldGroupOption<string, string>[] = [];
  statuses: FieldGroupOption<string, string>[] = [];
  public Object = Object;
  statusesTranslations: any;
  mobileStatusesTranslations: any;
  public secondSemester = false;
  public currentYear: number;
  public exportMenuId = 'preferences-export-prestations-switcher';
  public exportMenuPersonsId = 'preferences-export-persons-switcher';
  exportDownloadButtonDisplayed: boolean = false;

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  private _unsubscribeAll = new Subject<void>();

  constructor(
    private entitiesService: EntityService,
    private dateService: DateService,
    private assgnmentService: AssignmentService,
    private connectedUserService: ConnectedUserService,
    private translateService: TranslateService,
    private popupService: SimplePopupService,
    private cd: ChangeDetectorRef,
    public peopleManagementListService: PeopleManagementListService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {
    this.numberOfFilters = 0;
    this.onlyYoungFirefighter =
      this.connectedUserService.hasAnyRoles([
        'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER',
      ]) &&
      !this.connectedUserService.hasAnyRoles([
        'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL',
      ]);
    this.entitiesService
      .getAllForPermissions(['ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT'])
      .pipe(take(1))
      .subscribe((allEntities) => {
        this.entitiesAvailableForExport = allEntities;
        if (this.entitiesAvailableForExport == undefined) {
          this.entitiesAvailableForExport = [];
        }
        this.checkExportDownloadEnabled();
        this.cd.markForCheck();
      });
    this.secondSemester = this.dateService.now().month >= 7;
    this.currentYear = this.dateService.now().year;

    this.connectedUserService
      .hasAnyRolesObservable(['ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS'])
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((newValue) => {
        this.canExportPrestation = newValue;
        this.cd.markForCheck();
      });

    this.statusesTranslations = {
      retired: 'general_information.personal_information.is_retired',
      youngFirefighter:
        'general_information.personal_information.is_young_firefighter',
      veteran: 'general_information.personal_information.is_veteran',
      professionalOperational:
        'general_information.personal_information.is_professional_operational',
      professionalAdmTech:
        'general_information.personal_information.is_professional_adm_tech',
      intern: 'general_information.personal_information.is_intern',
      candidate: 'general_information.personal_information.is_candidate',
      operationalFirefighter:
        'general_information.personal_information.is_operational_firefighter',
      supportFirefighter:
        'general_information.personal_information.is_support_firefighter',
      samu: 'general_information.personal_information.is_samu',
    };

    this.mobileStatusesTranslations = {
      retired: 'general_information.personal_information.is_retired',
      youngFirefighter:
        'general_information.personal_information.is_young_firefighter_short',
      veteran: 'general_information.personal_information.is_veteran',
      professionalOperational:
        'general_information.personal_information.is_professional_operational_short',
      professionalAdmTech:
        'general_information.personal_information.is_professional_adm_tech_short',
      intern: 'general_information.personal_information.is_intern',
      candidate: 'general_information.personal_information.is_candidate',
      operationalFirefighter:
        'general_information.personal_information.is_operational_firefighter_short',
      supportFirefighter:
        'general_information.personal_information.is_support_firefighter_short',
      samu: 'general_information.personal_information.is_samu',
    };

    this.statuses.sort((a, b) => a.label.localeCompare(b.label));

    this.peopleManagementListService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.peopleManagementListService.getNumberOfFilters(['entityId']);
      });
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.peopleManagementListService.getNumberOfFilters([
      'entityId',
    ]);
  }

  setFilter(event: any): void {
    this.formControlToggle.setValue(event.allUnderEntity, { emitEvent: false });
    this.setEntity(event.entity);
  }

  /**
   * Set selected entity into form control
   * @param entity: the selected entity
   */
  setEntity(entity: Entity): void {
    if (entity == null) {
      this.formControl.setValue(null, { emitEvent: false });
    } else {
      this.formControl.setValue(entity.tecid, { emitEvent: false });
    }

    this.selectedEntity = entity;
    this.checkExportDownloadEnabled();
    this.isEntityNotNational();
    this.updateFilter(true);
  }

  private checkExportDownloadEnabled() {
    if (
      this.selectedEntity != undefined &&
      this.entitiesAvailableForExport.find(
        (one) => one.tecid === this.selectedEntity.tecid,
      ) != undefined
    ) {
      this.exportDownloadButtonDisplayed = true;
    } else {
      this.exportDownloadButtonDisplayed = false;
    }
  }

  public isEntityNotNational(): void {
    if (this.selectedEntity != null && this.selectedEntity.tecid != null) {
      this.entitiesService.get(this.selectedEntity.tecid).subscribe((value) => {
        this.isNotNational = value.type !== EntityType.NATIONAL;
      });
    } else {
      this.isNotNational = true;
    }
  }

  public exportCSV(): void {
    this.showLoading = true;
    this.entitiesService
      .exportPersonsCSV(this.selectedEntity.tecid)
      .subscribe(() => {
        this.showLoading = false;
      });
  }

  public exportCSVWithTechnical(technical: boolean): void {
    this.showLoading = true;
    this.entitiesService
      .exportPersonsCSVWithTechnical(this.selectedEntity.tecid, technical)
      .subscribe(() => {
        this.showLoading = false;
      });
  }

  exportFirstSemester(): void {
    let currentYear = this.dateService.now().year;
    let startDate = new DateModel({ day: 1, month: 1, year: currentYear });
    let endDate = new DateModel({ day: 30, month: 6, year: currentYear });
    this.showLoading = true;
    this.exportPrestations(startDate, endDate);
  }

  exportSecondSemester(): void {
    let currentYear = this.dateService.now().year;
    let startDate = new DateModel({ day: 1, month: 7, year: currentYear });
    let endDate = new DateModel({ day: 31, month: 12, year: currentYear });
    this.showLoading = true;
    this.exportPrestations(startDate, endDate);
  }

  exportPreviousSemester(): void {
    let currentYear = this.dateService.now().year;
    let startDate = new DateModel({ day: 1, month: 7, year: currentYear - 1 });
    let endDate = new DateModel({ day: 31, month: 12, year: currentYear - 1 });
    this.showLoading = true;
    this.exportPrestations(startDate, endDate);
  }

  exportPreviousFirstSemester(): void {
    let currentYear = this.dateService.now().year;
    let startDate = new DateModel({ day: 1, month: 1, year: currentYear - 1 });
    let endDate = new DateModel({ day: 30, month: 6, year: currentYear - 1 });
    this.showLoading = true;
    this.exportPrestations(startDate, endDate);
  }

  public getTooltipText(state: PersonLightStatus): string {
    let result = '';
    for (let status of Object.keys(state)) {
      if (state[status as keyof typeof state]) {
        if (this.isMobile) {
          result =
            result +
            this.translateService.instant(
              this.mobileStatusesTranslations[status],
            ) +
            ' - ';
        } else {
          result =
            result +
            this.translateService.instant(this.statusesTranslations[status]) +
            ' - ';
        }
      }
    }
    return result.length > 3 ? result.substring(0, result.length - 3) : '-';
  }

  public updateFilter(changeEntity: boolean): void {
    let subentities = this.formControlToggle.value === true;
    if (changeEntity) {
      this.formControlTechnicalToggle.setValue(false, { emitEvent: false });
      if (this.selectedEntity.tecid != null) {
        this.assgnmentService
          .existTechnicalAssignments(this.selectedEntity.tecid, subentities)
          .subscribe((exist) => {
            this.hasTechnicalAssignment = exist;
            this.cd.markForCheck();
          });
      }
    }
    this.formControlAvailability.setValue(null, { emitEvent: false });
    // Emit event only for the last formcontrol update to avoid multiple call
    this.formControlStatus.setValue(null);

    let technical = this.formControlTechnicalToggle.value === true;

    if (this.selectedEntity.tecid != null) {
      this.assgnmentService
        .getAllStatuesForEntity(
          this.selectedEntity.tecid,
          subentities,
          technical,
        )
        .subscribe((allStatus) => {
          this.availabilities = [];
          this.statuses = [];

          if (allStatus.volunteer) {
            this.availabilities.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_volunteer',
                ),
                value: 'volunteer',
              }),
            );
          }

          if (allStatus.primaryTypeTechnicalPro) {
            this.availabilities.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_professional_tech',
                ),
                value: 'professionalAdmTechicalPrimaryType',
              }),
            );
          }
          if (allStatus.professional) {
            this.availabilities.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_professional',
                ),
                value: 'professional',
              }),
            );
          }
          if (allStatus.primaryTypeTechnicalVol) {
            this.availabilities.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_volunteer_tech',
                ),
                value: 'volunteerAdmTechicalPrimaryType',
              }),
            );
          }
          if (allStatus.external) {
            this.availabilities.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_external',
                ),
                value: 'external',
              }),
            );
          }
          this.availabilities.sort((a, b) => a.label.localeCompare(b.label));

          if (!this.onlyYoungFirefighter && allStatus.candidate) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_candidate',
                ),
                value: 'candidate',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.intern) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_intern',
                ),
                value: 'intern',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.operationalFirefighter) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_operational_firefighter',
                ),
                value: 'operationalFirefighter',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.professionalAdmTech) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_professional_adm_tech',
                ),
                value: 'professionalAdmTech',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.professionalOperational) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_professional_operational',
                ),
                value: 'professionalOperational',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.retired) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_retired',
                ),
                value: 'retired',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.samu) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_samu',
                ),
                value: 'samu',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.supportFirefighter) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_support_firefighter',
                ),
                value: 'supportFirefighter',
              }),
            );
          }
          if (!this.onlyYoungFirefighter && allStatus.veteran) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_veteran',
                ),
                value: 'veteran',
              }),
            );
          }
          if (allStatus.youngFirefighter) {
            this.statuses.push(
              new FieldOption({
                label: this.translateService.instant(
                  'general_information.personal_information.is_young_firefighter',
                ),
                value: 'youngFirefighter',
              }),
            );
          }
        });
    } else {
      this.availabilities = [];
      this.statuses = [];
    }
  }

  private exportPrestations(startDate: DateModel, endDate: DateModel): void {
    if (this.selectedEntity.tecid != null) {
      this.entitiesService
        .exportPrestationsGlobal(this.selectedEntity.tecid, startDate, endDate)
        .subscribe(() => {
          this.showLoading = false;
        });
    } else {
      this.entitiesService
        .exportPrestationsGlobalNoAssignment(startDate, endDate)
        .subscribe(() => {
          this.showLoading = false;
        });
    }
  }

  exportRange(): void {
    // this.loading = true;
    this.cd.markForCheck();
    const fromDate = this.dateService.now();
    let popupDialog = this.popupService.open(
      PeopleManagementListExportRangeComponent,
      {
        autoFocus: false,
        data: new FromToPopupDataModel<PeopleManagementListExportRangeDataModelContent>(
          {
            title:
              'layout.navigation.profile.mypreferences.export-prestations-range',
            message: 'service_plan.popup.export.pdf.message',
            messageHtml: true,
            content: new PeopleManagementListExportRangeDataModelContent({
              entityTecid:
                this.selectedEntity != null ? this.selectedEntity.tecid : null,
              from: fromDate,
              to: this.dateService.add(fromDate, 1, 'month'),
            }),
          },
        ),
        panelClass: this.isMobile ? 'simple-popup-mobile' : ['simple-popup'],
      },
    );
  }
}
