import { PageTemplateModule } from '../../common/template/page-template/page-template.module';
import { SharedModule } from '../../common/shared/shared.module';
import { NgModule } from '@angular/core';
import { PeopleManagementListComponent } from './people-management-list.component';
import { SimplePopupModule } from '../../common/modules/popup/simple-popup.module';
import { FormsModule } from '@angular/forms';
import { NgxSelectModule } from 'ngx-select-ex';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { EntityService } from '../../common/shared/services/entity.service';
import { PersonDetailEditModule } from './person-detail-edit/person-detail-edit.module';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    NgxSelectModule,
    FormsModule,
    DatatableModule,
    SimplePopupModule,
    EpDatatableModule,
    PersonDetailEditModule,
    MatTooltipModule,
    FormModule,
    EntityFilterModule,
  ],
  providers: [EntityService],
  declarations: [PeopleManagementListComponent],
})
export class PeopleManagementListModule {}
