import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { RouterModule } from '@angular/router';
import { OPTIONAL_BACKUP_GROUP_MANAGEMENT_ROUTE } from '@app/optional-backup-group-management/optional-backup-group-management.route';
import { OptionalBackupGroupManagementComponent } from '@app/optional-backup-group-management/optional-backup-group-management.component';
import { OptionalBackupGroupManagementListModule } from '@app/optional-backup-group-management/list/optional-backup-group-management-list.module';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(OPTIONAL_BACKUP_GROUP_MANAGEMENT_ROUTE),
    OptionalBackupGroupManagementListModule,
  ],
  declarations: [OptionalBackupGroupManagementComponent],
})
export class OptionalBackupGroupManagementModule {}
