import { Routes } from '@angular/router';
import { RoleGuard } from '@app/security/guards/role.guard';
import { OptionalBackupGroupManagementComponent } from '@app/optional-backup-group-management/optional-backup-group-management.component';
import { OptionalBackupGroupManagementListComponent } from '@app/optional-backup-group-management/list/optional-backup-group-management-list.component';

export const OPTIONAL_BACKUP_GROUP_MANAGEMENT_ROUTE: Routes = [
  {
    path: '',
    component: OptionalBackupGroupManagementComponent,
    canActivate: [RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT'],
    },
    children: [
      {
        path: 'list',
        component: OptionalBackupGroupManagementListComponent,
        canActivate: [RoleGuard],
        data: {
          expectedRoles: [
            'ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW',
          ],
        },
      },
    ],
  },
];
