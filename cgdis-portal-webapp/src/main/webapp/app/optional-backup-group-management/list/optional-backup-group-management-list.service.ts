import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { OptionalBackupGroupSummaryDto } from '@app/model/optional-backup-group-summary-dto.model';

@Injectable()
export class OptionalBackupGroupManagementListService extends CgdisDatatableService<OptionalBackupGroupSummaryDto> {
  constructor(
    _restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      _restService.all('optional-backup-group', 'summary'),
    );
  }
}
