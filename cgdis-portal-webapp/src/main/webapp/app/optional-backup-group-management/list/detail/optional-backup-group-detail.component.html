<ng-container *ngIf="backupGroup && backupGroup.group.tecid">
  <cgdis-portal-cgdisdatatable
    [datatableService]="optionalBackupGroupManagementService"
    [id]="'optional-backup-group-person-list'"
    [sorts]="[{dir:'asc',prop:'name'}]"
    [class]="'entity__table'">



    <!-- Filter for entityId -->
    <cgdis-portal-datatable-number-filter [hidden]="true"
                                          [filterConfig]="filterConfigEq"
                                          [filterName]="'optionalBackupGroupTecid'"
                                          [customFormControl]="formControl"
                                          [datatableService]="optionalBackupGroupManagementService"></cgdis-portal-datatable-number-filter>

    <!-- first name -->
    <ep-datatable-column [columnName]="'name'" [flexGrow]="isMobile ? 5 : 3">
      <ng-template epDatatableHeader>
        <span [translate]="'optional_backup_group_management.list.name'"></span>
      </ng-template>

      <ng-template epDatatableCell let-row="row">
        {{row.name}}
      </ng-template>

      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [filterName]="'name'"
                                            [placeholder]="''"
                                            [allowClear]="true"
                                            [filterConfig]="filterConfigLike"
                                            [datatableService]="optionalBackupGroupManagementService"></cgdis-portal-datatable-text-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- last name -->
    <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [flexGrow]="isMobile ? 5 : 3">
      <ng-template epDatatableHeader>
        <span [translate]="'optional_backup_group_management.list.cgdisregistrationnumber'"></span>
      </ng-template>

      <ng-template epDatatableCell let-row="row">
        {{row.cgdisRegistrationNumber}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [filterName]="'cgdisRegistrationNumber'"
                                            [placeholder]="''"
                                            [allowClear]="true"
                                            [filterConfig]="filterConfigLike"
                                            [datatableService]="optionalBackupGroupManagementService"></cgdis-portal-datatable-text-filter>
      </ng-template>

    </ep-datatable-column>

  </cgdis-portal-cgdisdatatable>

</ng-container>
