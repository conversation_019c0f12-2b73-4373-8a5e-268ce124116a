import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule } from '@angular/forms';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { EpDatatableModule } from '@eportal/components';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EntityService } from '@app/common/shared/services/entity.service';
import { OptionalBackupGroupDetailComponent } from '@app/optional-backup-group-management/list/detail/optional-backup-group-detail.component';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    NgxSelectModule,
    FormsModule,
    DatatableModule,
    SimplePopupModule,
    EpDatatableModule,
    MatTooltipModule,
  ],
  providers: [EntityService],
  declarations: [OptionalBackupGroupDetailComponent],
  exports: [OptionalBackupGroupDetailComponent],
})
export class OptionalBackupGroupDetailModule {}
