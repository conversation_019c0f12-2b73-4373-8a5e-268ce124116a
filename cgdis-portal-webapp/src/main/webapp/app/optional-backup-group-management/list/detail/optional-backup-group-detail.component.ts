import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { OptionalBackupGroupPersonService } from '@app/optional-backup-group-management/list/detail/optional-backup-group-detail.service';
import { UntypedFormControl } from '@angular/forms';
import { FilterConfig, SearchOperator } from '@eportal/components';

import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { OptionalBackupGroupSummaryDto } from '@app/model/optional-backup-group-summary-dto.model';
import { OptionalBackupGroup } from '@app/model/optional-backup-group.model';

@Component({
  selector: 'cgdis-portal-optional-backup-group-detail',
  templateUrl: './optional-backup-group-detail.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OptionalBackupGroupPersonService],
})
export class OptionalBackupGroupDetailComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() backupGroup: OptionalBackupGroupSummaryDto;

  formControl = new UntypedFormControl();

  formControlToggle = new UntypedFormControl();

  filterConfigLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });

  filterConfigEq = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private cd: ChangeDetectorRef,
    public optionalBackupGroupManagementService: OptionalBackupGroupPersonService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    // this.seOptionalBackup(this.backupGroup.group);
    this.cd.markForCheck();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.backupGroup && changes.backupGroup.currentValue) {
      this.seOptionalBackup(this.backupGroup.group);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public seOptionalBackup(backupGroup: OptionalBackupGroup): void {
    this.formControl.setValue(backupGroup.tecid);
    this.cd.markForCheck();
  }
}
