import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { EntityService } from '@app/common/shared/services/entity.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { TranslateService } from '@ngx-translate/core';
import { OptionalBackupGroupManagementListService } from '@app/optional-backup-group-management/list/optional-backup-group-management-list.service';
import { Entity } from '@app/model/entity.model';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { take } from 'rxjs/operators';
import { OptionalBackupGroupSummaryDto } from '@app/model/optional-backup-group-summary-dto.model';

@Component({
  selector: 'cgdis-portal-optional-backup-group-management-list',
  templateUrl: './optional-backup-group-management-list.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OptionalBackupGroupManagementListService],
})
export class OptionalBackupGroupManagementListComponent
  implements OnInit, OnDestroy
{
  selectedEntity: Entity;

  formControl = new UntypedFormControl();

  formControlToggle = new UntypedFormControl();

  filterConfigEntityEqual = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  filterConfigLike = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.like,
  });

  filterConfigAllGroup = new FilterConfig({
    inUrl: true,
    operator: SearchOperator.eq,
  });

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  showFilter = false;

  numberOfFilters: number;

  constructor(
    private entitiesService: EntityService,
    private connectedUserService: ConnectedUserService,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef,
    public optionalBackupGroupManagementService: OptionalBackupGroupManagementListService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );

    this.optionalBackupGroupManagementService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.optionalBackupGroupManagementService.getNumberOfFilters();
      });
  }

  ngOnInit() {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  setFilter(event: any): void {
    this.formControlToggle.setValue(event.allUnderEntity, { emitEvent: false });
    this.setEntity(event.entity);
  }

  public setEntity(entity: Entity): void {
    this.selectedEntity = entity;
    this.formControl.setValue(entity.globalId);
    this.cd.markForCheck();
  }

  updateFilterNumber() {
    this.numberOfFilters =
      this.optionalBackupGroupManagementService.getNumberOfFilters();
  }

  castRow(row: any): OptionalBackupGroupSummaryDto {
    return row as OptionalBackupGroupSummaryDto;
  }
}
