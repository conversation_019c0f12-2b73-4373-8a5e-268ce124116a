import { NgModule } from '@angular/core';
import { OptionalBackupGroupManagementListComponent } from '@app/optional-backup-group-management/list/optional-backup-group-management-list.component';
import { SharedModule } from '@app/common/shared/shared.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule } from '@angular/forms';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { EpDatatableModule } from '@eportal/components';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EntityService } from '@app/common/shared/services/entity.service';
import { OptionalBackupGroupDetailModule } from '@app/optional-backup-group-management/list/detail/optional-backup-group-detail.module';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    NgxSelectModule,
    FormsModule,
    DatatableModule,
    SimplePopupModule,
    EpDatatableModule,
    MatTooltipModule,
    OptionalBackupGroupDetailModule,
    EntityFilterModule,
  ],
  providers: [EntityService],
  declarations: [OptionalBackupGroupManagementListComponent],
})
export class OptionalBackupGroupManagementListModule {}
