<ng-container class="people-management">
  <cgdis-portal-page-template>

    <cgdis-portal-page-header [titleKey]="'layout.navigation.menu.items.optional_backup_group_management.title'" [subtitleAlign]="true">
      <div page-header-subtitle >
        <cgdis-portal-entity-filter [entityPermission]="['ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW']" [initPrimaryEntity]="true" (filterSelected)="setFilter($event)"></cgdis-portal-entity-filter>
      </div>
    </cgdis-portal-page-header>

    <cgdis-portal-panel>
      <div  class="localSpinner">
        <!-- Persons -->
        <ng-container *ngIf="selectedEntity"><cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
          <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
          <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
        </cgdis-portal-button-link>

          <ng-container *ngIf="isMobile">
            <div class="row search-filter" [hidden]="!showFilter">
              <div class="col-md-2">
                <label class="form-label" [translate]="'optional_backup_group_management.list.name'"></label>
                <cgdis-portal-datatable-text-filter (onValueChanged)="updateFilterNumber()" [filterName]="'name'"
                                                    [allowClear]="true"
                                                    [datatableService]="optionalBackupGroupManagementService"></cgdis-portal-datatable-text-filter>
              </div>
            </div>
          </ng-container>

          <cgdis-portal-cgdisdatatable
            [datatableService]="optionalBackupGroupManagementService"
            [id]="'optional-backup-group-list'"
            [sorts]="[{dir:'asc',prop:'name'}]"
            [class]="'entity__table'">

            <ng-template #template let-row="row">
              <div class="person-detail-row margin" *ngIf="selectedEntity && castRow(row).group.tecid && castRow(row).personsCount > 0">
                <cgdis-portal-optional-backup-group-detail [backupGroup]="castRow(row)"></cgdis-portal-optional-backup-group-detail>

              </div>
            </ng-template>

            <cgdis-portal-datatable-toggle-filter [hidden]="true"

                                                  [filterName]="'allGroupUnderEntity'"
                                                  [filterConfig]="filterConfigAllGroup"
                                                  [datatableService]="optionalBackupGroupManagementService"
                                                  [customFormControl]="formControlToggle"
            ></cgdis-portal-datatable-toggle-filter>

            <!-- Filter for entityId -->
            <cgdis-portal-datatable-number-filter [hidden]="true"
                                                  [filterConfig]="filterConfigEntityEqual"
                                                  [filterName]="'entityGlobalId'"
                                                  [customFormControl]="formControl"
                                                  [datatableService]="optionalBackupGroupManagementService"></cgdis-portal-datatable-number-filter>

            <!-- first name -->
            <ep-datatable-column [columnName]="'name'" [flexGrow]="isMobile ? 5 : 3">
              <ng-template epDatatableHeader>
                <span [translate]="'optional_backup_group_management.list.name'"></span>
              </ng-template>

              <ng-template epDatatableCell let-row="row">
                {{castRow(row).group.name}}
              </ng-template>

              <ng-template epDatatableFilter>
                <cgdis-portal-datatable-text-filter [filterName]="'name'"
                                                    [placeholder]="''"
                                                    [allowClear]="true"
                                                    [filterConfig]="filterConfigLike"
                                                    [datatableService]="optionalBackupGroupManagementService"></cgdis-portal-datatable-text-filter>
              </ng-template>
            </ep-datatable-column>

            <!-- last name -->
            <ep-datatable-column [columnName]="'member'" [flexGrow]="isMobile ? 5 : 3">
              <ng-template epDatatableHeader>
                <span [translate]="'optional_backup_group_management.list.members'"></span>
              </ng-template>

              <ng-template epDatatableCell let-row="row">
                {{castRow(row).personsCount}}
              </ng-template>


            </ep-datatable-column>

          </cgdis-portal-cgdisdatatable>

        </ng-container>
      </div>
    </cgdis-portal-panel>

  </cgdis-portal-page-template>
</ng-container>
