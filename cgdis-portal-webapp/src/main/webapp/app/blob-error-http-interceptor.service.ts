import { Observable, throwError as observableThrowError } from 'rxjs';
import { Injectable, Injector } from '@angular/core';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { ErrorService } from '@eportal/core';
import { catchError } from 'rxjs/operators';

@Injectable()
export class BlobErrorHttpInterceptorService implements HttpInterceptor {
  errorService: ErrorService;

  constructor(private injector: Injector) {}
  intercept(
    req: HttpRequest<any>,
    next: <PERSON>ttp<PERSON>and<PERSON>,
  ): Observable<HttpEvent<any>> {
    this.errorService = this.injector.get(ErrorService);
    return next.handle(req).pipe(
      catchError((err: any) => {
        if (
          err instanceof HttpErrorResponse &&
          err.error instanceof Blob &&
          err.error.type === 'application/json'
        ) {
        }

        return observableThrowError(err);
      }),
    );
  }

  // public intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
  //   return next.handle(req).catch((response: any) => {
  //       if (err instanceof HttpErrorResponse && err.error instanceof Blob && err.error.type === "application/json") {
  //         // https://github.com/angular/angular/issues/19888
  //         // When request of type Blob, the error is also in Blob instead of object of the json data
  //         return new Promise<any>((resolve, reject) => {
  //           let reader = new FileReader();
  //           reader.onload = (e: Event) => {
  //             try {
  //               const errmsg = JSON.parse((<any>e.target).result);
  //               reject(new HttpErrorResponse({
  //                 error: errmsg,
  //                 headers: err.headers,
  //                 status: err.status,
  //                 statusText: err.statusText,
  //                 url: err.url
  //               }));
  //             } catch (e) {
  //               reject(err);
  //             }
  //           };
  //           reader.onerror = (e) => {
  //             reject(err);
  //           };
  //           reader.readAsText(err.error);
  //         });
  //       }
  //       return throwError(err);
  //     })
  //
  // }
}
