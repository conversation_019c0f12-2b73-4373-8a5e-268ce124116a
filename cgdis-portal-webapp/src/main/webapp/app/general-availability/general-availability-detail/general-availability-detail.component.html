<!--<cgdis-portal-page-template>-->
<!--  <cgdis-portal-page-header-->
<!--    [titleKey]="'general_availability.title'"-->
<!--    [subtitleAlign]="true"-->
<!--  >-->


<!--  </cgdis-portal-page-header>-->

  <ng-template #planningNotVisible>
    <div class="txtcenter" [translate]="'availability_planning.no_data'"></div>
  </ng-template>

  <cgdis-portal-tabs *ngIf="true; else planningNotVisible">

    <ng-container tabs-content>
      <cgdis-portal-general-availability-full-tabs [servicePlanId]="servicePlanId" [selectedWeek]="selectedWeek"></cgdis-portal-general-availability-full-tabs>
    </ng-container>
  </cgdis-portal-tabs>
<!--</cgdis-portal-page-template>-->
