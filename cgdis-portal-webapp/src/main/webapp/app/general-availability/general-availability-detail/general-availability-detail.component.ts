import { Component, OnDestroy, OnInit } from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-general-availabilty-detail',
  templateUrl: './general-availability-detail.component.html',
  styles: [],
})
export class GeneralAvailabilityDetailComponent implements OnInit, OnDestroy {
  selectedWeek: DateModel;
  servicePlanId: number;

  private subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private route: ActivatedRoute,
  ) {}

  ngOnInit() {
    this.subscriptions.push(
      this.route.params.subscribe((params) => {
        const selectedDateInData = params['selectedWeek'];
        if (selectedDateInData != undefined) {
          const selectedWeek = this.dateService.parse(selectedDateInData);
          if (selectedWeek != undefined) {
            this.selectedWeek = selectedWeek;
          }
        }
        this.servicePlanId = params['servicePlanId'];
      }),
    );
  }

  ngOnDestroy(): void {
    // Unsubscribe to all subscriptions
    for (let subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
  }

  public updateStartDate(newSelectedWeek: DateModel): void {
    if (!this.dateService.equals(this.selectedWeek, newSelectedWeek)) {
      this.selectedWeek = newSelectedWeek;
    }
  }
}
