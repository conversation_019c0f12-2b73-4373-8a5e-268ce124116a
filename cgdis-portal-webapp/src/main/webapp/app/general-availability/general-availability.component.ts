import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';

@Component({
  selector: 'cgdis-portal-general-availability',
  templateUrl: './general-availability.component.html',
  styleUrls: ['./general-availability.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GeneralAvailabilityComponent implements OnInit {
  constructor() {}

  ngOnInit() {}
}
