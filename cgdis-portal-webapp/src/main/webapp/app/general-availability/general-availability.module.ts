import { NgModule } from '@angular/core';
import { SharedModule } from '../common/shared/shared.module';
import { RouterModule } from '@angular/router';
import { CalendarModule } from '../common/modules/calendar/calendar.module';
import { LegendModule } from '../common/modules/legend/legend.module';
import { PageTemplateModule } from '../common/template/page-template/page-template.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { GENERAL_AVAILABILITY_ROUTE } from './general-availability.route';
import { GeneralAvailabilityComponent } from './general-availability.component';
import { GeneralAvailabilityDetailComponent } from './general-availability-detail/general-availability-detail.component';
import { WeekSelectorModule } from '../common/modules/week-selector/week-selector.module';
import { GeneralAvailabilityFullTabsModule } from '../common/modules/general-availability-full-tabs/general-availability-full-tabs.module';

@NgModule({
  imports: [
    SharedModule.forRoot(),
    RouterModule.forChild(GENERAL_AVAILABILITY_ROUTE),
    CalendarModule,
    WeekSelectorModule,
    PageTemplateModule,
    NgxSelectModule,
    GeneralAvailabilityFullTabsModule,
    LegendModule,
  ],
  declarations: [
    GeneralAvailabilityComponent,
    GeneralAvailabilityDetailComponent,
  ],
  exports: [GeneralAvailabilityComponent],
})
export class GeneralAvailabilityModule {}
