cgdis-portal-general-availabilty-detail {
  ng-scrollbar {
    .ng-scroll-viewport {
      &.cgdis-scroll__viewport.general-availability-full-tabs__scroll__viewport {
        max-height: calc(100vh - 18rem) !important;
      }
    }
  }

  .general-availability-header {
    height: 12rem;

    &__title {
      height: 6rem;
    }
  }
  .legend-box {
    margin-bottom: 0;
    height: 6rem;
  }

  .general-availability-header__person {
    transform: translateX(-1%) rotate(-90deg) translatex(-50%);

    width: 4rem !important;
    .general-availability-header__person__content {
      width: 10rem !important;
      max-width: 9rem;
      height: 2rem;
      text-align: left;
    }
  }

  .general-availability-onerow-cellule {
    width: 4rem;
  }
}
