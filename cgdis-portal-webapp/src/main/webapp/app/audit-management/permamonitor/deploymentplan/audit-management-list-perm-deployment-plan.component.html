<div class="accordion__panel">


  <cgdis-portal-cgdisdatatable
    [datatableService]="auditService"
    [id]="'audit-perm-deployment-plan-list'"
    [showDetails]="'MOBILE'"
    [autoMobileFilters]="true"
    [sorts]="[{dir:'desc',prop:'tecid'}]"
    [class]="'entity__table'">


    <cgdis-portal-audit-action-date-column
      cgdisDatatableColumn
      [dateFormControl]="dateFormControl"
      [auditService]="auditService">
    </cgdis-portal-audit-action-date-column>

    <cgdis-portal-audit-person-tecid-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
    ></cgdis-portal-audit-person-tecid-column>

    <cgdis-portal-audit-person-cgdis-registration-number-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-person-cgdis-registration-number-column>


    <cgdis-portal-audit-perm-deployment-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-deployment-name-column>

    <cgdis-portal-audit-perm-deployment-startdate-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-deployment-startdate-column>


    <cgdis-portal-audit-perm-deployment-description-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
    ></cgdis-portal-audit-perm-deployment-description-column>

    <cgdis-portal-audit-action-type-column
      cgdisDatatableColumn
      [auditService]="auditService"
      [auditTypes]="[AuditTypeEnum.PERM_DEPLOYMENT_PLAN]"
    ></cgdis-portal-audit-action-type-column>


    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-perm-deployment-plan-detail
          [audit]="row"></cgdis-portal-audit-management-perm-deployment-plan-detail>
      </div>
    </ng-template>


  </cgdis-portal-cgdisdatatable>

</div>
