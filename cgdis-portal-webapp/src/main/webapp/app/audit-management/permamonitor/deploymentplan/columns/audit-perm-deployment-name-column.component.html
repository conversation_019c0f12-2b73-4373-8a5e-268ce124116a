<ep-datatable-column [flexGrow]="3" [columnName]="'deploymentName'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.permamonitor.deploymentplan.name'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).name | defaultValue }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-with-null-filter
      [customFormControl]="formControl"
      [filterName]="'deploymentPlanName'"
      [filterConfig]="filterConfig"
      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
  </ng-template>

</ep-datatable-column>
