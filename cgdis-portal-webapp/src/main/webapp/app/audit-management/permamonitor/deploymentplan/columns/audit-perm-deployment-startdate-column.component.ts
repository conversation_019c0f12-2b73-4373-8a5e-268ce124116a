import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditPermDeploymentPlanModel } from '@app/model/audit/audit.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { FormControl } from '@angular/forms';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { DatetimeModel } from '@eportal/core';

@Component({
  selector: 'cgdis-portal-audit-perm-deployment-startdate-column',
  templateUrl: './audit-perm-deployment-startdate-column.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPermDeploymentStartdateColumnComponent,
      ),
    },
  ],
  imports: [SharedModule, EpDatatableModule, DatatableModule],
})
export class AuditPermDeploymentStartdateColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermDeploymentPlanModel>
  implements OnInit
{
  @Input() auditService: AuditManagementService;

  formControl: FormControl<DatetimeModel>;
  filterConfig: FilterConfig;

  ngOnInit(): void {
    this.formControl = new FormControl<DatetimeModel>(undefined);
    this.filterConfig = {
      operator: SearchOperator.eq,
    };
  }
}
