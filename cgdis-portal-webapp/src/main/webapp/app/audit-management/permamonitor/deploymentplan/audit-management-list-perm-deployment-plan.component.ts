import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditManagementListPermDeploymentPlanService } from '@app/audit-management/permamonitor/deploymentplan/audit-management-list-perm-deployment-plan.service';
import { UntypedFormControl } from '@angular/forms';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementPermDeploymentPlanDetailComponent } from '@app/audit-management/permamonitor/detail/deploymentplan/audit-management-perm-deployment-plan-detail.component';
import { EpDatatableModule } from '@eportal/components';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditPermDeploymentNameColumnComponent } from '@app/audit-management/permamonitor/deploymentplan/columns/audit-perm-deployment-name-column.component';
import { AuditPermDeploymentStartdateColumnComponent } from '@app/audit-management/permamonitor/deploymentplan/columns/audit-perm-deployment-startdate-column.component';
import { AuditPermDeploymentDescriptionColumnComponent } from '@app/audit-management/permamonitor/deploymentplan/columns/audit-perm-deployment-description-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-perm-deployment-plan',
  templateUrl: './audit-management-list-perm-deployment-plan.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListPermDeploymentPlanService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementPermDeploymentPlanDetailComponent,
    AuditManagementModule,
    AuditPermDeploymentNameColumnComponent,
    AuditPermDeploymentStartdateColumnComponent,
    AuditPermDeploymentDescriptionColumnComponent,
  ],
})
export class AuditManagementListPermDeploymentPlanComponent {
  @Input() dateFormControl: UntypedFormControl;

  constructor(
    public auditService: AuditManagementListPermDeploymentPlanService,
  ) {}

  protected readonly AuditTypeEnum = AuditTypeEnum;
}
