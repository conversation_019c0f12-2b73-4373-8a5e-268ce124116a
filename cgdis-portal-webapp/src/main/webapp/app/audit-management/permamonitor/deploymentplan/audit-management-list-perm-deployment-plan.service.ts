import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditPermDeploymentPlanModel } from '@app/model/audit/audit.model';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';

@Injectable()
export class AuditManagementListPermDeploymentPlanService extends CgdisDatatableService<AuditPermDeploymentPlanModel> {
  constructor(
    restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    super.initDataResourceList(
      restService.all('audits', 'permamonitor', 'deploymentplans'),
    );
  }
}
