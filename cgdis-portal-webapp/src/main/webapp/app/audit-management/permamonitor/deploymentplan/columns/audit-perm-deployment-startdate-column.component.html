<ep-datatable-column [flexGrow]="3" [columnName]="'startDate'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.permamonitor.deploymentplan.startdate'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).startDate | dateFormat }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-datepicker-filter
      [customFormControl]="formControl"
      [filterName]="'permDeploymentPlanStartDate'"
      [filterConfig]="filterConfig"
      [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>
  </ng-template>

</ep-datatable-column>
