import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditPermDeploymentPlanModel } from '@app/model/audit/audit.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { EpDatatableModule } from '@eportal/components';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';

@Component({
  selector: 'cgdis-portal-audit-perm-deployment-description-column',
  templateUrl: './audit-perm-deployment-description-column.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPermDeploymentDescriptionColumnComponent,
      ),
    },
  ],
  imports: [SharedModule, EpDatatableModule, DatatableModule],
})
export class AuditPermDeploymentDescriptionColumnComponent extends CgdisDatatableColumnComponent<AuditPermDeploymentPlanModel> {}
