import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import {
  MatAccordion,
  MatExpansionModule,
  MatExpansionPanel,
  MatExpansionPanelContent,
  MatExpansionPanelTitle,
} from '@angular/material/expansion';
import { UntypedFormControl } from '@angular/forms';
import { DateService } from '@eportal/core';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { TranslateModule } from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { AuditManagementListPermDeploymentPlanComponent } from '@app/audit-management/permamonitor/deploymentplan/audit-management-list-perm-deployment-plan.component';
import { AuditManagementListPermServicePlanComponent } from '@app/audit-management/permamonitor/serviceplan/audit-management-list-perm-service-plan.component';
import { AuditManagementListPermConfigDpceComponent } from '@app/audit-management/permamonitor/configdpce/audit-management-list-perm-config-dpce.component';
import { AuditManagementListPermConfigDpceCopyComponent } from '@app/audit-management/permamonitor/configdpce/audit-management-list-perm-config-dpce-copy.component';

@Component({
  selector: 'cgdis-portal-audit-permamonitor',
  standalone: true,
  imports: [
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelContent,
    MatExpansionPanelTitle,
    AuditManagementModule,
    MatExpansionModule,
    MatTabsModule,
    TranslateModule,
    AuditManagementListPermDeploymentPlanComponent,
    AuditManagementListPermServicePlanComponent,
    AuditManagementListPermConfigDpceComponent,
    AuditManagementListPermConfigDpceCopyComponent,
  ],
  templateUrl: './audit-permamonitor.component.html',
  styleUrl: './audit-permamonitor.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditPermamonitorComponent implements OnInit {
  dateFormControl = new UntypedFormControl();

  constructor(private dateService: DateService) {}

  ngOnInit(): void {
    this.dateFormControl.setValue(this.dateService.now());
  }
}
