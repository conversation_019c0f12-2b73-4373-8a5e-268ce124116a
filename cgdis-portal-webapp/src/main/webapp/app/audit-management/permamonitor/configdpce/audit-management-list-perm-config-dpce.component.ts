import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditManagementListPermConfigDpceService } from '@app/audit-management/permamonitor/configdpce/audit-management-list-perm-config-dpce.service';
import { UntypedFormControl } from '@angular/forms';
import { NgIf } from '@angular/common';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { AuditManagementPermConfigDpceDetailComponent } from '@app/audit-management/permamonitor/detail/configdpce/audit-management-perm-config-dpce-detail.component';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditPermConfigDpceDeploymentNameColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-deployment-name-column.component';
import { AuditPermConfigDpceEntityNameColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-entity-name-column.component';
import { AuditPermCategoryNameColumnComponent } from '@app/audit-management/permamonitor/columns/audit-perm-category-name-column.component';
import { AuditPermConfigDpceDayColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-day-column.component';
import { AuditPermConfigDpceHourColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-hour-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-perm-config-dpce',
  templateUrl: './audit-management-list-perm-config-dpce.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    DatatableModule,
    AuditManagementPermConfigDpceDetailComponent,
    AuditManagementModule,
    AuditPermConfigDpceDeploymentNameColumnComponent,
    AuditPermConfigDpceEntityNameColumnComponent,
    AuditPermCategoryNameColumnComponent,
    AuditPermConfigDpceDayColumnComponent,
    AuditPermConfigDpceHourColumnComponent,
  ],
  providers: [AuditManagementListPermConfigDpceService],
})
export class AuditManagementListPermConfigDpceComponent {
  @Input() dateFormControl: UntypedFormControl;

  constructor(public auditService: AuditManagementListPermConfigDpceService) {}

  protected readonly AuditTypeEnum = AuditTypeEnum;
}
