import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditManagementListPermConfigDpceCopyService } from '@app/audit-management/permamonitor/configdpce/audit-management-list-perm-config-dpce-copy.service';
import { UntypedFormControl } from '@angular/forms';
import { NgIf } from '@angular/common';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { AuditManagementPermConfigDpceCopyDetailComponent } from '@app/audit-management/permamonitor/detail/configdpce/audit-management-perm-config-dpce-copy-detail.component';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditPermConfigDpceCopyDeploymentNameColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-copy-deployment-name-column.component';
import { AuditPermConfigDpceCopyEntityNameColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-copy-entity-name-column.component';
import { AuditPermConfigDpceCopyToEntityNameColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-copy-to-entity-name-column.component';
import { AuditPermConfigDpceHourColumnComponent } from '@app/audit-management/permamonitor/configdpce/colums/audit-perm-config-dpce-hour-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-perm-config-dpce-copy',
  templateUrl: './audit-management-list-perm-config-dpce-copy.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    DatatableModule,
    AuditManagementPermConfigDpceCopyDetailComponent,
    AuditManagementModule,
    AuditPermConfigDpceCopyDeploymentNameColumnComponent,
    AuditPermConfigDpceCopyEntityNameColumnComponent,
    AuditPermConfigDpceCopyEntityNameColumnComponent,
    AuditPermConfigDpceCopyToEntityNameColumnComponent,
    AuditPermConfigDpceHourColumnComponent,
  ],
  providers: [AuditManagementListPermConfigDpceCopyService],
})
export class AuditManagementListPermConfigDpceCopyComponent {
  @Input() dateFormControl: UntypedFormControl;

  protected readonly AuditTypeEnum = AuditTypeEnum;

  constructor(
    public auditService: AuditManagementListPermConfigDpceCopyService,
  ) {}
}
