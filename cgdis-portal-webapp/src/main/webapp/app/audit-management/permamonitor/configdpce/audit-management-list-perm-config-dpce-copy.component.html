<div class="accordion__panel">


  <cgdis-portal-cgdisdatatable
    [datatableService]="auditService"
    [id]="'audit-perm-config-dpce-copy-list'"
    [sorts]="[{dir:'desc',prop:'tecid'}]"
    [showDetails]="'MOBILE'"
    [autoMobileFilters]="true"
    [class]="'entity__table'">


    <!-- Action date time -->
    <cgdis-portal-audit-action-date-column
      cgdisDatatableColumn
      [dateFormControl]="dateFormControl"
      [auditService]="auditService">
    </cgdis-portal-audit-action-date-column>


    <cgdis-portal-audit-person-tecid-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
    ></cgdis-portal-audit-person-tecid-column>

    <cgdis-portal-audit-person-cgdis-registration-number-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-person-cgdis-registration-number-column>


    <cgdis-portal-audit-perm-config-dpce-copy-deployment-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-copy-deployment-name-column>

    <cgdis-portal-audit-perm-config-dpce-copy-entity-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-copy-entity-name-column>

    <cgdis-portal-audit-perm-config-dpce-copy-toentity-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-copy-toentity-name-column>


    <cgdis-portal-audit-perm-config-dpce-hour-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [headerKey]="'audit.permamonitor.configdpcecopy.starthour'"
      [columnName]="'startHour'"
      [fieldName]="'startHour'"
      [filterName]="'permConfigDpceCopyStartHour'"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-hour-column>


    <cgdis-portal-audit-perm-config-dpce-hour-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [headerKey]="'audit.permamonitor.configdpcecopy.endhour'"
      [columnName]="'endHour'"
      [fieldName]="'endHour'"
      [filterName]="'permConfigDpceCopyEndHour'"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-hour-column>

    <cgdis-portal-audit-perm-config-dpce-hour-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [headerKey]="'audit.permamonitor.configdpcecopy.tostarthour'"
      [columnName]="'toStartHour'"
      [fieldName]="'toStartHour'"
      [filterName]="'permConfigDpceCopyToStartHour'"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-hour-column>

    <cgdis-portal-audit-perm-config-dpce-hour-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [headerKey]="'audit.permamonitor.configdpcecopy.toendhour'"
      [columnName]="'toEndHour'"
      [fieldName]="'toEndHour'"
      [filterName]="'permConfigDpceCopyToEndHour'"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-hour-column>


    <cgdis-portal-audit-action-type-column
      cgdisDatatableColumn
      [auditService]="auditService"
      [auditTypes]="[AuditTypeEnum.PERM_CONFIGDPCE_COPY]"
    ></cgdis-portal-audit-action-type-column>




    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-perm-config-dpce-copy-detail
          [audit]="row"></cgdis-portal-audit-management-perm-config-dpce-copy-detail>
      </div>
    </ng-template>


  </cgdis-portal-cgdisdatatable>

</div>


