<ep-datatable-column [flexGrow]="3" [columnName]="'toEntityName'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.permamonitor.configdpcecopy.toentityname'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row)?.toEntity?.name | defaultValue }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-entity-filter class="cgdis-portal-datatable-select-filter"
                                [entityPermission]="['ROLE_PERMISSION_ADMIN_AUDIT']"
                                [underDisable]="true"
                                [placeHolder]="''"
                                [allowClear]="true"
                                (filterSelected)="selectEntity($event)"
    ></cgdis-portal-entity-filter>
  </ng-template>
</ep-datatable-column>
