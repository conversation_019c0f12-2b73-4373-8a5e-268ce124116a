<div class="accordion__panel">


  <cgdis-portal-cgdisdatatable
    [datatableService]="auditService"
    [id]="'audit-perm-config-dpce-list'"
    [sorts]="[{dir:'desc',prop:'tecid'}]"
    [autoMobileFilters]="true"
    [class]="'entity__table'">

    <cgdis-portal-audit-action-date-column
      cgdisDatatableColumn
      [dateFormControl]="dateFormControl"
      [auditService]="auditService">
    </cgdis-portal-audit-action-date-column>

    <cgdis-portal-audit-person-tecid-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
    ></cgdis-portal-audit-person-tecid-column>

    <cgdis-portal-audit-person-cgdis-registration-number-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-person-cgdis-registration-number-column>

    <cgdis-portal-audit-perm-config-dpce-deployment-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-deployment-name-column>


    <cgdis-portal-audit-perm-config-dpce-entity-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-entity-name-column>


    <cgdis-portal-audit-perm-category-name-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-category-name-column>

    <cgdis-portal-audit-perm-config-dpce-day-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
    ></cgdis-portal-audit-perm-config-dpce-day-column>

    <cgdis-portal-audit-perm-config-dpce-hour-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditService"
      [fieldName]="'startHour'"
      [filterName]="'permConfigDpceStartHour'"
      [columnName]="'startHour'"
      [headerKey]="'audit.permamonitor.configdpce.starthour'"
    ></cgdis-portal-audit-perm-config-dpce-hour-column>


    <cgdis-portal-audit-action-type-column
      cgdisDatatableColumn
      [auditService]="auditService"
      [auditTypes]="[AuditTypeEnum.PERM_CONFIG_DPCE]"
    ></cgdis-portal-audit-action-type-column>


    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-perm-config-dpce-detail-component
          [audit]="row"></cgdis-portal-audit-management-perm-config-dpce-detail-component>
      </div>
    </ng-template>


  </cgdis-portal-cgdisdatatable>

</div>
