import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { SharedModule } from '@app/common/shared/shared.module';
import {
  AuditModel,
  AuditPermConfigDpceCopyModel,
} from '@app/model/audit/audit.model';
import { EpDatatableModule, SearchOperator } from '@eportal/components';
import { Entity } from '@app/model/entity.model';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'cgdis-portal-audit-perm-config-dpce-copy-toentity-name-column',
  standalone: true,
  templateUrl:
    './audit-perm-config-dpce-copy-to-entity-name-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPermConfigDpceCopyToEntityNameColumnComponent,
      ),
    },
  ],
  imports: [
    SharedModule,
    EpDatatableModule,
    DatatableModule,
    EntityFilterModule,
  ],
})
export class AuditPermConfigDpceCopyToEntityNameColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermConfigDpceCopyModel>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditModel>;

  private toEntityFormControl = new FormControl<number>(undefined);

  ngOnInit(): void {
    this.auditService.addFilterWithFormControl(
      'permConfigDpceCopyToEntityTecid',
      this.toEntityFormControl,
      {
        operator: SearchOperator.eq,
      },
    );
  }

  selectEntity($event: { entity: Entity; allUnderEntity: boolean }) {
    this.toEntityFormControl.setValue($event?.entity?.tecid);
  }
}
