import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditPermConfigDpceCopyModel } from '@app/model/audit/audit.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { FormControl } from '@angular/forms';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';

@Component({
  selector: 'cgdis-portal-audit-perm-config-dpce-deployment-name-column',
  templateUrl: './audit-perm-config-dpce-deployment-name-column.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPermConfigDpceDeploymentNameColumnComponent,
      ),
    },
  ],
  imports: [SharedModule, EpDatatableModule, DatatableModule],
})
export class AuditPermConfigDpceDeploymentNameColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermConfigDpceCopyModel>
  implements OnInit
{
  @Input() auditService: AuditManagementService;
  deploymentPlanNameFormControl: FormControl<string>;
  deploymentPlanNamefilterConfig: FilterConfig;

  ngOnInit(): void {
    this.deploymentPlanNameFormControl = new FormControl<string>(undefined);
    this.deploymentPlanNamefilterConfig = {
      operator: SearchOperator.like,
    };
  }
}
