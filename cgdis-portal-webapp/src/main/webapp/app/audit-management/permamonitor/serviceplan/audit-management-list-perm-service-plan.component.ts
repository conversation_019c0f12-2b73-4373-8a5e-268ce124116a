import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AuditPermServicePlanCategoryModel } from '@app/model/audit/audit.model';
import { AuditManagementListPermServicePlanService } from '@app/audit-management/permamonitor/serviceplan/audit-management-list-perm-service-plan.service';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementPermServicePlanDetailComponentComponent } from '@app/audit-management/permamonitor/detail/serviceplan/audit-management-perm-service-plan-detail-component.component';
import {
  AuditActionTypeEnum,
  AuditTypeEnum,
} from '@app/model/audit/audit.enum';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditPermServicePlanDeploymentPlanColumnComponent } from '@app/audit-management/permamonitor/serviceplan/columns/audit-perm-service-plan-deployment-plan-column.component';
import { AuditPermServicePlanNameColumnComponent } from '@app/audit-management/permamonitor/serviceplan/columns/audit-perm-service-plan-name-column.component';
import { AuditPermServicePlanCategoryNameColumnComponent } from '@app/audit-management/permamonitor/serviceplan/columns/audit-perm-service-plan-category-name-column.component';
import { AuditPermServicePlanSubcategoryNameColumnComponent } from '@app/audit-management/permamonitor/serviceplan/columns/audit-perm-service-plan-subcategory-name-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-perm-service-plan',
  templateUrl: './audit-management-list-perm-service-plan.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementPermServicePlanDetailComponentComponent,
    AuditManagementModule,
    AuditPermServicePlanDeploymentPlanColumnComponent,
    AuditPermServicePlanNameColumnComponent,
    AuditPermServicePlanCategoryNameColumnComponent,
    AuditPermServicePlanSubcategoryNameColumnComponent,
    AuditPermServicePlanSubcategoryNameColumnComponent,
  ],
  providers: [AuditManagementListPermServicePlanService],
})
export class AuditManagementListPermServicePlanComponent
  implements OnInit, OnDestroy
{
  @Input() dateFormControl: UntypedFormControl;
  actionTypes: FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>[];

  showFilter = false;
  isMobile: boolean = false;

  numberOfFilters: number;

  servicePlanNameFormControl = new FormControl<string>(undefined);
  servicePlanNamefilterConfig: FilterConfig = { operator: SearchOperator.like };
  servicePlanCategoryNameFormControl = new FormControl<string>(undefined);
  servicePlanNameCategoryfilterConfig: FilterConfig = {
    operator: SearchOperator.like,
  };
  servicePlanSubcategoryNameFormControl = new FormControl<string>(undefined);
  servicePlanNameSubcategoryfilterConfig: FilterConfig = {
    operator: SearchOperator.like,
  };
  servicePlanDeploymentPlanNameFormControl = new FormControl<string>(undefined);
  servicePlanDeploymentPlanNamefilterConfig: FilterConfig = {
    operator: SearchOperator.like,
  };
  yesNoValues: FieldOption<boolean>[];
  private subscriptions: Subscription[] = [];

  constructor(
    public auditService: AuditManagementListPermServicePlanService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
    private auditManagementService: AuditManagementService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  cast(row: any): AuditPermServicePlanCategoryModel {
    return row as AuditPermServicePlanCategoryModel;
  }

  ngOnInit() {
    this.loadTypes();
    this.numberOfFilters = 0;

    this.yesNoValues = [
      new FieldOption({ I18NLabel: 'default.yes', value: true }),
      new FieldOption({ I18NLabel: 'default.no', value: false }),
    ];

    this.subscriptions.push(
      this.auditService
        .canExecuteFirstSearch()
        .subscribe(() => this.updateFilterNumber()),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditService.getNumberOfFilters();
  }

  private loadTypes(): void {
    const auditTypes = [AuditTypeEnum.PERM_SERVICE_PLAN];
    this.actionTypes =
      this.auditManagementService.getFieldOptionActionTypes(auditTypes);
  }

  protected readonly AuditTypeEnum = AuditTypeEnum;
}
