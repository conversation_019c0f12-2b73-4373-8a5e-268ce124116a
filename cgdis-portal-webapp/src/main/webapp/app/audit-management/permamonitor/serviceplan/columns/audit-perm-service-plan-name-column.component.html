<ep-datatable-column [flexGrow]="3" [columnName]="'permServicePlanName'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.permamonitor.serviceplan.name'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).servicePlanName | defaultValue }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-with-null-filter
      [customFormControl]="formControl"
      [filterName]="'permServicePlanName'"
      [filterConfig]="filterConfig"
      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
  </ng-template>

</ep-datatable-column>
