import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditPermServicePlanCategoryModel } from '@app/model/audit/audit.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { FormControl } from '@angular/forms';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';

@Component({
  selector: 'cgdis-portal-audit-perm-service-plan-deployment-plan-column',
  templateUrl:
    './audit-perm-service-plan-deployment-plan-column.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPermServicePlanDeploymentPlanColumnComponent,
      ),
    },
  ],
  imports: [SharedModule, EpDatatableModule, DatatableModule],
})
export class AuditPermServicePlanDeploymentPlanColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermServicePlanCategoryModel>
  implements OnInit
{
  @Input() auditService: AuditManagementService;

  formControl: FormControl<string>;
  filterConfig: FilterConfig;

  ngOnInit(): void {
    this.formControl = new FormControl<string>(undefined);
    this.filterConfig = {
      operator: SearchOperator.like,
    };
  }
}
