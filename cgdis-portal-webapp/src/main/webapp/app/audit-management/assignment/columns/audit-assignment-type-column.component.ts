import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { CgdisDataGridViewColumnComponent } from '@app/common/modules/datatable/models/cgdis-datagrid-view-column-component.model';
import { CgdisDatatableSearch } from '@app/common/modules/datatable/models/cgdis-datatable-search.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-assignment-type-column',
  standalone: true,
  template: `{{ value }}`,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditAssignmentTypeColumnComponent extends CgdisDataGridViewColumnComponent {
  auditService = input<CgdisDatatableService<any, any>>();

  override get value(): string {
    const type = this.row?.type;
    return type ?? '-';
  }

  override get searchableValue(): string {
    return this.value;
  }

  override get defaultData(): CgdisDatatableSearch {
    return {
      label: 'assignment.type',
      field: 'type',
      type: 'string',
    };
  }
}