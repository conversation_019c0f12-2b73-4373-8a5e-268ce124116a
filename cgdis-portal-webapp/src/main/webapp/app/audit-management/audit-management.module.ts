import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { RouterModule } from '@angular/router';

import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { DatetimeModule } from '@eportal/core';
import { SimpleTableModule } from '@app/common/modules/simple-table/simple-table.module';
import { AuditManagementComponent } from '@app/audit-management/audit-management.component';
import { AuditManagementDetailComponent } from '@app/audit-management/detail/audit-management-detail.component';
import { AuditManagementListComponent } from '@app/audit-management/list/audit-management-list.component';
import { AUDIT_MANAGEMENT_ROUTES } from '@app/audit-management/audit-management.route';
import { NgxSelectModule } from 'ngx-select-ex';
import { AuditManagementServicePlanDetailComponent } from '@app/audit-management/detail/service-plan/audit-management-service-plan-detail.component';
import { AuditManagementModelDetailComponent } from '@app/audit-management/detail/model/audit-management-model-detail.component';
import { AuditManagementPrestationDetailComponent } from '@app/audit-management/detail/prestation/audit-management-prestation-detail.component';
import { AuditManagementServicePlanVersionDetailComponent } from '@app/audit-management/detail/service-plan-version/audit-management-service-plan-version-detail.component';
import { AuditManagementModelVersionDetailComponent } from '@app/audit-management/detail/model-version/audit-management-model-version-detail.component';
import { AuditManagementCopyPrestationDetailComponent } from '@app/audit-management/detail/copy-prestation/audit-management-copy-prestation-detail.component';
import { AuditManagementListPrestationComponent } from '@app/audit-management/list/prestation/audit-management-list-prestation.component';
import { AuditManagementListServicePlanComponent } from '@app/audit-management/list/service_plan/audit-management-list-service-plan.component';
import { AuditManagementListModelComponent } from '@app/audit-management/list/model/audit-management-list-model.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTabsModule } from '@angular/material/tabs';
import { AuditManagementSlotDetailComponent } from './detail/timeslot/audit-management-slot-detail.component';
import { ReactiveFormsModule } from '@angular/forms';
import { AuditManagementListLogasComponent } from './list/logas/audit-management-list-logas.component';
import { AuditManagementListAllowanceComponent } from '@app/audit-management/list/allowance/audit-management-list-allowance.component';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { TabsHeadersComponent } from '@app/common/modules/tabs-headers/tabs-headers.component';
import { AuditPersonTecidColumnComponent } from '@app/audit-management/common/colums/audit-person-tecid-column.component';
import { AuditPersonCgdisRegistrationNumberColumnComponent } from '@app/audit-management/common/colums/audit-person-cgdis-registration-number-column.component';
import { AuditActionDateColumnComponent } from './common/colums/audit-action-date-column.component';
import { AuditActionTypeColumnComponent } from '@app/audit-management/common/colums/audit-action-type-column.component';
import { AuditServicePlanColumnComponent } from '@app/audit-management/common/colums/audit-service-plan-column.component';
import { AuditPrestationStartColumnComponent } from '@app/audit-management/list/prestation/columns/audit-prestation-start-column.component';
import { AuditPrestationEndColumnComponent } from '@app/audit-management/list/prestation/columns/audit-prestation-end-column.component';
import { AuditPrestationPositionColumnComponent } from '@app/audit-management/list/prestation/columns/audit-prestation-position-column.component';
import { AuditPrestationPersonColumnComponent } from '@app/audit-management/list/prestation/columns/audit-prestation-person-column.component';
import { AuditManagementListPrestationCopyComponent } from '@app/audit-management/list/prestationcopy/audit-management-list-prestationcopy.component';
import { AuditCopyPrestationStartColumnComponent } from '@app/audit-management/list/prestationcopy/columns/audit-copyprestation-start-column.component';
import { AuditCopyPrestationEndColumnComponent } from '@app/audit-management/list/prestationcopy/columns/audit-copyprestation-end-column.component';
import { AuditManagementListSlotComponent } from '@app/audit-management/list/slot/audit-management-list-slot.component';
import { AuditSlotStartColumnComponent } from '@app/audit-management/list/slot/columns/audit-slot-start-column.component';
import { AuditSlotEndColumnComponent } from '@app/audit-management/list/slot/columns/audit-slot-end-column.component';
import { AuditSlotTargetStartColumnComponent } from '@app/audit-management/list/slot/columns/audit-slot-target-start-column.component';
import { AuditSlotTargetEndColumnComponent } from '@app/audit-management/list/slot/columns/audit-slot-target-end-column.component';
import { AuditPrestationPersonNameColumnComponent } from '@app/audit-management/list/prestation/columns/audit-prestation-person-name-column.component';

@NgModule({
  imports: [
    SharedModule,
    PageTemplateModule,
    RouterModule.forChild(AUDIT_MANAGEMENT_ROUTES),
    DatatableModule,
    EpDatatableModule,
    DatetimeModule,
    SimpleTableModule,
    NgxSelectModule,
    MatExpansionModule,
    MatTabsModule,
    ReactiveFormsModule,
    EntityFilterModule,
    TabsHeadersComponent,
  ],
  declarations: [
    AuditManagementComponent,
    AuditManagementListComponent,
    AuditManagementListPrestationComponent,
    AuditManagementListServicePlanComponent,
    AuditManagementListModelComponent,
    AuditManagementDetailComponent,
    AuditManagementServicePlanDetailComponent,
    AuditManagementServicePlanVersionDetailComponent,
    AuditManagementModelVersionDetailComponent,
    AuditManagementCopyPrestationDetailComponent,
    AuditManagementModelDetailComponent,
    AuditManagementPrestationDetailComponent,
    AuditManagementSlotDetailComponent,
    AuditManagementListLogasComponent,
    AuditManagementListAllowanceComponent,
    AuditPersonTecidColumnComponent,
    AuditPersonCgdisRegistrationNumberColumnComponent,
    AuditActionDateColumnComponent,
    AuditActionTypeColumnComponent,
    AuditServicePlanColumnComponent,
    AuditPrestationStartColumnComponent,
    AuditPrestationEndColumnComponent,
    AuditPrestationPositionColumnComponent,
    AuditPrestationPersonColumnComponent,
    AuditManagementListPrestationCopyComponent,
    AuditCopyPrestationStartColumnComponent,
    AuditCopyPrestationEndColumnComponent,
    AuditManagementListSlotComponent,
    AuditSlotStartColumnComponent,
    AuditSlotEndColumnComponent,
    AuditSlotTargetStartColumnComponent,
    AuditSlotTargetEndColumnComponent,
    AuditPrestationPersonNameColumnComponent,
  ],
  providers: [AuditManagementService],
  exports: [
    AuditPersonTecidColumnComponent,
    AuditPersonCgdisRegistrationNumberColumnComponent,
    AuditActionDateColumnComponent,
    AuditActionTypeColumnComponent,
  ],
})
export class AuditManagementModule {}
