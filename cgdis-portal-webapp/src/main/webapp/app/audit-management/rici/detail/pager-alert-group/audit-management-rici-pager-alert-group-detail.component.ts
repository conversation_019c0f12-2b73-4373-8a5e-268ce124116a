import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditRiciPagerAlertGroupModel } from '@app/model/audit/audit-rici-pager-alert-group.model';
import { SharedModule } from '@app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { SimplePopupService } from '@eportal/components';

@Component({
  selector: 'cgdis-portal-audit-management-rici-pager-alert-group-detail',
  templateUrl: './audit-management-rici-pager-alert-group-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [SharedModule, TranslateModule],
})
export class AuditManagementRiciPagerAlertGroupDetailComponent {
  @Input() audit: AuditRiciPagerAlertGroupModel;

  constructor(private popupService: SimplePopupService) {}

  showDetails() {
    this.popupService.openModal({
      title: 'audit.rici.pager.alert.group.detail.title',
      content: this.getDetailContent(),
      size: 'lg',
    });
  }

  private getDetailContent(): string {
    return `
      <div class="row">
        <div class="col-md-6">
          <h6>Person Information</h6>
          <p><strong>Name:</strong> ${this.audit.personName || 'N/A'}</p>
          <p><strong>CGDIS Registration Number:</strong> ${this.audit.personCgdisRegistrationNumber || 'N/A'}</p>
          <p><strong>Person TECID:</strong> ${this.audit.personTecid || 'N/A'}</p>
        </div>
        <div class="col-md-6">
          <h6>Alert Group Information</h6>
          <p><strong>Alert Group Name:</strong> ${this.audit.alertGroupName || 'N/A'}</p>
          <p><strong>Description:</strong> ${this.audit.alertGroupDescription || 'N/A'}</p>
          <p><strong>Alert Group TECID:</strong> ${this.audit.alertGroupTecid || 'N/A'}</p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <h6>RIC Schema Information</h6>
          <p><strong>Schema Alias:</strong> ${this.audit.riciRicSchemaAlias || 'N/A'}</p>
          <p><strong>Schema TECID:</strong> ${this.audit.riciRicSchemaTecid || 'N/A'}</p>
        </div>
        <div class="col-md-6">
          <h6>RIC Range Information</h6>
          <p><strong>Range Name:</strong> ${this.audit.riciRicRangeName || 'N/A'}</p>
          <p><strong>Range TECID:</strong> ${this.audit.riciRicRangeTecid || 'N/A'}</p>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <h6>Pager Information</h6>
          <p><strong>Pager ID:</strong> ${this.audit.pagerPagerId || 'N/A'}</p>
          <p><strong>Serial Number:</strong> ${this.audit.pagerSerialNumber || 'N/A'}</p>
          <p><strong>Pager TECID:</strong> ${this.audit.pagerTecid || 'N/A'}</p>
        </div>
        <div class="col-md-6">
          <h6>Action Information</h6>
          <p><strong>Action:</strong> ${this.audit.alertGroupValue ? 'Assigned to Alert Group' : 'Removed from Alert Group'}</p>
          <p><strong>Action Type:</strong> ${this.audit.actionType || 'N/A'}</p>
          <p><strong>Action Date:</strong> ${this.audit.actionDatetime ? new Date(this.audit.actionDatetime.toString()).toLocaleString() : 'N/A'}</p>
          <p><strong>Performed by:</strong> ${this.audit.personTecid?.firstName || ''} ${this.audit.personTecid?.lastName || ''}</p>
        </div>
      </div>
    `;
  }
}
