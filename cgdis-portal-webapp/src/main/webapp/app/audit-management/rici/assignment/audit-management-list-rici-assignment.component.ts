import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementListRiciAssignmentService } from '@app/audit-management/rici/assignment/audit-management-list-rici-assignment.service';
import { AuditRiciAssignmentModel } from '@app/model/audit/audit-rici-assignment.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { UntypedFormControl } from '@angular/forms';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditEntityTecidColumnComponent } from '@app/audit-management/assignment/columns/audit-entity-tecid-column.component';
import { AuditAssignmentTypeColumnComponent } from '@app/audit-management/assignment/columns/audit-assignment-type-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-assignment',
  standalone: true,
  imports: [
    DatatableModule,
    EpDatatableModule,
    SharedModule,
    TranslateModule,
    AuditManagementModule,
    AuditEntityTecidColumnComponent,
    AuditAssignmentTypeColumnComponent,
  ],
  templateUrl: './audit-management-list-rici-assignment.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListRiciAssignmentService],
})
export class AuditManagementListRiciAssignmentComponent {
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  protected isMobile: boolean = false;
  protected readonly AuditTypeEnum = AuditTypeEnum;

  constructor(
    protected auditManagementListRiciAssignmentService: AuditManagementListRiciAssignmentService,
  ) {}

  cast(row: any): AuditRiciAssignmentModel {
    return row as AuditRiciAssignmentModel;
  }
}
