<div class="accordion__panel">

  <cgdis-portal-cgdisdatatable
    [autoMobileFilters]="true"
    [class]="'entity__table'"
    [datatableService]="auditManagementListRiciAssignmentService"
    [id]="'assignment-list'"
    [showDetails]="'MOBILE'"
    [sorts]="[{dir:'desc',prop:'tecid'}]">

    <cgdis-portal-audit-action-date-column
      [auditService]="auditManagementListRiciAssignmentService"
      [dateFormControl]="dateFormControl"
      cgdisDatatableColumn>
    </cgdis-portal-audit-action-date-column>

    <cgdis-portal-audit-person-tecid-column
      [columnHiddenMobile]="true"
      cgdisDatatableColumn
    ></cgdis-portal-audit-person-tecid-column>

    <cgdis-portal-audit-person-cgdis-registration-number-column
      [auditService]="auditManagementListRiciAssignmentService"
      [columnHiddenMobile]="true"
      cgdisDatatableColumn
    ></cgdis-portal-audit-person-cgdis-registration-number-column>

    <cgdis-portal-audit-entity-tecid-column
      [auditService]="auditManagementListRiciAssignmentService"
      [columnHiddenMobile]="true"
      cgdisDatatableColumn
    ></cgdis-portal-audit-entity-tecid-column>

    <cgdis-portal-audit-assignment-type-column
      [auditService]="auditManagementListRiciAssignmentService"
      cgdisDatatableColumn
    ></cgdis-portal-audit-assignment-type-column>

    <cgdis-portal-audit-action-type-column
      [auditService]="auditManagementListRiciAssignmentService"
      [auditTypes]="[AuditTypeEnum.ASSIGNMENT]"
      cgdisDatatableColumn
    ></cgdis-portal-audit-action-type-column>
  </cgdis-portal-cgdisdatatable>
</div>
