<mat-accordion>
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.simcard.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-sim-card
          [actionTypes]="actionTypes"
          [dateFormControl]="dateFormControl">
        </cgdis-portal-audit-management-list-rici-sim-card>
      </div>
    </ng-template>
  </mat-expansion-panel>
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.ricrange.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-ric-range
          [actionTypes]="actionTypes"
          [dateFormControl]="dateFormControl">
        </cgdis-portal-audit-management-list-rici-ric-range>
      </div>
    </ng-template>
  </mat-expansion-panel>
  <!-- Pager Audit Panel -->
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.pager.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-pager
          [dateFormControl]="dateFormControl"
        >
        </cgdis-portal-audit-management-list-rici-pager>
      </div>
    </ng-template>
  </mat-expansion-panel>
  <!-- Schema Audit Panel -->
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.schema.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-schema
          [actionTypes]="actionTypes"
          [dateFormControl]="dateFormControl">
        </cgdis-portal-audit-management-list-rici-schema>
      </div>
    </ng-template>
  </mat-expansion-panel>

  <!-- Alert Group Audit Panel -->
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.alert-group.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-alert-group
          [actionTypes]="actionTypes"
          [dateFormControl]="dateFormControl">
        </cgdis-portal-audit-management-list-rici-alert-group>
      </div>
    </ng-template>
  </mat-expansion-panel>

  <!-- New: SIM Card Import Audit Panel -->
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.simcard.import.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-sim-card-import
          [actionTypes]="actionTypes"
          [dateFormControl]="dateFormControl">
        </cgdis-portal-audit-management-list-rici-sim-card-import>
      </div>
    </ng-template>
  </mat-expansion-panel>

  <!-- New: Pager Import Audit Panel -->
  <mat-expansion-panel [expanded]="true" class="accordion__group">
    <mat-expansion-panel-header class="general-information-header" role="heading">
      <mat-panel-title class="mb-0">
        <span [translate]="'audit.rici.pager.import.title'" class="accordion__title"></span>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="card-body">
        <cgdis-portal-audit-management-list-rici-pager-import
          [actionTypes]="actionTypes"
          [dateFormControl]="dateFormControl">
        </cgdis-portal-audit-management-list-rici-pager-import>
      </div>
    </ng-template>
  </mat-expansion-panel>
</mat-accordion>
