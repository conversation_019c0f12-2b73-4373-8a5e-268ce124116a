<div class="accordion__panel">

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }}
      )</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.actionDateTime'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()"
                                                  class="informations-datepicker-filter"
                                                  [customFormControl]="dateFormControl"
                                                  [initialValue]="dateFormControl.value"
                                                  [filterName]="'actionDate'"
                                                  [datatableService]="auditService"
        ></cgdis-portal-datatable-datepicker-filter>
      </div>

      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.actionType.title'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                              [possibleValues]="actionTypes"
                                              [filterName]="'actionType'"
                                              [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
      </div>

      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.iccid'"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="iccidFormControl"
                                                      [filterName]="'riciSimCardIccid'"
                                                      [filterConfig]="iccidFilterConfig"
                                                      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
      </div>

      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.msisdn'"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="msisdnFormControl"
                                                      [filterName]="'riciSimCardMsisdn'"
                                                      [filterConfig]="msisdnFilterConfig"
                                                      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
      </div>

      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.status'"></label>
        <cgdis-portal-datatable-select-filter
          (onValueChanged)="updateFilterNumber()"
          [customFormControl]="statusFormControl"
          [filterName]="'riciSimCardStatus'"
          [filterConfig]="statusFilterConfig"
          [datatableService]="auditService">

        </cgdis-portal-datatable-select-filter>
      </div>

      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.associatedPagerId'"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="pagerIdFormControl"
                                                      [filterName]="'associatedPagerId'"
                                                      [filterConfig]="pagerIdFilterConfig"
                                                      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [datatableService]="auditService"
    [id]="'audit-rici-sim-card-list'"
    [sorts]="[{dir:'desc',prop:'tecid'}]"
    [showDetails]="isMobile"
    [class]="'entity__table'">

    <!-- Action date time -->
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionDateTime'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        {{ cast(context.row).actionDatetime | dateTimeFormat }}
      </ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-datepicker-filter
          [customFormControl]="dateFormControl"
          [initialValue]="dateFormControl.value"
          [filterName]="'actionDate'"
          [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- User -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.lastName'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.person'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <span
          class="text-wrap">{{ cast(context.row).personTecid.lastName }} {{ cast(context.row).personTecid.firstName }}</span>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.cgdisregistrationnumber'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        {{ cast(context.row).personTecid.cgdisRegistrationNumber }}
      </ng-template>

      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [filterName]="'personTecid.cgdisRegistrationNumber'"
          [allowClear]="true"
          [datatableService]="auditService">
        </cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- ICCID -->
    <ep-datatable-column *ngIf="!isMobile" [flexGrow]="3" [columnName]="'ICCID'" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.simcard.iccid'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).iccid | defaultValue }}</div>
      </ng-template>

      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [customFormControl]="iccidFormControl"
          [filterName]="'riciSimCardIccid'"
          [filterConfig]="iccidFilterConfig"
          [allowClear]="true"
          [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- MSISDN -->
    <ep-datatable-column *ngIf="!isMobile" [flexGrow]="3" [columnName]="'msisdn'" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.simcard.msisdn'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).msisdn | defaultValue }}</div>
      </ng-template>

      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [customFormControl]="msisdnFormControl"
          [filterName]="'riciSimCardMsisdn'"
          [filterConfig]="msisdnFilterConfig"
          [allowClear]="true"
          [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Status -->
    <ep-datatable-column *ngIf="!isMobile" [flexGrow]="3" [columnName]="'status'" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.simcard.status'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).status | defaultValue }}</div>
      </ng-template>

      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-select-filter
          [customFormControl]="statusFormControl"
          [filterName]="'riciSimCardStatus'"
          [filterConfig]="statusFilterConfig"
          [possibleValues]="ricSimStatuses"
          [allowClear]="true"
          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Associated Pager ID -->
    <ep-datatable-column *ngIf="!isMobile" [flexGrow]="3" [columnName]="'associatedPagerId'" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.simcard.associatedPagerId'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).associatedPagerId | defaultValue }}</div>
      </ng-template>

      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [customFormControl]="pagerIdFormControl"
          [filterName]="'associatedPagerId'"
          [allowClear]="true"
          [filterConfig]="pagerIdFilterConfig"
          [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Action type -->
    <ep-datatable-column [columnName]="'actionType'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionType.title'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <span class="text-wrap">{{ 'audit.actionType.' + cast(context.row).actionType | translate }}</span>
      </ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-select-filter
          [allowClear]="true"
          [possibleValues]="actionTypes"
          [filterName]="'actionType'"
          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-sim-card-detail
          [audit]="row"></cgdis-portal-audit-management-rici-sim-card-detail>
      </div>
    </ng-template>

  </cgdis-portal-cgdisdatatable>

</div>
