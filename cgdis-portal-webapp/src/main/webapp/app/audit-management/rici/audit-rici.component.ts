import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import {
  MatAccordion,
  MatExpansionModule,
  MatExpansionPanel,
  MatExpansionPanelContent,
  MatExpansionPanelTitle,
} from '@angular/material/expansion';
import { UntypedFormControl } from '@angular/forms';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { DateService } from '@eportal/core';
import { AuditActionTypeEnum } from '@app/model/audit/audit.enum';
import _ from 'lodash';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { TranslateModule } from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { AuditManagementListRiciSimCardComponent } from '@app/audit-management/rici/simcard/audit-management-list-rici-sim-card.component';
import { AuditManagementListRiciRicRangeComponent } from '@app/audit-management/rici/ricrange/audit-management-list-rici-ric-range.component';
import { AuditManagementListRiciPagerComponent } from '@app/audit-management/rici/pager/audit-management-list-rici-pager.component';
import { AuditManagementListRiciAlertGroupComponent} from '@app/audit-management/rici/alert-group/audit-management-list-rici-alert-group.component'; // Import Schema List Component
import { AuditManagementListRiciSchemaComponent } from '@app/audit-management/rici/schema/audit-management-list-rici-schema.component';
import { AuditManagementListRiciSimCardImportComponent } from '@app/audit-management/rici/simcard-import/audit-management-list-rici-sim-card-import.component'; // New import
import { AuditManagementListRiciPagerImportComponent } from '@app/audit-management/rici/pager-import/audit-management-list-rici-pager-import.component'; // New import
import { AuditManagementListRiciPagerAlertGroupComponent } from '@app/audit-management/rici/pager-alert-group/audit-management-list-rici-pager-alert-group.component'; // New import

@Component({
  selector: 'cgdis-portal-audit-rici',
  standalone: true,
  imports: [
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelContent,
    MatExpansionPanelTitle,
    AuditManagementModule,
    MatExpansionModule,
    MatTabsModule,
    TranslateModule,
    AuditManagementListRiciSimCardComponent,
    AuditManagementListRiciRicRangeComponent,
    AuditManagementListRiciPagerComponent,
    AuditManagementListRiciAlertGroupComponent,
    // Add Schema List Component
    AuditManagementListRiciSchemaComponent,
    AuditManagementListRiciSimCardImportComponent, // Add new component
    AuditManagementListRiciPagerImportComponent, // Add new component
    AuditManagementListRiciPagerAlertGroupComponent, // Add new component
  ],
  templateUrl: './audit-rici.component.html',
  styleUrl: './audit-rici.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditRiciComponent implements OnInit {
  dateFormControl = new UntypedFormControl();
  actionTypes: FieldOption<string>[];

  constructor(private dateService: DateService) {}

  ngOnInit(): void {
    this.dateFormControl.setValue(this.dateService.now());
    this.loadTypes();
  }

  private loadTypes(): void {
    let allAction: string[] = [];

    for (let n in AuditActionTypeEnum) {
      allAction.push(
        AuditActionTypeEnum[n as keyof typeof AuditActionTypeEnum],
      );
    }

    this.actionTypes = _.map(allAction, (oneType) => {
      return new FieldGroupOption({
        I18NLabel: 'audit.actionType.' + oneType,
        value: oneType,
      });
    });
  }
}
