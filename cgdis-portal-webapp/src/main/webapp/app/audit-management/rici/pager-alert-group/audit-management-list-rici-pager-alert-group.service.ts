import { Injectable } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { SimplePopupService } from '@eportal/components';
import { RestService } from '@eportal/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdis-datatable.service';
import { AuditRiciPagerAlertGroupModel } from '@app/model/audit/audit-rici-pager-alert-group.model';

/**
 * Service for managing datatable operations for RICI Pager Alert Group audit records.
 */
@Injectable()
export class AuditManagementListRiciPagerAlertGroupService extends CgdisDatatableService<AuditRiciPagerAlertGroupModel> {
    constructor(
        restService: RestService,
        fb: UntypedFormBuilder,
        location: Location,
        router: Router,
        route: ActivatedRoute,
        popupService: SimplePopupService
    ) {
        super(fb, location, router, route, popupService, true);
        // Initialize the data resource list to the new dedicated endpoint for Pager Alert Group audits
        super.initDataResourceList(restService.all('audits', 'rici', 'pager-alert-groups'));
    }
}
