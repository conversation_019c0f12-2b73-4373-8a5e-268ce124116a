<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          {{ 'audit.rici.pager.alert.group.title' | translate }}
          <span class="badge badge-secondary ml-2" *ngIf="numberOfFilters > 0">
            {{ numberOfFilters }}
          </span>
        </h5>
      </div>
      <div class="card-body">
        <ep-datatable
          [service]="auditPagerAlertGroupService"
          [filterConfigs]="filterConfigs"
          (filtersChanged)="updateFilterNumber()"
        >
          <ng-container epDatatableColumn="personName">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.person.name' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.personName }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="personCgdisRegistrationNumber">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.person.cgdis.registration.number' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.personCgdisRegistrationNumber }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="alertGroupName">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.alert.group.name' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.alertGroupName }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="riciRicSchemaAlias">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.schema.alias' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.riciRicSchemaAlias }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="riciRicRangeName">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.range.name' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.riciRicRangeName }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="alertGroupValue">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.action' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              <span class="badge" [ngClass]="audit.alertGroupValue ? 'badge-success' : 'badge-danger'">
                {{ (audit.alertGroupValue ? 'audit.rici.pager.alert.group.assigned' : 'audit.rici.pager.alert.group.removed') | translate }}
              </span>
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="pagerPagerId">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.rici.pager.alert.group.pager.id' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.pagerPagerId }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="actionDatetime">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.action.datetime' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.actionDatetime | datetime }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="personTecid">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.person.tecid' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ audit.personTecid?.firstName }} {{ audit.personTecid?.lastName }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="actionType">
            <ng-container *epDatatableColumnHeader>
              {{ 'audit.action.type' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              {{ ('audit.actionType.' + audit.actionType) | translate }}
            </ng-container>
          </ng-container>

          <ng-container epDatatableColumn="actions">
            <ng-container *epDatatableColumnHeader>
              {{ 'common.actions' | translate }}
            </ng-container>
            <ng-container *epDatatableColumnCell="let audit">
              <cgdis-portal-audit-management-rici-pager-alert-group-detail
                [audit]="audit"
              ></cgdis-portal-audit-management-rici-pager-alert-group-detail>
            </ng-container>
          </ng-container>
        </ep-datatable>
      </div>
    </div>
  </div>
</div>
