import { ChangeDetectionStrategy, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { AuditRiciPagerAlertGroupModel } from '@app/model/audit/audit-rici-pager-alert-group.model';
import { AuditManagementListRiciPagerAlertGroupService } from './audit-management-list-rici-pager-alert-group.service';
import { Subscription } from 'rxjs';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementRiciPagerAlertGroupDetailComponent } from '../detail/pager-alert-group/audit-management-rici-pager-alert-group-detail.component';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { ActivatedRoute } from '@angular/router';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-pager-alert-group',
  templateUrl: './audit-management-list-rici-pager-alert-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciPagerAlertGroupService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciPagerAlertGroupDetailComponent,
  ],
})
export class AuditManagementListRiciPagerAlertGroupComponent implements OnInit, OnDestroy {
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;

  numberOfFilters = 0;
  subscriptions: Subscription[] = [];

  filterConfigs: FilterConfig[] = [
    {
      field: 'personName',
      label: 'audit.rici.pager.alert.group.person.name',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
    {
      field: 'personCgdisRegistrationNumber',
      label: 'audit.rici.pager.alert.group.person.cgdis.registration.number',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
    {
      field: 'alertGroupName',
      label: 'audit.rici.pager.alert.group.alert.group.name',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
    {
      field: 'riciRicSchemaAlias',
      label: 'audit.rici.pager.alert.group.schema.alias',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
    {
      field: 'riciRicRangeName',
      label: 'audit.rici.pager.alert.group.range.name',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
    {
      field: 'pagerPagerId',
      label: 'audit.rici.pager.alert.group.pager.id',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
    {
      field: 'pagerSerialNumber',
      label: 'audit.rici.pager.alert.group.pager.serial.number',
      operator: SearchOperator.CONTAINS,
      type: 'text',
    },
  ];

  constructor(
    public auditPagerAlertGroupService: AuditManagementListRiciPagerAlertGroupService,
    private route: ActivatedRoute,
    private auditManagementService: AuditManagementService,
  ) {}

  ngOnInit(): void {
    this.auditManagementService.setCurrentAuditType(AuditTypeEnum.RICI_PAGER_ALERT_GROUP);

    this.subscriptions.push(
      this.auditPagerAlertGroupService
        .canExecuteFirstSearch()
        .subscribe(() => {
          this.updateFilterNumber();
          this.auditPagerAlertGroupService.search();
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditPagerAlertGroupService.getNumberOfFilters();
  }
}
