import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { AuditRiciPagerImportModel } from '@app/model/audit/audit-rici-pager-import.model';
import { AuditManagementListRiciPagerImportService } from '@app/audit-management/rici/pager-import/audit-management-list-rici-pager-import.service';
import { AuditManagementRiciPagerImportDetailComponent } from '@app/audit-management/rici/pager-import/detail/audit-management-rici-pager-import-detail.component';
import { PagerImportStatusEnum } from '@rici/common/enums/pager-import-status.enum';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-pager-import',
  templateUrl: './audit-management-list-rici-pager-import.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciPagerImportService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciPagerImportDetailComponent,
  ],
})
export class AuditManagementListRiciPagerImportComponent
  implements OnInit, OnDestroy
{
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile: boolean = false;
  numberOfFilters: number;

  fileNameFormControl = new FormControl<string>(undefined);
  fileNameFilterConfig = new FilterConfig({ operator: SearchOperator.like });
  importStatusFormControl = new FormControl<string>(undefined);
  importStatusFilterConfig = new FilterConfig({ operator: SearchOperator.eq });
  totalRecordsFormControl = new FormControl<number>(undefined);
  totalRecordsFilterConfig = new FilterConfig({ operator: SearchOperator.eq });
  successfulRecordsFormControl = new FormControl<number>(undefined);
  successfulRecordsFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
  validationErrorsFormControl = new FormControl<number>(undefined);
  validationErrorsFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
  importErrorsFormControl = new FormControl<number>(undefined);
  importErrorsFilterConfig = new FilterConfig({ operator: SearchOperator.eq });
  protected importStatusValues: FieldOption<PagerImportStatusEnum>[];
  private subscriptions: Subscription[] = [];

  constructor(
    public auditPagerImportService: AuditManagementListRiciPagerImportService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  cast(row: any): AuditRiciPagerImportModel {
    return row as AuditRiciPagerImportModel;
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.initImportStatusValues();
    this.subscriptions.push(
      this.auditPagerImportService.canExecuteFirstSearch().subscribe(() => {
        this.updateFilterNumber();
        // No specific URL params to pre-populate for import audits, but keeping the pattern
      }),
    );
  }

  initImportStatusValues() {
    this.importStatusValues = Object.values(PagerImportStatusEnum).map(
      (status) =>
        new FieldOption<PagerImportStatusEnum>({
          value: status,
          I18NLabel: `audit.rici.pager.import.statuses.${status}`,
        }),
    );

    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditPagerImportService.getNumberOfFilters();
  }
}
