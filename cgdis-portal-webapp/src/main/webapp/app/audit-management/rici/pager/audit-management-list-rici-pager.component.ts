import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { AuditManagementListRiciPagerService } from './audit-management-list-rici-pager.service'; // Import pager service
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AuditRiciPagerModel } from '@app/model/audit/audit.model'; // Import pager model
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementRiciPagerDetailComponent } from '../detail/pager/audit-management-rici-pager-detail.component'; // Import pager detail component
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { ActivatedRoute } from '@angular/router';
import {
  RiciPagerAssignmentType,
  RiciPagerStatus,
} from '@rici/common/enums/pager.enum';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { DatatablePersonFilterComponent } from '@rici/views/pagers/pagers-list/pagers-list-table/datatable-person-filter/datatable-person-filter.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-pager', // Update selector
  templateUrl: './audit-management-list-rici-pager.component.html', // Update template URL
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciPagerService], // Provide pager service
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciPagerDetailComponent,
    DatatablePersonFilterComponent,
    // Import pager detail component
  ],
})
export class AuditManagementListRiciPagerComponent
  implements OnInit, OnDestroy
{
  actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile: boolean = false;
  numberOfFilters: number;
  // --- Filter Form Controls for Pager ---
  pagerIdFormControl = new FormControl<string>(undefined);
  pagerIdFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
    inUrl: true, // Example: Allow filtering by pagerId via URL
  });
  serialNumberFormControl = new FormControl<string>(undefined);
  serialNumberFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  manufacturerFormControl = new FormControl<string>(undefined);
  manufacturerFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  modelFormControl = new FormControl<string>(undefined);
  modelFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  statusFormControl = new FormControl<string>(undefined);
  statusFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
  simIccidFormControl = new FormControl<string>(undefined);
  simIccidFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  assignmentTypeFormControl = new FormControl<string>(undefined);
  assignmentTypeFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
  assignedPersonFormControl = new FormControl<string>(undefined);
  assignedPersonFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  assignedEntityFormControl = new FormControl<string>(undefined);
  assignedEntityFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  individualRicFormControl = new FormControl<string>(undefined);
  individualRicFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  protected personFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
    inUrl: false,
  });
  protected entityFilterConfig: FilterConfig = {
    operator: SearchOperator.eq,
    inUrl: false,
  };
  protected pagerStatusValues: FieldOption<RiciPagerStatus>[];
  protected assignmentTypeValues: FieldOption<RiciPagerAssignmentType>[];
  // --- End Filter Form Controls ---
  private subscriptions: Subscription[] = [];
  private pagerId: string;

  constructor(
    public auditPagerService: AuditManagementListRiciPagerService, // Inject pager service
    private breakpointObserver: BreakpointObserver,
    private auditManagementService: AuditManagementService,
    private cd: ChangeDetectorRef,
    private route: ActivatedRoute,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  setPagerStatusValues() {
    this.pagerStatusValues = Object.values(RiciPagerStatus).map(
      (status) =>
        new FieldOption<RiciPagerStatus>({
          value: status,
          I18NLabel: `rici.pagers.status.${status}`,
        }),
    );

    this.cd.markForCheck();
  }

  setPagerAssignmentTypeValues() {
    this.assignmentTypeValues = Object.values(RiciPagerAssignmentType).map(
      (type) =>
        new FieldOption<RiciPagerAssignmentType>({
          value: type,
          I18NLabel: `rici.pagers.assignment-types.${type}`,
        }),
    );
  }

  // Update cast method for Pager model
  cast(row: any): AuditRiciPagerModel {
    return row as AuditRiciPagerModel;
  }

  setActionTypes() {
    this.actionTypes = this.auditManagementService.getFieldOptionActionTypes([
      AuditTypeEnum.RICI_PAGER,
    ]);
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.setPagerStatusValues();
    this.setPagerAssignmentTypeValues();
    this.setActionTypes();

    // Example: Handle URL parameter filtering for pagerId
    const urlParams = new URLSearchParams(window.location.search);
    const hashParams = new URLSearchParams(
      window.location.hash.split('?')[1] || '',
    );
    const pagerId = urlParams.get('pagerId') || hashParams.get('pagerId');
    if (pagerId) {
      this.pagerIdFormControl.setValue(pagerId);
      this.auditPagerService.addFilterWithFormControl(
        'pagerId', // Field name in AuditRiciPagerModel
        this.pagerIdFormControl,
        this.pagerIdFilterConfig,
      );
    }

    this.subscriptions.push(
      this.auditPagerService // Use pager service
        .canExecuteFirstSearch()
        .subscribe(() => {
          this.updateFilterNumber();
          this.auditPagerService.search();
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditPagerService.getNumberOfFilters(); // Use pager service
  }
}
