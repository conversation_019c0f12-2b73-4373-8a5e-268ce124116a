import { Injectable } from '@angular/core';
import {
  AuditActionTypeEnum,
  AuditTypeEnum,
} from '@app/model/audit/audit.enum';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';

@Injectable()
export class AuditManagementService {
  actionTypeFieldOptions: Map<
    AuditActionTypeEnum,
    FieldOption<AuditActionTypeEnum>
  > = new Map();
  private mappings: Map<AuditTypeEnum, AuditActionTypeEnum[]> = new Map();

  constructor() {
    this.init();
  }

  public getActionTypes(auditTypes: AuditTypeEnum[]): AuditActionTypeEnum[] {
    const actionTypes = new Set<AuditActionTypeEnum>();
    auditTypes.forEach((auditType) => {
      this.mappings.get(auditType).forEach((actionType) => {
        actionTypes.add(actionType);
      });
    });
    return Array.from(actionTypes);
  }

  public getFieldOptionActionTypes(
    auditTypes: AuditTypeEnum[],
  ): FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>[] {
    return auditTypes.map(
      (oneAuditType, index, array) =>
        new FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>({
          I18NLabel: 'audit.type.' + oneAuditType,
          value: oneAuditType,
          children: this.getActionTypes([oneAuditType]).map((oneActionType) =>
            this.createFieldOptionActionType(oneActionType),
          ),
        }),
    );
  }

  public getFieldOptionAuditTypes(
    types: AuditTypeEnum[],
  ): FieldOption<string>[] {
    return types.map((type) => {
      return new FieldGroupOption({
        I18NLabel: 'audit.type.' + type,
        value: type,
      });
    });
  }

  private init() {
    this.mappings.set(AuditTypeEnum.PRESTATION, [
      AuditActionTypeEnum.ADD,
      AuditActionTypeEnum.CLOSURE,
      AuditActionTypeEnum.DELETE,
      AuditActionTypeEnum.COPY_ADD,
      AuditActionTypeEnum.COPY_CLOSURE,
      AuditActionTypeEnum.COPY_DELETE,
      AuditActionTypeEnum.SPLIT_ADD,
      AuditActionTypeEnum.SPLIT_SPLITTED,
      AuditActionTypeEnum.MERGE_DELETED,
      AuditActionTypeEnum.MERGE_EXTENDED,
      AuditActionTypeEnum.TEAM_CLOSURE,
      AuditActionTypeEnum.TEAM_ADD,
      AuditActionTypeEnum.TEAM_DELETE,
      AuditActionTypeEnum.FULL_AV_REM_ADD,
      AuditActionTypeEnum.FULL_AV_REM_DELETE,
      AuditActionTypeEnum.FULL_AV_ADD,
      AuditActionTypeEnum.FULL_AV_DELETE,
      AuditActionTypeEnum.FULL_AV_CLOSURE,
    ]);

    this.mappings.set(AuditTypeEnum.COPY_PRESTATION, [
      AuditActionTypeEnum.DAY,
      AuditActionTypeEnum.WEEK,
      AuditActionTypeEnum.SLOT,
    ]);

    this.mappings.set(AuditTypeEnum.SLOT, [
      AuditActionTypeEnum.SPLIT,
      AuditActionTypeEnum.MERGE,
    ]);

    this.mappings.set(AuditTypeEnum.PDS, [
      AuditActionTypeEnum.CLOSURE,
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.DELETE,
    ]);

    this.mappings.set(AuditTypeEnum.VERSION_PDS, [
      AuditActionTypeEnum.CLOSURE,
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.DELETE,
      AuditActionTypeEnum.COPY,
    ]);

    this.mappings.set(AuditTypeEnum.MODEL, [
      AuditActionTypeEnum.CLOSURE,
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.DELETE,
      AuditActionTypeEnum.COPY,
    ]);

    this.mappings.set(AuditTypeEnum.VERSION_MODEL, [
      AuditActionTypeEnum.CLOSURE,
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.DELETE,
      AuditActionTypeEnum.COPY,
    ]);

    this.mappings.set(AuditTypeEnum.LOGAS, [
      AuditActionTypeEnum.START,
      AuditActionTypeEnum.END,
    ]);

    this.mappings.set(AuditTypeEnum.ALLOWANCE_CONFIG, [
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.DELETE,
    ]);

    this.mappings.set(AuditTypeEnum.PERM_DEPLOYMENT_PLAN, [
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.ADD,
    ]);

    this.mappings.set(AuditTypeEnum.PERM_SERVICE_PLAN, [
      AuditActionTypeEnum.BOOKMARKED_UPDATE,
      AuditActionTypeEnum.PERM_CATEGORY_UPDATE,
      AuditActionTypeEnum.PERM_SUBCAT_UPDATE,
    ]);

    this.mappings.set(AuditTypeEnum.PERM_CONFIG_DPCE, [
      AuditActionTypeEnum.UPDATE,
      AuditActionTypeEnum.COPY,
      AuditActionTypeEnum.COPY_ENTITY,
    ]);

    this.mappings.set(AuditTypeEnum.PERM_CONFIGDPCE_COPY, [
      AuditActionTypeEnum.COPY,
      AuditActionTypeEnum.COPY_ENTITY,
    ]);

    this.mappings.set(AuditTypeEnum.RICI_ALERT_GROUP, [
      AuditActionTypeEnum.DELETE,
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
    ]);

    this.mappings.set(AuditTypeEnum.RICI_PAGER, [
      AuditActionTypeEnum.DELETE,
      AuditActionTypeEnum.CREATE,
      AuditActionTypeEnum.UPDATE,
    ]);
  }

  private createFieldOptionActionType(
    actionType: AuditActionTypeEnum,
  ): FieldOption<AuditActionTypeEnum> {
    return new FieldGroupOption({
      I18NLabel: 'audit.actionType.' + actionType,
      value: actionType,
    });
  }
}
