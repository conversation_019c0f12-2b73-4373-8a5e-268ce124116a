import { Observable, zip as observableZip } from 'rxjs';
import { TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslationService } from '@app/security/translation.service';
import _ from 'lodash';

export class CustomTranslateLoader implements TranslateLoader {
  constructor(
    private http: HttpClient,
    private prefix: string,
    private suffix: string,
    private translationService: TranslationService,
  ) {}

  getTranslation(lang: string): Observable<any> {
    return new Observable((subscriber) => {
      observableZip(
        this.http.get('' + this.prefix + lang + this.suffix),
        this.translationService.getAll(lang),
      ).subscribe(
        (value) => subscriber.next(_.merge(value[0], value[1])),
        (error) => subscriber.error(error),
        () => subscriber.complete(),
      );
    });
  }
}
