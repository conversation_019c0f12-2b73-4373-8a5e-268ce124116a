package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciPagerAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.Person;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPersonWithPagerAndAlertGroups;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.service.person.IPersonBusinessService;
import lu.fujitsu.ts.cgdis.portal.core.service.rici.IRiciAlertGroupROBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.AbstractAuditAspect;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.rici.RiciPersonPagerUpdateAlertGroupForm;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Audit aspect for RICI Pager Alert Group update operations.
 */
@Component
public class RiciPagerAlertGroupUpdateAuditAspect extends AbstractAuditAspect<AuditRiciPagerAlertGroup, RiciPersonWithPagerAndAlertGroups, RiciPersonWithPagerAndAlertGroups> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RiciPagerAlertGroupUpdateAuditAspect.class);

    private final IPersonBusinessService personBusinessService;
    private final IRiciAlertGroupROBusinessService alertGroupROBusinessService;

    @Autowired
    public RiciPagerAlertGroupUpdateAuditAspect(
            IPersonBusinessService personBusinessService,
            IRiciAlertGroupROBusinessService alertGroupROBusinessService) {
        super(AuditActionType.UPDATE, AuditRiciPagerAlertGroup.class);
        this.personBusinessService = personBusinessService;
        this.alertGroupROBusinessService = alertGroupROBusinessService;
    }

    @Override
    protected void populateAuditBeforeProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciPagerAlertGroup audit, Map<String, Object> parameters) {
        RiciPersonPagerUpdateAlertGroupForm form = (RiciPersonPagerUpdateAlertGroupForm) parameters.get("form");
        if (form != null) {
            try {
                // Get person information
                Person person = personBusinessService.get(
                        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW),
                        form.getPersonTecid()
                );
                if (person != null) {
                    audit.setPersonTecid(person.getTecid());
                    audit.setPersonName(person.getFirstName() + " " + person.getLastName());
                    audit.setPersonCgdisRegistrationNumber(person.getCgdisRegistrationNumber());
                }

                // Get alert group information
                RiciAlertGroup alertGroup = alertGroupROBusinessService.get(
                        new CGDISSecurityConstraint(Permission.ROLE_PERMISSION_RICI_PAGER_VIEW),
                        form.getAlertGroupTecid()
                );
                if (alertGroup != null) {
                    audit.setAlertGroupTecid(alertGroup.getTecid());
                    audit.setAlertGroupName(alertGroup.getName());
                    audit.setAlertGroupDescription(alertGroup.getDescription());
                    
                    if (alertGroup.getRiciRicSchema() != null) {
                        audit.setRiciRicSchemaTecid(alertGroup.getRiciRicSchema().getTecid());
                        audit.setRiciRicSchemaAlias(alertGroup.getRiciRicSchema().getSchemaAlias());
                    }
                    
                    if (alertGroup.getRiciRicRange() != null) {
                        audit.setRiciRicRangeTecid(alertGroup.getRiciRicRange().getTecid());
                        audit.setRiciRicRangeName(alertGroup.getRiciRicRange().getName());
                    }
                }

                audit.setAlertGroupValue(form.getAlertGroupValue());

            } catch (Exception e) {
                LOGGER.warn("Could not fetch details for audit before processing: {}", e.getMessage());
            }
        } else {
            LOGGER.warn("RiciPersonPagerUpdateAlertGroupForm parameter 'form' not found, cannot populate audit before processing.");
        }
    }

    @Override
    protected void populateAuditAfterProcessingMethod(ProceedingJoinPoint point, Method method, AuditRiciPagerAlertGroup audit, Map<String, Object> parameters, RiciPersonWithPagerAndAlertGroups result) {
        if (result != null && result.getActivePager() != null) {
            audit.setPagerTecid(result.getActivePager().getTecid());
            audit.setPagerPagerId(result.getActivePager().getPagerId());
            audit.setPagerSerialNumber(result.getActivePager().getSerialNumber());
        }
    }

    @Override
    protected boolean mustCreateAuditLog(AuditRiciPagerAlertGroup audit, Map<String, Object> parameters, RiciPersonWithPagerAndAlertGroups result) {
        return result != null;
    }

    @Override
    protected Long getTecidFromResult(RiciPersonWithPagerAndAlertGroups result) {
        return result != null ? result.getTecid() : null;
    }
}
