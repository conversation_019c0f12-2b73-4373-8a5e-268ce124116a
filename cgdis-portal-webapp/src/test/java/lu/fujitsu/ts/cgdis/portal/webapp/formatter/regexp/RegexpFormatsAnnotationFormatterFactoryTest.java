package lu.fujitsu.ts.cgdis.portal.webapp.formatter.regexp;

import lu.fujitsu.ts.cgdis.portal.webapp.annotation.format.RegexpFormat;
import lu.fujitsu.ts.cgdis.portal.webapp.annotation.format.RegexpFormats;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.format.Parser;

import java.lang.annotation.Annotation;
import java.text.ParseException;

import static org.junit.jupiter.api.Assertions.*;

class RegexpFormatsAnnotationFormatterFactoryTest {

  @BeforeEach
  void setUp() {
  }


  @Test
  void testGetFieldTypes() {
    RegexpFormatsAnnotationFormatterFactory factory = new RegexpFormatsAnnotationFormatterFactory();
    assertTrue(factory.getFieldTypes().contains(String.class));
  }

  @Test
  void testGetPrinter() {
    RegexpFormatsAnnotationFormatterFactory factory = new RegexpFormatsAnnotationFormatterFactory();
    assertThrows(UnsupportedOperationException.class, () -> factory.getPrinter(null, null));
  }

  @Test
  void testGetParser() throws ParseException {
    RegexpFormatsAnnotationFormatterFactory factory = new RegexpFormatsAnnotationFormatterFactory();
    RegexpFormats formats = new RegexpFormats() {
      @Override
      public Class<? extends Annotation> annotationType() {
        return RegexpFormats.class;
      }

      @Override
      public RegexpFormat[] value() {
        return new RegexpFormat[] {
          new RegexpFormat() {
            @Override
            public Class<? extends Annotation> annotationType() {
              return RegexpFormat.class;
            }

            @Override
            public String regexp() {
              return "a";
            }

            @Override
            public String replacementValue() {
              return "b";
            }
          },
          new RegexpFormat() {
            @Override
            public Class<? extends Annotation> annotationType() {
              return RegexpFormat.class;
            }

            @Override
            public String regexp() {
              return "b";
            }

            @Override
            public String replacementValue() {
              return "c";
            }
          },
          new RegexpFormat() {
            @Override
            public Class<? extends Annotation> annotationType() {
              return RegexpFormat.class;
            }

            @Override
            public String regexp() {
              return "c";
            }

            @Override
            public String replacementValue() {
              return "d";
            }
          }
        };
      }
    };

    Parser<String> parser = factory.getParser(formats, String.class);
    assertEquals("ddd", parser.parse("aaa", null));
  }
}
