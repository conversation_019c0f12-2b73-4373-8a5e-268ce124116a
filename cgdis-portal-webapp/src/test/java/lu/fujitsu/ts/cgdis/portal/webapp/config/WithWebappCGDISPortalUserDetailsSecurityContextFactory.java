package lu.fujitsu.ts.cgdis.portal.webapp.config;

import lu.fujitsu.ts.cgdis.portal.core.domain.CGDISPortalUserDetails;
import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.security.Role;
import lu.fujitsu.ts.eportal.server.security.model.EPortalAuthority;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.test.context.support.WithSecurityContextFactory;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;


final class WithWebappCGDISPortalUserDetailsSecurityContextFactory implements WithSecurityContextFactory<WithWebappCGDISPortalUser> {

    @Autowired
    public WithWebappCGDISPortalUserDetailsSecurityContextFactory() {

    }

    @Override
    public SecurityContext createSecurityContext(WithWebappCGDISPortalUser withUser) {
        String username = withUser.username();
        Assert.hasLength(username, "value() must be non-empty String");
        CGDISPortalUserDetails principal = new CGDISPortalUserDetails();
        principal.setTecid(withUser.tecid());
        principal.setUsername(withUser.username());
        setAuthorities(withUser, principal);
        TestingAuthenticationToken authentication = new TestingAuthenticationToken(principal, principal.getUsername(), new ArrayList<>(principal.getAuthorities()));
        authentication.setAuthenticated(true);
        authentication.setDetails(principal);
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authentication);
        return context;
    }

    private void setAuthorities(WithWebappCGDISPortalUser withUser, CGDISPortalUserDetails principal) {

        Map<String, EPortalAuthority> roleAuthorities;
        Map<String, EPortalAuthority> permissionAuthorities;


        if (withUser.allAuthorities() || ArrayUtils.isNotEmpty(withUser.authoritiesToRemove())) {
            Set<Role> rolesToRemove = Arrays.stream(withUser.authoritiesToRemove()).flatMap(testAuthority -> Arrays.stream(testAuthority.roles()))
                    .collect(Collectors.toSet());
            Set<Permission> permissionsToRemove = Arrays.stream(withUser.authoritiesToRemove()).flatMap(testAuthority -> Arrays.stream(testAuthority.permisions()))
                    .collect(Collectors.toSet());


            roleAuthorities = Arrays.stream(Role.values())
                    .filter(role -> !rolesToRemove.contains(role))
                    .collect(Collectors.toMap(
                            role -> role.name(),
                            o -> {
                                HashSet<String> domains = new HashSet<>();
                                domains.add("domain1");
                                domains.add("domain2");
                                domains.add("domain3");
                                domains.add("domain4");
                                return new EPortalAuthority(o.name(), domains );
                            }));
            permissionAuthorities = Arrays.stream(Permission.values())
                    .filter(permission -> !permissionsToRemove.contains(permission))
                    .collect(Collectors.toMap(
                            permission -> permission.name(),
                            o -> {
                                HashSet<String> domains = new HashSet<>();
                                domains.add("domain1");
                                domains.add("domain2");
                                domains.add("domain3");
                                domains.add("domain4");
                                return new EPortalAuthority(o.name(), domains );
                            }));



        } else if (ArrayUtils.isNotEmpty(withUser.authorities())) {
            roleAuthorities = new HashMap<>();
            permissionAuthorities = new HashMap<>();

            Arrays.stream(withUser.authorities()).forEach(authority -> {
                if (ArrayUtils.isNotEmpty(authority.roles())) {
                    roleAuthorities.putAll(Arrays.stream(authority.roles()).collect(Collectors.toMap(
                            role -> role.name(), role -> new EPortalAuthority(role.name(), Arrays.stream(authority.domains()).collect(Collectors.toSet())))));
                }
                if (ArrayUtils.isNotEmpty(authority.permisions())) {
                    permissionAuthorities.putAll(Arrays.stream(authority.permisions()).collect(Collectors.toMap(
                            permission -> permission.name(), permission -> new EPortalAuthority(permission.name(), Arrays.stream(authority.domains()).collect(Collectors.toSet())))));
                }


            });

        } else {
            roleAuthorities = new HashMap<>();
            permissionAuthorities = new HashMap<>();
        }

        permissionAuthorities.putAll(roleAuthorities);
        principal.setAuthorities(permissionAuthorities);

        Set<String> allDomains =
                permissionAuthorities.values().stream().map(EPortalAuthority::getDomains)
                .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
    principal.setEntities(allDomains);

    }
}
