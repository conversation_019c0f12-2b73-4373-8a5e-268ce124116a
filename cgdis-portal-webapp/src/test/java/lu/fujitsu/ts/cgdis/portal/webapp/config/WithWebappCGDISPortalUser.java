package lu.fujitsu.ts.cgdis.portal.webapp.config;

import org.springframework.security.test.context.support.WithSecurityContext;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;


/**
 * The interface With business cgdis portal user.
 */
@Retention(RetentionPolicy.RUNTIME)
@WithSecurityContext(factory = WithWebappCGDISPortalUserDetailsSecurityContextFactory.class)
public @interface WithWebappCGDISPortalUser {
    /**
     * Tecid long.
     *
     * @return the long
     */
    String username() default "cgdis_user";

    /**
     * Tecid long.
     *
     * @return the long
     */
    long tecid() default 1;

    /**
     * Use these authorities fir this user
     *
     * @return the class
     */
    TestAuthority[] authorities() default {}  ;

    /**
     * Authorities to remove from all authorities
     * BE careful: property allAuthorities is automatically set to true
     *
     * @return the test authority [ ]
     */
    TestAuthority[] authoritiesToRemove() default {}  ;


    /**
     * User has all roles and permissions for all entities
     *
     * @return the boolean
     */
    boolean allAuthorities() default false;

}
