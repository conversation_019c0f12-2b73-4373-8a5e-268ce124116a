package lu.fujitsu.ts.cgdis.portal.webapp.config;

import lu.fujitsu.ts.cgdis.portal.core.security.Permission;
import lu.fujitsu.ts.cgdis.portal.core.security.Role;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * The interface Test authorities.
 */
@Retention(RetentionPolicy.RUNTIME)
public @interface TestAuthority {

    /**
     * Authority string.
     *
     * @return the string
     */
    Permission[] permisions() default {};

    /**
     * Roles role [ ].
     *
     * @return the role [ ]
     */
    Role[] roles()  default {};

    /**
     * Domains string [ ].
     *
     * @return the string [ ]
     */
    String[] domains() default {} ;


}
