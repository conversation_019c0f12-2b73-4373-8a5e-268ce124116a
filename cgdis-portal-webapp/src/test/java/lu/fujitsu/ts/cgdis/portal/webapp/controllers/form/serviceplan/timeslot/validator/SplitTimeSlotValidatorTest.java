package lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.serviceplan.timeslot.validator;

import lu.fujitsu.ts.cgdis.portal.core.domain.serviceplan.timeslot.ServicePlanTimeSlot;
import lu.fujitsu.ts.cgdis.portal.core.service.serviceplan.IServicePlanVersionTimeSlotBusinessService;
import lu.fujitsu.ts.cgdis.portal.webapp.config.WithWebappCGDISPortalUser;
import lu.fujitsu.ts.cgdis.portal.webapp.controllers.form.serviceplan.timeslot.SplitServicePlanTimeSlotForm;
import lu.fujitsu.ts.eportal.server.core.exceptions.NotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.BDDMockito;
import org.mockito.Captor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;

@ExtendWith(SpringExtension.class)
@WithWebappCGDISPortalUser
class SplitTimeSlotValidatorTest {

  private SplitTimeSlotValidator validator;

  @MockBean
  private ConstraintValidatorContext context;

  @MockBean
  private ConstraintValidatorContext.ConstraintViolationBuilder defaultConstraintValidationBuilder;

  @Captor
  private ArgumentCaptor<String> constraintViolationCaptor;

  @Captor
  private ArgumentCaptor<String> propertyNodeCaptor;

  @MockBean
  private IServicePlanVersionTimeSlotBusinessService timeSlotBusinessService;

  @MockBean
  private ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext defaultNodeBuilderCustomizable;

  @BeforeEach
  void setUp() {
    given(context.buildConstraintViolationWithTemplate(constraintViolationCaptor.capture())).willReturn(defaultConstraintValidationBuilder);
    given(defaultConstraintValidationBuilder.addPropertyNode(propertyNodeCaptor.capture())).willReturn(defaultNodeBuilderCustomizable);
    given(defaultNodeBuilderCustomizable.addConstraintViolation()).willReturn(context);
    validator = new SplitTimeSlotValidator(timeSlotBusinessService);
  }

  @Test
  void givenSlot__WhenSplitInPast_ThenReturnNotValid() throws NotFoundException {
    // GIVEN

    LocalDateTime now = LocalDateTime.now();
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(11, 0))
      .date(LocalDate.now().minusDays(2)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(10, 0))
      .endTime(LocalTime.of(12, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.inpast");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }


  @Test
  void givenSlotOnOneDay__WhenSplit_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(11, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(10, 0))
      .endTime(LocalTime.of(12, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isTrue();


  }

  @Test
  void givenSlotOnOneDay__WhenSplitBeforeStart_ThenReturnNotValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(9, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(10, 0))
      .endTime(LocalTime.of(12, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");

  }

  @Test
  void givenSlotOnOneDay__WhenSplitAtStart_ThenReturnNotValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(10, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(10, 0))
      .endTime(LocalTime.of(12, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

  @Test
  void givenSlotOnOneDay__WhenSplitAtEnd_ThenReturnNotValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(12, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(10, 0))
      .endTime(LocalTime.of(12, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

  @Test
  void givenSlotOnOneDay__WhenSplitAfterEnd_ThenReturnNotValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(13, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(10, 0))
      .endTime(LocalTime.of(12, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

  @Test
  void givenSlotOnTwoDays__WhenSplitBeforeMidnight_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(23, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isTrue();


  }


  @Test
  void givenSlotOnTwoDays__WhenSplitAfterMidnight_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(4, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isTrue();


  }

  @Test
  void givenSlotOnTwoDays__WhenSplitAtMidnight_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(0, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isTrue();


  }

  @Test
  void givenSlotOnTwoDays__WhenSplitBeforeStart_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(21, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

  @Test
  void givenSlotOnTwoDays__WhenSplitAtStart_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(22, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

  @Test
  void givenSlotOnTwoDays__WhenSplitAtEnd_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(6, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

  @Test
  void givenSlotOnTwoDays__WhenSplitAfterEnd_ThenReturnValid() throws NotFoundException {
    // GIVEN
    SplitServicePlanTimeSlotForm form = SplitServicePlanTimeSlotForm.builder()
      .slotTecid(123L)
      .splitTime(LocalTime.of(7, 0))
      .date(LocalDate.now().plusDays(1)) // There is Check on date > now
      .build();

    ServicePlanTimeSlot slot = ServicePlanTimeSlot.builder()
      .startTime(LocalTime.of(22, 0))
      .endTime(LocalTime.of(6, 0))
      .build();

    BDDMockito.given(timeSlotBusinessService.get(any(), eq(123L))).willReturn(slot);

    // WHEN
    boolean valid = validator.isValid(form, context);

    // THEN
    assertThat(valid).isFalse();
    assertThat(constraintViolationCaptor.getValue()).isEqualTo("service-plan.split.notinslotrange");
    assertThat(propertyNodeCaptor.getValue()).isEqualTo("splitTime");


  }

}
