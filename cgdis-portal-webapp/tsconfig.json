{"compilerOptions": {"target": "es2022", "allowJs": true, "module": "ES2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "sourceMap": true, "experimentalDecorators": true, "lib": ["es2022", "dom"], "baseUrl": "src/main/webapp", "paths": {"@app/*": ["app/*"], "@env/*": ["environments/*"], "@permamonitor/*": ["app/permamonitor/*"], "@rici/*": ["app/rici/*"]}, "noImplicitAny": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "exclude": ["node_modules/**/*", "target/**/*", "node/**/*", "ng-update-v6.js", "protractor.conf.js", "proxy-fujitsubas.conf.js", "proxy-mobile.conf.js", "proxy.conf.js", "src/main/webapp/app/version.js", "src/main/webapp/app/rici/views/sim/sim-create/popup_example/**/*"], "angularCompilerOptions": {"preserveWhitespaces": "false"}}