{"compilerOptions": {"target": "es5", "module": "commonjs", "moduleResolution": "node", "sourceMap": false, "experimentalDecorators": true, "removeComments": false, "noImplicitAny": false, "outDir": "target/web/app", "lib": ["es2015", "dom"], "typeRoots": ["node_modules/@types"], "baseUrl": "src/main/webapp", "paths": {"@app/*": ["app/*"], "@env/*": ["environments/*"]}}, "files": ["src/main/webapp/app/app.module.ts", "src/main/webapp/app/main.ts", "src/main/webapp/app/polyfills.ts", "src/main/webapp/app/vendor.ts"], "angularCompilerOptions": {"genDir": "target/aot", "entryModule": "src/main/webapp/app.module#AppModule", "skipMetadataEmit": true}}