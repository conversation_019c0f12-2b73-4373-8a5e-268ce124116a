/*
 *
 * Copyright (c) 2012-2017 Fujitsu Technology Solutions, Luxembourg
 * You may not use this file except in compliance with the License granted by Fujitsu to your company.
 * A copy of the License is available upon request by sending an <NAME_EMAIL>
 * Software distributed under this License is provided under an "AS IS" BASIS,
 * WITHOUT ANY SUPPORT, WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and limitations under the License.
 */

const PROXY_CONFIG = [
  {
    context: [
      '/oauth2',
      '/mobile',
      '/cgdis/api',
      '/manage',
      '/h2-console',
      '/swagger-resources',
      '/v2/api-docs',
      '/login'
    ],
    target: "http://localhost:8080",
    secure: false
  }
];

module.exports = PROXY_CONFIG;
