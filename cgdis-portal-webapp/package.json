{"name": "CGDISPortal", "version": "0.0.1", "description": "CGDIS Portal", "scripts": {"cleanup": "rimraf target/{aot,web,webaot}", "clean-web": "rimraf target/web/app/{src,target/}", "start": "ng serve  --poll 2000 --verbose  --proxy-config proxy.conf.js --port 9000", "start-no-verbose": "ng serve  --poll 2000 --proxy-config proxy.conf.js --port 9000", "startjc": "ng serve  --poll 2000 --host cgdis.portal   --proxy-config proxy.conf.js --port 9100 ", "startjcmobile": "ng serve --build-optimizer false  --poll 2000 --host 0.0.0.0 --progress=true  --proxy-config proxy-mobile.conf.js --port 9001 --env mobile", "start-no-reload": "ng serve --live-reload false --proxy-config proxy.conf.js --port 9000", "start-public": "ng serve --proxy-config proxy.conf.js --port 9000 --host 0.0.0.0", "start-fujitsubas": "ng serve --proxy-config proxy-fujitsubas.conf.js --port 9000", "serve": "ng serve --proxy-config proxy.conf.js", "serveaot": "ng serve --aot --configuration production --sourcemaps --build-optimizer false --proxy-config proxy.conf.js --port 9000", "test": "karma start src/test/javascript/karma.conf.js", "test:watch": "npm test --watch", "ngc": "ngc -p tsconfig-aot.json", "angularcli:prod": "npm run cleanup && npm run angularcli:prod:main && npm run clean-web", "angularcli:prod:main": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng build --configuration production  --aot --build-optimizer false", "angularcli:mobile:test": "npm run cleanup && npm run angularcli:mobile:test:main && npm run clean-web", "angularcli:mobile:test:main": "node --max_old_space_size=8192 'node_modules/@angular/cli/bin/ng' build --configuration production --environment=mobile  --aot --build-optimizer false --base-href ./ ", "angularcli:mobile:dev": "npm run cleanup && npm run angularcli:mobile:dev:main && npm run clean-web", "angularcli:mobile:dev:main": "node --max_old_space_size=8192 'node_modules/@angular/cli/bin/ng' build --configuration production --environment=mobile  --aot --build-optimizer false --base-href ./ ", "angularcli:mobile:prod": "npm run cleanup && npm run angularcli:mobile:prod:main && npm run clean-web", "angularcli:mobile:prod:main": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng build --configuration production --environment=mobile  --aot --build-optimizer false --base-href ./ ", "release-dry": "standard-version --dry-run", "release-alpha": "standard-version --prerelease alpha", "release": "standard-version", "compodoc": "./node_modules/.bin/compodoc -p tsconfig.json", "prepare": "node -e \"console.log('APP_VERSION = \\'' + process.env.npm_package_version +'\\';')\" > src/main/webapp/app/version.js", "lint": "ng lint --quiet", "lint:fix": "ng lint --fix"}, "engines": {"node": ">=6.9.0"}, "keywords": [], "author": "Fujitsu Technology Solutions Luxembourg", "license": "UNLICENSED", "dependencies": {"@angular/animations": "~17.3.12", "@angular/cdk": "~17.3.10", "@angular/common": "17.3.12", "@angular/compiler": "17.3.12", "@angular/core": "17.3.12", "@angular/forms": "17.3.12", "@angular/localize": "~17.3.12", "@angular/material": "^17.3.10", "@angular/platform-browser": "17.3.12", "@angular/platform-browser-dynamic": "17.3.12", "@angular/router": "17.3.12", "@ckeditor/ckeditor5-angular": "~8.0.0", "@eportal/components": "~3.177.0-alpha.3", "@eportal/core": "~3.173.0", "@iplab/ngx-file-upload": "~17.1.0", "@ng-bootstrap/ng-bootstrap": "~16.0.0", "@ngx-translate/core": "~15.0.0", "@ngx-translate/http-loader": "~8.0.0", "@popperjs/core": "~2.11.6", "@swimlane/ngx-datatable": "~20.1.0", "@typescript-eslint/types": "~8.9.0", "@viablelogic/ngx-responsive": "~11.0.2", "angularx-qrcode": "~17.0.1", "autoprefixer": "^8.0.0", "bootstrap": "4.0.0", "bootstrap-datepicker": "~1.9.0", "ckeditor5": "~43.1.0", "classlist.js": "^1.1.20150312", "core-js": "~2.6.4", "cronstrue": "^1.58.0", "date-holidays": "^1.6.2", "escape-string-regexp": "~1.0.5", "file-saver": "~2.0.5", "file-saver-es": "~2.0.5", "font-awesome": "^4.7.0", "fullcalendar": "3.9.0", "glob": "~10.4.2", "highcharts": "~11.4.8", "highcharts-angular": "~4.0.1", "install": "~0.12.2", "jquery": "3.3.1", "lodash": "4.17.20", "lodash.isequal": "~4.5.0", "magnific-popup": "^1.1.0", "modernizr": "^3.5.0", "moment": "~2.30.1", "moment-es6": "~1.0.0", "ng2-nouislider": "~2.0.0", "ngx-cron-editor": "~0.10.1", "ngx-device-detector": "~7.0.0", "ngx-loading": "~17.0.0", "ngx-owl-carousel-o": "~17.0.0", "ngx-scrollbar": "~16.0.0", "ngx-select-ex": "~8.0.1", "ngx-toastr": "~19.0.0", "ngx-trim-directive": "~3.0.1", "ngx-webstorage": "~13.0.1", "nouislider": "~15.8.1", "npm": "~10.8.2", "qtip2": "^3.0.3", "rxjs": "~7.8.1", "select2": "4.0.5", "svgo": "^0.7.2", "swagger-ui": "2.2.10", "web-animations-js": "^2.3.1", "zone.js": "~0.14.7"}, "devDependencies": {"@angular-devkit/architect": "~0.1703.11", "@angular-devkit/build-angular": "~17.3.11", "@angular-devkit/core": "~17.3.11", "@angular-devkit/schematics": "~17.3.11", "@angular-eslint/builder": "^17", "@angular-eslint/eslint-plugin": "^17", "@angular-eslint/eslint-plugin-template": "^17", "@angular-eslint/schematics": "^17", "@angular-eslint/template-parser": "^17", "@angular/cli": "~17.3.10", "@angular/compiler-cli": "17.3.12", "@compodoc/compodoc": "^1.0.4", "@schematics/angular": "~17.3.8", "@types/bootstrap": "^3.3.37", "@types/file-saver": "^1.3.0", "@types/geojson": "7946.0.4", "@types/jasmine": "2.5.53", "@types/jquery": "3.3.31", "@types/lodash": "~4.14.179", "@types/node": "~14.18.63", "@types/resize-observer-browser": "~0.1.11", "@typescript-eslint/eslint-plugin": "7.11.0", "@typescript-eslint/parser": "7.11.0", "angular2-template-loader": "0.6.2", "browser-sync": "3.0.2", "browserslist": "~4.23.1", "chalk": "~2.4.2", "codelyzer": "~6.0.2", "eslint": "^8.57.0", "eslint-config-prettier": "~9.1.0", "eslint-plugin-prettier": "~5.2.1", "events": "~3.3.0", "istanbul-instrumenter-loader": "^2.0.0", "jasmine-core": "2.7.0", "karma": "6.4.3", "karma-chrome-launcher": "2.2.0", "karma-coverage": "1.1.1", "karma-coverage-istanbul-reporter": "^1.2.1", "karma-intl-shim": "1.0.3", "karma-jasmine": "1.1.0", "karma-jasmine-html-reporter": "^0.2.2", "karma-junit-reporter": "1.2.0", "karma-notify-reporter": "1.0.1", "karma-remap-istanbul": "0.6.0", "karma-sourcemap-loader": "0.3.7", "prettier": "~3.3.3", "prettier-eslint": "~16.3.0", "prettier-plugin-css-order": "~2.1.2", "rimraf": "2.6.1", "standard-version": "^4.2.0", "tslib": "~1.10.0", "tslint": "~6.1.3", "tslint-loader": "3.5.3", "typescript": "5.4.5"}, "repository": {}, "volta": {"node": "18.20.2"}}