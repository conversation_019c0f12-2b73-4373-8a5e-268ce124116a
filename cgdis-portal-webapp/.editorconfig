# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# http://editorconfig.org
root = true

[*]
charset = utf-8
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true
end_of_line = lf


[*.md]
max_line_length = 0
trim_trailing_whitespace = false

# Indentation override
#[lib/**.js]
#[{package.json,.travis.yml}]
#[**/**.js]
