{"$schema": "./node_modules/@angular-devkit/core/src/workspace/workspace-schema.json", "version": 1, "newProjectRoot": "src", "cli": {"analytics": false}, "projects": {"undefined": {"root": "src/main/webapp", "projectType": "application", "prefix": "cgdis-portal", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "target/classes/public/", "main": "src/main/webapp/app/main.ts", "tsConfig": "tsconfig.json", "serviceWorker": false, "stylePreprocessorOptions": {"includePaths": ["src/main/webapp/sass"]}, "index": "src/main/webapp/index.html", "assets": [{"glob": "**/*", "input": "src/main/webapp/content", "output": "content"}, {"glob": "favicon.ico", "input": "src/main/webapp", "output": ""}, {"glob": "**/*", "input": "src/main/webapp/assets", "output": "assets"}, {"glob": "check.html", "input": "src/main/webapp", "output": ""}], "styles": ["node_modules/fullcalendar/dist/fullcalendar.min.css", "node_modules/qtip2/dist/jquery.qtip.min.css", "node_modules/ngx-toastr/toastr.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css", "node_modules/@swimlane/ngx-datatable/assets/icons.css", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/main/webapp/sass/main.scss"], "scripts": ["src/main/webapp/js/vendor/modernizr.min.js", "node_modules/jquery/dist/jquery.min.js", "node_modules/bootstrap/dist/js/bootstrap.js", "node_modules/bootstrap-datepicker/dist/js/bootstrap-datepicker.js", "node_modules/bootstrap-datepicker/dist/locales/bootstrap-datepicker.fr.min.js", "node_modules/magnific-popup/dist/jquery.magnific-popup.min.js", "node_modules/select2/dist/js/select2.min.js", "node_modules/select2/dist/js/i18n/fr.js", "node_modules/moment/min/moment.min.js", "node_modules/fullcalendar/dist/fullcalendar.min.js", "node_modules/fullcalendar/dist/locale-all.js", "node_modules/qtip2/dist/jquery.qtip.min.js"], "polyfills": "src/main/webapp/app/polyfills.ts", "aot": false, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"src": "src/main/webapp/environments/environment.ts", "replaceWith": "src/main/webapp/environments/environment.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "mobile": {"fileReplacements": [{"src": "src/main/webapp/environments/environment.ts", "replaceWith": "src/main/webapp/environments/environment.mobile.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "undefined:build"}, "configurations": {"production": {"buildTarget": "undefined:build:production"}, "development": {"buildTarget": "undefined:build:development"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "undefined:build"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/main/webapp/**/*.ts", "src/main/webapp/**/*.html"]}}}}}, "schematics": {"@schematics/angular:component": {"style": "scss", "changeDetection": "OnPush", "skipTests": true, "standalone": false}}}