{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "CGDIS Portal"}, "apps": [{"root": "src/main/webapp", "outDir": "target/classes/public/", "assets": ["content", "favicon.ico", "assets", "mockup", "check.html"], "index": "index.html", "main": "app/main.ts", "polyfills": "app/polyfills.ts", "prefix": "cgdis-portal", "test": "../../test/javascript/test.ts", "tsconfig": "../../../tsconfig.json", "mobile": false, "showCircularDependencies": true, "styles": ["../../../node_modules/fullcalendar/dist/fullcalendar.min.css", "../../../node_modules/qtip2/dist/jquery.qtip.min.css", "../../../node_modules/ng2-toastr/bundles/ng2-toastr.min.css", "../../../node_modules/bootstrap/dist/css/bootstrap.min.css", "../../../node_modules/font-awesome/css/font-awesome.min.css", "../../../node_modules/nouislider/distribute/nouislider.min.css", "../../../node_modules/@fujitsu/owl.carousel/dist/assets/owl.carousel.css", "../../../node_modules/@fujitsu/owl.carousel/dist/assets/owl.theme.default.css", "../../../node_modules/@swimlane/ngx-datatable/release/assets/icons.css", "../../../node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "sass/main.scss"], "stylePreprocessorOptions": {"includePaths": ["sass"]}, "scripts": ["./js/vendor/modernizr.min.js", "../../../node_modules/jquery/dist/jquery.min.js", "../../../node_modules/bootstrap/dist/js/bootstrap.js", "../../../node_modules/bootstrap-datepicker/dist/js/bootstrap-datepicker.js", "../../../node_modules/bootstrap-datepicker/dist/locales/bootstrap-datepicker.fr.min.js", "../../../node_modules/magnific-popup/dist/jquery.magnific-popup.min.js", "../../../node_modules/select2/dist/js/select2.min.js", "../../../node_modules/select2/dist/js/i18n/fr.js", "../../../node_modules/@fujitsu/owl.carousel/dist/owl.carousel.min.js", "../../../node_modules/moment/min/moment.min.js", "../../../node_modules/fullcalendar/dist/fullcalendar.min.js", "../../../node_modules/fullcalendar/dist/locale-all.js", "../../../node_modules/qtip2/dist/jquery.qtip.min.js", "../../../node_modules/hammerjs/hammer.min.js"], "environmentSource": "environments/environment.ts", "environments": {"dev": "environments/environment.ts", "prod": "environments/environment.ts", "mobile": "environments/environment.mobile.ts"}}], "lint": [{"project": "../../../tsconfig.json"}, {"project": "../../../tsconfig-aot.json"}], "test": {"karma": {"config": "src/test/javascript/karma.conf.js"}}, "defaults": {"styleExt": "sass", "prefixInterfaces": false, "component": {"changeDetection": "OnPush", "inlineStyle": true, "inlineTemplate": false, "spec": false}, "directive": {"spec": false}, "guard": {"spec": false}, "pipe": {"spec": false}, "service": {"spec": false}}, "packageManager": "npm"}