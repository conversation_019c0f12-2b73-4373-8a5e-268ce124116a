######################
# Project Specific
######################
/src/main/webapp/**/*.js
/src/main/webapp/**/*.d.ts
/src/main/webapp/**/*.js.map
/src/test/javascript/spec/**/*.js
/src/test/javascript/spec/**/*.d.ts
/src/test/javascript/spec/**/*.js.map
database-model/*.sql.cleaned

######################
# Node
######################
/node/
node_tmp/
node_modules/
npm-debug.log.*

######################
# SASS
######################
.sass-cache/

######################
# Eclipse
######################
*.pydevproject
.project
.metadata
tmp/
tmp/**/*
*.tmp
*.bak
*.swp
*~.nib
local.properties
.classpath
.settings/
.loadpath
.factorypath
**/src/main/resources/rebel.xml

# External tool builders
.externalToolBuilders/**

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# PDT-specific
.buildpath

######################
# Intellij
######################
.idea/
*.iml
*.iws
*.ipr
*.ids
*.orig

######################
# Visual Studio Code
######################
.vscode/

######################
# Maven
######################
/log/
/*/target/
/target/

######################
# Gradle
######################
.gradle/
/build/

######################
# Package Files
######################
*.jar
*.war
*.ear
*.db

######################
# Windows
######################
# Windows image file caches
Thumbs.db

# Folder config file
Desktop.ini

######################
# Mac OSX
######################
.DS_Store
.svn

# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

######################
# Directories
######################
/bin/
/deploy/

######################
# Logs
######################
*.log


######################
# Others
######################
*.class
*.*~
*~
.merge_file*

######################
# Gradle Wrapper
######################
!gradle/wrapper/gradle-wrapper.jar

######################
# Maven Wrapper
######################
!.mvn/wrapper/maven-wrapper.jar

######################
# ESLint
######################
.eslintcache

##############################
database-model/cmd.sed
database-model/*.cleaned

######################
# Custom Files
######################
**/src/main/resources/logback-local.xml
**/src/main/resources/application-local.yml
**/src/main/resources/local/**/*
/cgdis-portal-gateway/src/main/resources/rebel.xml


**/*.puml
/cgdis-portal-webapp/.angular/

.aider*

