package lu.fujitsu.ts.cgdis.portal.connector.portaldb.converters;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciSimCardDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciSimCardImportDbo; // Import new DBO
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciPagerImportDbo; // Import new DBO
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.*;
import lu.fujitsu.ts.cgdis.portal.core.exception.CGDISTechnicalException;
import lu.fujitsu.ts.eportal.server.core.utils.EPortalConverter;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Audit dbo 2 model converter.
 */
@Service
public class AuditDbo2ModelConverter extends EPortalConverter<AuditDbo, Audit> {

    /**
     * The Model class.
     */
    private Map<AuditType, Class<? extends Audit>> modelClass;

    /**
     * The Mapper.
     */
    private Mapper theMapper;

    /**
     * Instantiates a new Audit dbo 2 model converter.
     *
     * @param mapper the mapper
     */
    @Autowired
    @Lazy
    public AuditDbo2ModelConverter(Mapper mapper) {
        super(AuditDbo.class, Audit.class, mapper);
        this.theMapper = mapper;
        modelClass = new HashMap<>();
        modelClass.put(AuditType.PRESTATION, AuditPrestation.class);
        modelClass.put(AuditType.PDS, AuditServicePlan.class);
        modelClass.put(AuditType.MODEL, AuditModel.class);
        modelClass.put(AuditType.VERSION_MODEL, AuditModelVersion.class);
        modelClass.put(AuditType.VERSION_PDS, AuditServicePlanVersion.class);
        modelClass.put(AuditType.COPY_PRESTATION, AuditCopyPrestation.class);
        modelClass.put(AuditType.SLOT, AuditTimeSlot.class);
        modelClass.put(AuditType.LOGAS, AuditLogas.class);
        modelClass.put(AuditType.ALLOWANCE_CONFIG, AuditAllowanceConfiguration.class);
        modelClass.put(AuditType.PERM_DEPLOYMENT_PLAN, AuditPermDeploymentPlan.class);
        modelClass.put(AuditType.PERM_SERVICE_PLAN, AuditPermServicePlanCategory.class);
        modelClass.put(AuditType.PERM_CONFIG_DPCE, AuditPermConfigDpce.class);
        modelClass.put(AuditType.PERM_CONFIGDPCE_COPY, AuditPermConfigDpceCopy.class);
        modelClass.put(AuditType.SECURITY_ROLE, AuditSecurityRole.class);
        modelClass.put(AuditType.RICI_SIM_CARD, AuditRiciSimCard.class);
        modelClass.put(AuditType.RICI_RIC_RANGE, AuditRiciRicRange.class);
        modelClass.put(AuditType.RICI_PAGER, AuditRiciPager.class);
        modelClass.put(AuditType.RICI_ALERT_GROUP, AuditRiciAlertGroup.class);
        modelClass.put(AuditType.RICI_SCHEMA, AuditRiciSchema.class); // Add RICI_SCHEMA mapping
        // Add new import audit types
        modelClass.put(AuditType.RICI_SIM_CARD_IMPORT, AuditRiciSimCardImport.class);
        modelClass.put(AuditType.RICI_PAGER_IMPORT, AuditRiciPagerImport.class);
        modelClass.put(AuditType.RICI_PAGER_ALERT_GROUP, AuditRiciPagerAlertGroup.class);
    }

    @Override
    public Audit convert(AuditDbo audit) {
        Class<? extends Audit> destinationClass = modelClass.get(audit.getType());
        if (destinationClass == null) {
            throw new CGDISTechnicalException("Please provide the target MODEL class mapping for the audit type: " + audit.getType());
        }
        return theMapper.map(audit, destinationClass);
    }
}
