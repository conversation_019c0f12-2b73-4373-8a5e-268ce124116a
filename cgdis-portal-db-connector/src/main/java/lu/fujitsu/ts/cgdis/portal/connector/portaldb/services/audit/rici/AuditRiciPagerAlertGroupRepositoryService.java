package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit.rici;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditRiciPagerAlertGroupDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.IAuditRiciPagerAlertGroupRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit.AbstractAuditRepositoryService;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciPagerAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.eportal.server.core.domain.audit.IAuditSecurityConstraint;
import lu.fujitsu.ts.eportal.server.core.repository.specification.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import org.dozer.Mapper;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Repository service for RICI Pager Alert Group audit operations.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.AUDIT)
public class AuditRiciPagerAlertGroupRepositoryService extends AbstractAuditRepositoryService<AuditRiciPagerAlertGroup, AuditRiciPagerAlertGroupDbo, IAuditRiciPagerAlertGroupRepository> {

    public AuditRiciPagerAlertGroupRepositoryService(
            IAuditRiciPagerAlertGroupRepository repository,
            AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditRiciPagerAlertGroupDbo> specBuilder,
            Mapper mapper) {
        super(repository, specBuilder, AuditRiciPagerAlertGroupDbo.class, AuditRiciPagerAlertGroup.class, mapper);
    }

    @Override
    protected void additionalSortBuilder(Map<String, String> sorts) {
        // Add any additional sorting logic if needed
    }

    @Override
    public AuditType getAuditType() {
        return AuditType.RICI_PAGER_ALERT_GROUP;
    }
}
