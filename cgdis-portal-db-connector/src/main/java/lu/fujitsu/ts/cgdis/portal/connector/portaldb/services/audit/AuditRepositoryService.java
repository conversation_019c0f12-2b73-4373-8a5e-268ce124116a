package lu.fujitsu.ts.cgdis.portal.connector.portaldb.services.audit;

import lu.fujitsu.ts.cgdis.portal.connector.portaldb.converters.AuditDbo2ModelConverter;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.converters.AuditModel2DboConverter;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit.AuditPermServicePlanCategoryDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.permamonitor.PermDeploymentPlanDbo;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.repositories.audit.IAuditRepository;
import lu.fujitsu.ts.cgdis.portal.connector.portaldb.specifications.impl.serviceplans.AuditServicePlanSecurityConstraintSpecification;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.Audit;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingCategory;
import lu.fujitsu.ts.cgdis.portal.core.logging.LoggingDomain;
import lu.fujitsu.ts.cgdis.portal.core.security.IAuditSecurityConstraint;
import lu.fujitsu.ts.cgdis.portal.services.audit.IAuditRepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.services.SecuredRORepositoryService;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.SpecificationUtils;
import lu.fujitsu.ts.eportal.server.connector.jpa.specifications.secured.AbstractComposedSecuredSpecificationBuilder;
import lu.fujitsu.ts.eportal.server.core.domain.Fetches;
import lu.fujitsu.ts.eportal.server.core.domain.SearchCriterion;
import lu.fujitsu.ts.eportal.server.core.services.impl.sort.MappingSortBuilder;
import lu.fujitsu.ts.eportal.server.logging.milestones.LogMilestone;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableType;
import lu.fujitsu.ts.eportal.server.logging.milestones.LoggableValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Path;
import java.util.*;

/**
 * The type Volunteer availability repository service.
 */
@Service
@LogMilestone(category = LoggingCategory.REPOSITORY_SERVICE, domain = LoggingDomain.AUDIT)
public class AuditRepositoryService extends SecuredRORepositoryService<IAuditSecurityConstraint, Audit, AuditDbo, Long, IAuditRepository> implements IAuditRepositoryService<Audit> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuditRepositoryService.class);


    /**
     * Instantiates a new Volunteer availability repository service.
     *
     * @param repository  the repository
     * @param specBuilder the spec builder
     * @param mapper      the mapper
     */
    @Autowired
    public AuditRepositoryService(IAuditRepository repository,
                                  AbstractComposedSecuredSpecificationBuilder<IAuditSecurityConstraint, AuditDbo> specBuilder,
                                  AuditModel2DboConverter auditModel2DboConverter,
                                  AuditDbo2ModelConverter auditDbo2ModelConverter
    ) {
        super("Audit", auditModel2DboConverter, auditDbo2ModelConverter, repository, specBuilder, new Fetches[]{});
        Map<String, String> sorts = new HashMap<>();
        sorts.put("serviceplan", "servicePlans.portalLabel");
        sorts.put("permServicePlanDeploymentPlanName", mergeSortMappingAttributes(AuditPermServicePlanCategoryDbo.Fields.DEPLOYMENT_PLAN, PermDeploymentPlanDbo.Fields.NAME));
        sorts.put("permServicePlanName", AuditPermServicePlanCategoryDbo.Fields.SERVICE_PLAN_NAME);
        sorts.put("permServicePlanCategoryName", AuditPermServicePlanCategoryDbo.Fields.CATEGORY_NAME);
        sorts.put("permServicePlanSubcategoryName", AuditPermServicePlanCategoryDbo.Fields.SUBCATEGORY_NAME);
        this.setSortBuilder(new MappingSortBuilder(sorts));
    }

    private String mergeSortMappingAttributes(String... fields) {
        StringJoiner joiner = new StringJoiner(".");
        for (String field : fields) {
            joiner.add(field);
        }
        return joiner.toString();
    }

    @Override
    public Page<Audit> search(IAuditSecurityConstraint constraint, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        return null;
    }

    @Override
    public AuditType getAuditType() {
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    @LogMilestone(action = "search audits")
    public Page<Audit> search(IAuditSecurityConstraint constraint, @LoggableValue(type = LoggableType.COLLECTION) Collection<AuditType> types, Collection<SearchCriterion> criteria, PageRequest pageRequest) {
        Page<AuditDbo> result = repository.findAll(
                SpecificationUtils.and(
                        specBuilder.build(constraint, criteria),
                        buildSecurityConstraint(constraint, types)
                ),
                buildPageRequest(pageRequest));
        if (result == null) {
            return new PageImpl<>(new ArrayList<>(), pageRequest, 0);
        } else {
            return result.map(dbo2ModelConverter::convert);
        }
    }


    private Specification<AuditDbo> buildSecurityConstraint(IAuditSecurityConstraint constraint, Collection<AuditType> types) {
        return (root, query, criteriaBuilder) -> {

            Path<Object> type = root.get("type");
            return criteriaBuilder.and(
                    type.in(types),
                    criteriaBuilder.or(
                            type.in(Arrays.asList(AuditType.MODEL, AuditType.VERSION_MODEL, AuditType.LOGAS, AuditType.ALLOWANCE_CONFIG,
                                    AuditType.PERM_DEPLOYMENT_PLAN, AuditType.PERM_SERVICE_PLAN, AuditType.PERM_CONFIG_DPCE, AuditType.PERM_CONFIGDPCE_COPY,
                                    AuditType.RICI_SIM_CARD_IMPORT, AuditType.RICI_PAGER_IMPORT, // Added new import audit types
                                    AuditType.RICI_SIM_CARD, AuditType.RICI_RIC_RANGE, AuditType.RICI_PAGER, AuditType.RICI_SCHEMA, AuditType.RICI_ALERT_GROUP, AuditType.RICI_PAGER_ALERT_GROUP)), // Added RICI_PAGER_ALERT_GROUP
                            new AuditServicePlanSecurityConstraintSpecification(constraint).toPredicate(root, query, criteriaBuilder)
                    )

            );

        };
    }


}
