package lu.fujitsu.ts.cgdis.portal.connector.portaldb.dbos.audit;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;

/**
 * Database object for RICI Pager Alert Group audit records.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "AUDIT_RICI_PAGER_ALERT_GROUP")
@DiscriminatorValue(AuditRiciPagerAlertGroupDbo.TYPE_RICI_PAGER_ALERT_GROUP)
@FieldNameConstants
public class AuditRiciPagerAlertGroupDbo extends AuditDbo {

    /**
     * The discriminator value for RICI Pager Alert Group audit records.
     */
    public static final String TYPE_RICI_PAGER_ALERT_GROUP = "RICI_PAGER_ALERT_GROUP";

    /**
     * Column name constants
     */
    public static final String PERSON_TECID_COLUMN = "PERSON_TECID";
    public static final String PERSON_NAME_COLUMN = "PERSON_NAME";
    public static final String PERSON_CGDIS_REGISTRATION_NUMBER_COLUMN = "PERSON_CGDIS_REGISTRATION_NUMBER";
    public static final String ALERT_GROUP_TECID_COLUMN = "ALERT_GROUP_TECID";
    public static final String ALERT_GROUP_NAME_COLUMN = "ALERT_GROUP_NAME";
    public static final String ALERT_GROUP_DESCRIPTION_COLUMN = "ALERT_GROUP_DESCRIPTION";
    public static final String RICI_RIC_SCHEMA_TECID_COLUMN = "RICI_RIC_SCHEMA_TECID";
    public static final String RICI_RIC_SCHEMA_ALIAS_COLUMN = "RICI_RIC_SCHEMA_ALIAS";
    public static final String RICI_RIC_RANGE_TECID_COLUMN = "RICI_RIC_RANGE_TECID";
    public static final String RICI_RIC_RANGE_NAME_COLUMN = "RICI_RIC_RANGE_NAME";
    public static final String ALERT_GROUP_VALUE_COLUMN = "ALERT_GROUP_VALUE";
    public static final String PAGER_TECID_COLUMN = "PAGER_TECID";
    public static final String PAGER_PAGER_ID_COLUMN = "PAGER_PAGER_ID";
    public static final String PAGER_SERIAL_NUMBER_COLUMN = "PAGER_SERIAL_NUMBER";

    @Column(name = PERSON_TECID_COLUMN)
    private Long personTecid;

    @Column(name = PERSON_NAME_COLUMN)
    private String personName;

    @Column(name = PERSON_CGDIS_REGISTRATION_NUMBER_COLUMN)
    private String personCgdisRegistrationNumber;

    @Column(name = ALERT_GROUP_TECID_COLUMN)
    private Long alertGroupTecid;

    @Column(name = ALERT_GROUP_NAME_COLUMN)
    private String alertGroupName;

    @Column(name = ALERT_GROUP_DESCRIPTION_COLUMN)
    private String alertGroupDescription;

    @Column(name = RICI_RIC_SCHEMA_TECID_COLUMN)
    private Long riciRicSchemaTecid;

    @Column(name = RICI_RIC_SCHEMA_ALIAS_COLUMN)
    private String riciRicSchemaAlias;

    @Column(name = RICI_RIC_RANGE_TECID_COLUMN)
    private Long riciRicRangeTecid;

    @Column(name = RICI_RIC_RANGE_NAME_COLUMN)
    private String riciRicRangeName;

    @Column(name = ALERT_GROUP_VALUE_COLUMN)
    private Boolean alertGroupValue;

    @Column(name = PAGER_TECID_COLUMN)
    private Long pagerTecid;

    @Column(name = PAGER_PAGER_ID_COLUMN)
    private String pagerPagerId;

    @Column(name = PAGER_SERIAL_NUMBER_COLUMN)
    private String pagerSerialNumber;
}
