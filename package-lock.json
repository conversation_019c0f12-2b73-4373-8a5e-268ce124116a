{"name": "portal", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@angular/material": "~19.1.5"}}, "node_modules/@angular/animations": {"version": "19.1.7", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/animations/-/animations-19.1.7.tgz", "integrity": "sha512-EnyQTCNc1nWnjc5V3HPlClpJIS2R2XAfIEUCyI3lE4FLQxcXyhIsM9NmacAZT3Ai8RL+8JVCttPmBZXMpjP6Ug==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"@angular/core": "19.1.7"}}, "node_modules/@angular/animations/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD", "peer": true}, "node_modules/@angular/cdk": {"version": "19.1.5", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/cdk/-/cdk-19.1.5.tgz", "integrity": "sha512-+g20LIvYHThKBD6oXTYWVL6+ecaOWtPJu08R5xIfGrwXoj0l/9prLwuSW8GlIATI3mDkSesyhQsomb9jAUzKwQ==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "optionalDependencies": {"parse5": "^7.1.2"}, "peerDependencies": {"@angular/common": "^19.0.0 || ^20.0.0", "@angular/core": "^19.0.0 || ^20.0.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/cdk/node_modules/parse5": {"version": "7.2.1", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/parse5/-/parse5-7.2.1.tgz", "integrity": "sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==", "license": "MIT", "optional": true, "peer": true, "dependencies": {"entities": "^4.5.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/@angular/cdk/node_modules/parse5/node_modules/entities": {"version": "4.5.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "peer": true, "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/@angular/cdk/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD", "peer": true}, "node_modules/@angular/common": {"version": "19.1.7", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/common/-/common-19.1.7.tgz", "integrity": "sha512-MXfUGfWeesTQ12HXgeoVIXsS+r1jZxT2FkLQtqS+NRsRD4T1vlyvD7kTI+Ku1NAjdt3mB8TJ0cZHubvmml8I+Q==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"@angular/core": "19.1.7", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/common/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD", "peer": true}, "node_modules/@angular/core": {"version": "19.1.7", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/core/-/core-19.1.7.tgz", "integrity": "sha512-P+e4ekJYWMFhWSzJav0R51bFAfUhIOmnqmG9mlI/ZONu2qcTTmyIG9AW5x1qhrMHEH42RaeK60RkKyqgcHaGDg==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"rxjs": "^6.5.3 || ^7.4.0", "zone.js": "~0.15.0"}}, "node_modules/@angular/core/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD", "peer": true}, "node_modules/@angular/forms": {"version": "19.1.7", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/forms/-/forms-19.1.7.tgz", "integrity": "sha512-GVOCwqIXTpZt+bE3cqkasqpEs5n/aVq04yXLgM+mvVEbmAMibZYpzfg8NARlXCH3zveqhOSTJgsllfbbb7sdDw==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"@angular/common": "19.1.7", "@angular/core": "19.1.7", "@angular/platform-browser": "19.1.7", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/forms/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD", "peer": true}, "node_modules/@angular/material": {"version": "19.1.5", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/material/-/material-19.1.5.tgz", "integrity": "sha512-Fi04Toe+z0qorfpZkQ5rIRE9cVgBOHdgCig5oFrHpycSDW2LMTrvZtSV/qMwrIe5GPn49EXE2jCGcSpgumW4KA==", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/animations": "^19.0.0 || ^20.0.0", "@angular/cdk": "19.1.5", "@angular/common": "^19.0.0 || ^20.0.0", "@angular/core": "^19.0.0 || ^20.0.0", "@angular/forms": "^19.0.0 || ^20.0.0", "@angular/platform-browser": "^19.0.0 || ^20.0.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/material/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD"}, "node_modules/@angular/platform-browser": {"version": "19.1.7", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/@angular/platform-browser/-/platform-browser-19.1.7.tgz", "integrity": "sha512-QKakWl+CeVVwn22yjRHBXm6BvDsHoo+9u1pJGGk2smKSYjHW6qAly28+P7FUfVXUQI7rg++M66JwzNOFfYMDQA==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"@angular/animations": "19.1.7", "@angular/common": "19.1.7", "@angular/core": "19.1.7"}, "peerDependenciesMeta": {"@angular/animations": {"optional": true}}}, "node_modules/@angular/platform-browser/node_modules/tslib": {"version": "2.7.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz", "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "license": "0BSD", "peer": true}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "license": "Apache-2.0", "peer": true, "dependencies": {"tslib": "^2.1.0"}}, "node_modules/rxjs/node_modules/tslib": {"version": "2.8.1", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD", "peer": true}, "node_modules/zone.js": {"version": "0.15.0", "resolved": "https://nexus.cgdis.lu/repository/npm-proxy/zone.js/-/zone.js-0.15.0.tgz", "integrity": "sha512-9oxn0IIjbCZkJ67L+LkhYWRyAy7axphb3VgE2MBDlOqnmHMPWGYMxJxBYFueFq/JGY2GMwS0rU+UCLunEmy5UA==", "license": "MIT", "peer": true}}}