package lu.fujitsu.ts.cgdis.portal.core.domain.audit;

public enum AuditType {
    PRESTATION,
    MODEL,
    PDS,
    COPY_PRESTATION,
    SLOT,
    LOGAS,
    VERSION_MODEL,
    <PERSON><PERSON><PERSON><PERSON>CE_CONFIG,
    VERSION_PDS,
    PERM_DEPLOYMENT_PLAN,
    PERM_SERVICE_PLAN,
    PERM_CONFIG_DPCE,
    PERM_CONFIGDPCE_COPY,
    SECURITY_ROLE,
    RICI_SIM_CARD,
    RICI_RIC_RANGE,
    RICI_PAGER,
    // New dedicated audit types for CSV imports
    RICI_SIM_CARD_IMPORT,
    RICI_PAGER_IMPORT,
    RICI_SCHEMA,
    RICI_ALERT_GROUP;
}
