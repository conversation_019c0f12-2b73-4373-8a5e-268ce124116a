# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@angular/animations@^19.0.0 || ^20.0.0", "@angular/animations@19.1.7":
  version "19.1.7"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/animations/-/animations-19.1.7.tgz"
  integrity sha512-EnyQTCNc1nWnjc5V3HPlClpJIS2R2XAfIEUCyI3lE4FLQxcXyhIsM9NmacAZT3Ai8RL+8JVCttPmBZXMpjP6Ug==
  dependencies:
    tslib "^2.3.0"

"@angular/cdk@19.1.5":
  version "19.1.5"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/cdk/-/cdk-19.1.5.tgz"
  integrity sha512-+g20LIvYHThKBD6oXTYWVL6+ecaOWtPJu08R5xIfGrwXoj0l/9prLwuSW8GlIATI3mDkSesyhQsomb9jAUzKwQ==
  dependencies:
    tslib "^2.3.0"
  optionalDependencies:
    parse5 "^7.1.2"

"@angular/common@^19.0.0 || ^20.0.0", "@angular/common@19.1.7":
  version "19.1.7"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/common/-/common-19.1.7.tgz"
  integrity sha512-MXfUGfWeesTQ12HXgeoVIXsS+r1jZxT2FkLQtqS+NRsRD4T1vlyvD7kTI+Ku1NAjdt3mB8TJ0cZHubvmml8I+Q==
  dependencies:
    tslib "^2.3.0"

"@angular/core@^19.0.0 || ^20.0.0", "@angular/core@19.1.7":
  version "19.1.7"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/core/-/core-19.1.7.tgz"
  integrity sha512-P+e4ekJYWMFhWSzJav0R51bFAfUhIOmnqmG9mlI/ZONu2qcTTmyIG9AW5x1qhrMHEH42RaeK60RkKyqgcHaGDg==
  dependencies:
    tslib "^2.3.0"

"@angular/forms@^19.0.0 || ^20.0.0":
  version "19.1.7"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/forms/-/forms-19.1.7.tgz"
  integrity sha512-GVOCwqIXTpZt+bE3cqkasqpEs5n/aVq04yXLgM+mvVEbmAMibZYpzfg8NARlXCH3zveqhOSTJgsllfbbb7sdDw==
  dependencies:
    tslib "^2.3.0"

"@angular/material@~19.1.5":
  version "19.1.5"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/material/-/material-19.1.5.tgz"
  integrity sha512-Fi04Toe+z0qorfpZkQ5rIRE9cVgBOHdgCig5oFrHpycSDW2LMTrvZtSV/qMwrIe5GPn49EXE2jCGcSpgumW4KA==
  dependencies:
    tslib "^2.3.0"

"@angular/platform-browser@^19.0.0 || ^20.0.0", "@angular/platform-browser@19.1.7":
  version "19.1.7"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/@angular/platform-browser/-/platform-browser-19.1.7.tgz"
  integrity sha512-QKakWl+CeVVwn22yjRHBXm6BvDsHoo+9u1pJGGk2smKSYjHW6qAly28+P7FUfVXUQI7rg++M66JwzNOFfYMDQA==
  dependencies:
    tslib "^2.3.0"

entities@^4.5.0:
  version "4.5.0"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

parse5@^7.1.2:
  version "7.2.1"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/parse5/-/parse5-7.2.1.tgz"
  integrity sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==
  dependencies:
    entities "^4.5.0"

"rxjs@^6.5.3 || ^7.4.0":
  version "7.8.2"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/rxjs/-/rxjs-7.8.2.tgz"
  integrity sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==
  dependencies:
    tslib "^2.1.0"

tslib@^2.1.0:
  version "2.8.1"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tslib@^2.3.0:
  version "2.7.0"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/tslib/-/tslib-2.7.0.tgz"
  integrity sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==

zone.js@~0.15.0:
  version "0.15.0"
  resolved "https://nexus.cgdis.lu/repository/npm-proxy/zone.js/-/zone.js-0.15.0.tgz"
  integrity sha512-9oxn0IIjbCZkJ67L+LkhYWRyAy7axphb3VgE2MBDlOqnmHMPWGYMxJxBYFueFq/JGY2GMwS0rU+UCLunEmy5UA==
