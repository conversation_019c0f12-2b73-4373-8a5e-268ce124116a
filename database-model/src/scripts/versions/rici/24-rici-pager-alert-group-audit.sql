CREATE TABLE IF NOT EXISTS `cgdis_portal_db`.`AUDIT_RICI_PAGER_ALERT_GROUP`
(
    `TECID`                             BIGINT(20)   NOT NULL COMMENT 'FK to AUDIT.TECID',
    `PERSON_TECID`                      BIGINT(20)   NULL COMMENT 'TECID of the person',
    `PERSON_NAME`                       VARCHAR(255) NULL COMMENT 'Name of the person at the time of audit',
    `PERSON_CGDIS_REGISTRATION_NUMBER`  VARCHAR(50)  NULL COMMENT 'CGDIS registration number of the person',
    `ALERT_GROUP_TECID`                 BIGINT(20)   NULL COMMENT 'TECID of the alert group',
    `ALERT_GROUP_NAME`                  VARCHAR(255) NULL COMMENT 'Name of the alert group at the time of audit',
    `ALERT_GROUP_DESCRIPTION`           VARCHAR(255) NULL COMMENT 'Description of the alert group',
    `RICI_RIC_SCHEMA_TECID`             BIGINT(20)   NULL COMMENT 'TECID of the RIC schema',
    `RICI_RIC_SCHEMA_ALIAS`             VARCHAR(255) NULL COMMENT 'Alias of the RIC schema',
    `RICI_RIC_RANGE_TECID`              BIGINT(20)   NULL COMMENT 'TECID of the RIC range',
    `RICI_RIC_RANGE_NAME`               VARCHAR(255) NULL COMMENT 'Name of the RIC range',
    `ALERT_GROUP_VALUE`                 BOOLEAN      NULL COMMENT 'Whether the person is assigned to the alert group (true) or removed (false)',
    `PAGER_TECID`                       BIGINT(20)   NULL COMMENT 'TECID of the pager',
    `PAGER_PAGER_ID`                    VARCHAR(50)  NULL COMMENT 'Pager ID',
    `PAGER_SERIAL_NUMBER`               VARCHAR(50)  NULL COMMENT 'Pager serial number',
    PRIMARY KEY (`TECID`),
    CONSTRAINT `FK_AUDIT_RICI_PAGER_ALERT_GROUP_AUDIT` FOREIGN KEY (`TECID`) REFERENCES `AUDIT` (`TECID`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  COMMENT = 'Audit table for RICI Pager Alert Group operations';
